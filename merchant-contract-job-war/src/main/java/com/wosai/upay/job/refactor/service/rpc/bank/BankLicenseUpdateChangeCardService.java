package com.wosai.upay.job.refactor.service.rpc.bank;

import java.util.Map;

/**
 * merchant-bank-service LicenseUpdateChangeCardService
 */
public interface BankLicenseUpdateChangeCardService {

    Integer SAME_NAME = 1;

    Integer DIFF_NAME = 2;

    /**
     * 通知换卡成功
     *
     * @param merchantId 商户号
     * @param changeType 换卡类型 1-同名换卡 2-异名换卡
     */
    void notifyChangeCardSuccess(String merchantId, Integer changeType, Map<String, Object> newBank, boolean sameAccountType, Map<String, Object> source);
}
