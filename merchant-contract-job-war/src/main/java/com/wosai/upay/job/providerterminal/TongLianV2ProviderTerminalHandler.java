package com.wosai.upay.job.providerterminal;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.enume.ProviderTerminalBindLevel;
import com.wosai.upay.job.enume.ProviderTerminalIdTypeEnum;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.providerterminal.model.ProviderTerminalAddContext;
import com.wosai.upay.job.util.ScaleConverterUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Component
public class TongLianV2ProviderTerminalHandler extends AbstractProviderTerminalHandler {

    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private TongLianV2Service tongLianV2Service;

    @Override
    protected List<Integer> getSupportPaywayList() {
        // 通联收银宝接口文档中查询终端状态有云闪付相关信息，但是实际上没有返回
        return ALIPAY_WEIXIN_LIST;
    }

    @Override
    public ProviderEnum getProvider() {
        return ProviderEnum.PROVIDER_TONGLIAN_V2;
    }

    @Override
    protected String contractProviderTerminalId(ProviderTerminalAddContext request) {
        ProviderTerminalIdTypeEnum type;
        if (request.getBindLevel() == ProviderTerminalBindLevel.MERCHANT) {
            type = isProdEnvironment ?
                    ProviderTerminalIdTypeEnum.MERCHANT_PREFIX :
                    ProviderTerminalIdTypeEnum.MERCHANT_TEST_PREFIX;
        } else if (request.getBindLevel() == ProviderTerminalBindLevel.STORE) {
            type = isProdEnvironment ?
                    ProviderTerminalIdTypeEnum.STORE_PREFIX :
                    ProviderTerminalIdTypeEnum.STORE_TEST_PREFIX;
        } else {
            type = isProdEnvironment ?
                    ProviderTerminalIdTypeEnum.TERMINAL_PREFIX :
                    ProviderTerminalIdTypeEnum.TERMINAL_TEST_PREFIX;
        }

        String prefix = type.getCode();
        String nextTerminalId = ScaleConverterUtil.encode36(Long.parseLong(snGenerator.nextProviderTerminalId()));
        String providerTerminalId = StringUtils.isNotBlank(prefix) ? prefix + nextTerminalId : nextTerminalId;

        AddTermInfoDTO addTermInfoDTO = new AddTermInfoDTO();
        addTermInfoDTO.setDeviceId(providerTerminalId);
        addTermInfoDTO.setSubMchId(request.getAcquirerMerchantInfo().getAcquirerMerchantId());
        addTermInfoDTO.setMerchantSn(request.getMerchantSn());

        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByPayMchId(request.getAcquirerMerchantInfo().getAcquirerMerchantId(), TongLianV2Param.class);
        ContractResponse response = tongLianV2Service.addTerm(addTermInfoDTO, tongLianV2Param);
        if (!response.isSuccess()) {
            throw new ContractBizException("通联收银宝终端采集信息失败:" + request.getMerchantSn() + ":" + response.getMessage());
        }
        return providerTerminalId;
    }
}
