package com.wosai.upay.job.payactivity;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.payactivity.alipay.AlipayActivityProcessor;
import com.wosai.upay.job.service.payactivity.AlipayActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/7/29 11:39 上午
 */
@Component
@AutoJsonRpcServiceImpl
@Slf4j
public class AlipayActivityServiceImpl implements AlipayActivityService {
    @Autowired
    private AlipayActivityProcessor alipayActivityProcessor;

    @Override
    public CommonResult createTaskApply(String merchantSn, Long auditId, int activityType, Map formBody) {

        return alipayActivityProcessor.getHandleClass(activityType).createTaskApply(merchantSn, auditId, activityType, formBody);
    }


    /**
     * 审批拒绝
     *
     * @param merchantSn   商户号
     * @param auditId      审批ID
     * @param activityType 活动类型
     * @param formBody
     * @return
     */
    @Override
    public CommonResult auditReject(String merchantSn, Long auditId, int activityType, Map formBody) {
        return alipayActivityProcessor.getHandleClass(activityType).auditReject(merchantSn, auditId, activityType, formBody);

    }

    /**
     * 审批通过
     *
     * @param merchantSn   商户号
     * @param auditId      审批ID
     * @param activityType 活动类型
     * @param formBody
     * @return
     */
    @Override
    public CommonResult auditApprove(String merchantSn, Long auditId, int activityType, Map formBody) {
        return alipayActivityProcessor.getHandleClass(activityType).auditApprove(merchantSn, auditId, activityType, formBody);
    }
}
