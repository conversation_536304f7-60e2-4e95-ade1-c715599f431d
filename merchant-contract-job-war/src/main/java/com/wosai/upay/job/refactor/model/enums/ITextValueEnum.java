package com.wosai.upay.job.refactor.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 文本信息接口
 *
 * <AUTHOR>
 */
public interface ITextValueEnum {

    /**
     * 获取枚举文本
     *
     * @return 获取枚举文本信息
     */
    String getText();

    /**
     * 获取枚举值
     *
     * @return 获取枚举值
     */
    int getValue();

    /**
     * 获取所有大于0的枚举值
     *
     * @param iTextValueEnumClass 枚举类型
     * @param values              枚举值
     * @param <T>                 返回类型
     * @return 返回所有大于0的枚举值
     */
    static <T extends ITextValueEnum> ITextValueEnum[] getEnums(Class<T> iTextValueEnumClass, int values) {
        return Arrays.stream(iTextValueEnumClass.getEnumConstants())
                .filter(x -> (values & x.getValue()) > 0).toArray(ITextValueEnum[]::new);
    }

    /**
     * 获取所有枚举值
     *
     * @param iTextValueEnumClass 枚举类型
     * @param <T>                 返回类型
     * @return 返回所有枚举值
     */
    static <T extends ITextValueEnum> T[] getAllEnums(Class<T> iTextValueEnumClass) {
        return iTextValueEnumClass.getEnumConstants();
    }

    /**
     * 获取所有大于0的枚举值
     *
     * @param iTextValueEnumClass 枚举类型
     * @param values              枚举值
     * @param <T>                 返回类型
     * @return 返回所有大于0的枚举值
     */
    static <T extends ITextValueEnum> List<T> listEnums(Class<T> iTextValueEnumClass, int values) {
        return Arrays.stream(iTextValueEnumClass.getEnumConstants())
                .filter(x -> (values & x.getValue()) > 0).collect(Collectors.toList());
    }

    /**
     * 获取所有枚举值
     *
     * @param iTextValueEnumClass 枚举类型
     * @param <T>                 返回类型
     * @return 返回所有枚举值
     */
    static <T extends ITextValueEnum> List<T> listAllEnums(Class<T> iTextValueEnumClass) {
        return Arrays.asList(iTextValueEnumClass.getEnumConstants());
    }


    /**
     * 判断枚举值是否包含当前传入value
     *
     * @param iTextValueEnumClass iTextValueEnumClass
     * @param value               value
     * @return 返回所有的枚举值
     */
    static <T extends ITextValueEnum> boolean containsEnum(Class<T> iTextValueEnumClass, int value) {
        return Arrays.stream(iTextValueEnumClass.getEnumConstants()).
                anyMatch(iTextValueEnum -> Objects.equals(iTextValueEnum.getValue(), value));
    }

}
