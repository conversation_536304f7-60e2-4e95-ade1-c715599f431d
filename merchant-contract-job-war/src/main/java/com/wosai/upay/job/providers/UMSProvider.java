package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.ChinaUmsParam;
import com.wosai.upay.merchant.contract.service.ChinaUmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

import static com.wosai.upay.job.handlers.ContractSubTaskHandler.RESULT_CODE_SUCCESS;
import static com.wosai.upay.job.model.DO.MerchantProviderParams.PARAMS_CONFIG_STATUS_SUCCESS;

/**
 * @Description: 银联商务
 * @Date 2021/2/20 3:52 下午
 * @Created by 闫梦杰
 */
@Component(ProviderUtil.UMS_PROVIDER_CHANNEL)
@Slf4j
public class UMSProvider extends AbstractProvider {
    @Autowired
    private ChinaUmsService chinaUmsService;
    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private MerchantProviderParamsBiz merchantProviderParamsBiz;

    /**
     * 生成更新任务
     *
     * @param merchantSn
     * @param event
     * @param paramContext
     * @param contractRule
     * @return
     */
    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        // 非当前收单机构，更新不影响总状态
        int influPtask = AcquirerTypeEnum.UMS.getValue().equals(acquirer) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.UMS_PROVIDER_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);

        //判断更新类型
        Integer taskType = null;

        if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
            if (contractRule.getPayway() == null || PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
                Map requestParam = (Map) paramContext.get("cardRequestParam");
                if (!CollectionUtils.isEmpty(requestParam)) {
                    //银行卡管理服务发起的变更(merchant_bank_account_pre)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
                } else {
                    //dts订阅直接变更(merchant_bank_account)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                }
            }
        }
        if (taskType == null) {
            return null;
        }
        return subTask.setTask_type(taskType);

    }

    /**
     * 新增任务处理
     *
     * @param contractTask
     * @param contractChannel
     * @param sub
     * @return
     */
    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {

        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        ChinaUmsParam umsParam = buildParam(contractChannel, sub, ChinaUmsParam.class);
        //新增商户入网
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
            if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
                if (needPayFor(contextParam, sub, contractTask)) {
                    return null;
                }
                ContractResponse response = chinaUmsService.contractMerchant(contextParam, umsParam);
                String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
                updateClearProvider(merchantId, sub.getMerchant_sn());
                return response;
            } else {
                //其他支付类型：微信，支付宝，云闪付
                return chinaUmsService.contractMerchantOtherPayWay(sub, umsParam);
            }
        }
        //微信重新入网报备,微信商家实名认证
        if (ProviderUtil.CONTRACT_RECONTRACT.equals(contractTask.getType()) || ProviderUtil.CONTRACT_TYPE_AUTH.equals(contractTask.getType())) {
            return chinaUmsService.contractWeiXinWithParams(contextParam, sub, umsParam);
        }
        return null;
    }

    /**
     * 更新任务处理
     *
     * @param contractTask
     * @param contractChannel
     * @param sub
     * @return
     */
    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel
            contractChannel, ContractSubTask sub) {
        ChinaUmsParam umsParam = buildParam(contractChannel, sub, ChinaUmsParam.class);
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(contractTask.getType())) {
            if (needPayFor(contextParam, sub, contractTask)) {
                return null;
            }
        }
        return callRemote(contextParam, sub, umsParam);
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        ChinaUmsParam umsParam = contractParamsBiz.buildContractParamsByParams(merchantProviderParams, ChinaUmsParam.class);
        return chinaUmsService.wechatSubDevConfig(umsParam, weixinConfig);
    }

    /**
     * 切换交易参数
     *
     * @param merchantProviderParams
     * @param fee
     * @param sync
     * @param tradeAppId
     * @return
     */
    @Override
    public boolean changeTradeParams(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId) {
        // 银商 微信重新进件 设为默认 切换交易参数
        if (!merchantProviderParams.getMerchant_sn().equals(merchantProviderParams.getOut_merchant_sn())
                && merchantProviderParams.getPayway().equals(PaywayEnum.WEIXIN.getValue())) {
            //微信重新进件 设置交易参数
            reConfirm(merchantProviderParams);
            return true;
        } else {
            return super.changeTradeParams(merchantProviderParams, fee, sync, tradeAppId);
        }
    }


    private ContractResponse callRemote(Map contextParam, ContractSubTask contractSubTask, ChinaUmsParam umsParam) {

        String merchantSn = contractSubTask.getMerchant_sn();
        ContractResponse contractResponse = null;
        //只有更换银行卡类型
        if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(contractSubTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(contractSubTask.getTask_type())) {
            //pawy==0 的调用银行卡申请
            if (contractSubTask.getPayway().equals(PaywayEnum.ACQUIRER.getValue())) {
                contractResponse = chinaUmsService.updateMerchantBankAccount(merchantSn, contextParam, umsParam);
            } else {
                //其他支付类型 直接返回
                contractResponse = new ContractResponse();
                contractResponse.setCode(RESULT_CODE_SUCCESS);
                contractResponse.setMessage("银商更换银行卡成功");
            }
        }
        return contractResponse;
    }

    /**
     * 微信重新进件 设置默认
     *
     * @param merchantProviderParams
     */
    private void reConfirm(MerchantProviderParams merchantProviderParams) {
        ChinaUmsParam umsParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParams.getProvider()),
                merchantProviderParams.getPayway(), merchantProviderParams.getChannel_no(), ChinaUmsParam.class);
        //weixinMchId对应merchantProviderParams.getId()
        WeixinConfig configs = agentAppidBiz.getConfig(merchantProviderParams.getMerchant_sn(),
                merchantProviderParams.getProvider(), umsParam.getChannel_no(), merchantProviderParams.getId());
        //微信的重新进件 确认
        if (!chinaUmsService.reWeChatContractConfirm(configs, umsParam)) {
            log.error("微信重新进件切换参数失败：商户号:{},providerParamsId:{}"
                    , merchantProviderParams.getMerchant_sn(), merchantProviderParams.getId());
            throw new ContractBizException("微信重新进件切换参数失败：商户号:" + merchantProviderParams.getMerchant_sn() + ",providerParamsId:" + merchantProviderParams.getId());
        }
        //当前设置为使用中,支付目录微信子商户号配置成功
        merchantProviderParamsMapper.updateParamsConfigStatusById(UseStatusEnum.IN_USE.getValue(), System.currentTimeMillis(),PARAMS_CONFIG_STATUS_SUCCESS, merchantProviderParams.getId());

        //删除原来的配置
        MerchantProviderParamsExample merchantProviderParamsExample = new MerchantProviderParamsExample();
        merchantProviderParamsExample.or()
                .andMerchant_snEqualTo(merchantProviderParams.getMerchant_sn())
                .andPaywayEqualTo(merchantProviderParams.getPayway())
                .andRule_group_idEqualTo(merchantProviderParams.getRule_group_id())
                .andIdNotEqualTo(merchantProviderParams.getId())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> upList = merchantProviderParamsMapper.selectByExample(merchantProviderParamsExample);
        for (MerchantProviderParams params : upList) {
            merchantProviderParamsBiz.deleteParamsById(params.getId());
        }
    }
}
