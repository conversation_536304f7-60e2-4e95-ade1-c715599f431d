package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.model.entity.BnsMchDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.InactiveBnsMerchantMapper;
import com.wosai.upay.job.refactor.model.entity.InactiveBnsMerchantDO;

import javax.annotation.Resource;
import java.util.List;


/**
 * 不活跃bns商户表数据库访问层 {@link InactiveBnsMerchantDO}
 * 对InactiveBnsMerchantMapper层做出简单封装 {@link InactiveBnsMerchantMapper}
 *
 * <AUTHOR>
 */
@Repository
public class InactiveBnsMerchantDAO extends AbstractBaseDAO<InactiveBnsMerchantDO, InactiveBnsMerchantMapper>{

    protected InactiveBnsMerchantDAO(SqlSessionFactory sqlSessionFactory, InactiveBnsMerchantMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据主键起始id获取数据
     *
     * @param beginId   起始id
     * @param batchSize 批量大小
     * @return data
     */
    public List<InactiveBnsMerchantDO> listByBeginId(Long beginId, Integer batchSize) {
        LambdaQueryWrapper<InactiveBnsMerchantDO> query = new LambdaQueryWrapper<>();
        query.gt(InactiveBnsMerchantDO::getId, beginId).orderByAsc(InactiveBnsMerchantDO::getId);
        query.last("limit " + batchSize);
        return entityMapper.selectList(query);
    }
}
