package com.wosai.upay.job.refactor.task.license;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.utils.stream.ExtCollectors;
import com.wosai.upay.job.enume.ErrorMsgViewEndpointTypeEnum;
import com.wosai.upay.job.model.dto.response.ErrorInfoPromptTextRspDTO;
import com.wosai.upay.job.refactor.service.rpc.risk.req.RiskEntryResult;
import com.wosai.upay.job.service.ErrorCodeManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 营业执照更新错误信息提示转换器
 *
 * <AUTHOR>
 * @date 2025/3/7 07:38
 */
@Component
@Slf4j
public class LicenseUpgradeErrorMsgPromptConvertor {

    @Resource
    private ErrorCodeManageService errorCodeManageService;

    public static final String MICRO_UPGRADE_MSG_PLATFORM = "merchant-contract-job_micro_upgrade_platform";

    public static final String DEFAULT_MESSAGE = "营业执照认证失败，请联系客服处理问题";

    public static final String DEFAULT_CRM_MESSAGE = "营业执照认证失败，请联系销售支持，提交技术工单排查。";

    public String getUpgradeCheckFailPromptMessageMapJson(String originalFailReason, String defaultAppMessage) {
        return JSON.toJSONString(getUpgradeCheckFailPromptMessageMap(originalFailReason, defaultAppMessage));
    }

    public String getAppUpgradeCheckFailPromptMessage(String originalFailReason, String defaultAppMessage) {
        return MapUtils.getString(getUpgradeCheckFailPromptMessageMap(originalFailReason, defaultAppMessage), RiskEntryResult.APP, defaultAppMessage);
    }

    public Map<String, Object> getUpgradeCheckFailPromptMessageMap(String originalFailReason, String defaultAppMessage) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put(RiskEntryResult.APP, defaultAppMessage);
        messageMap.put(RiskEntryResult.CRM, originalFailReason);
        messageMap.put(RiskEntryResult.SPA, originalFailReason);
        if (StringUtils.isEmpty(originalFailReason)) {
            return messageMap;
        }
        List<ErrorInfoPromptTextRspDTO> promptMessages = null;
        try {
            promptMessages = errorCodeManageService.getAllEndPointPromptMessage(originalFailReason, MICRO_UPGRADE_MSG_PLATFORM);
        } catch (Exception e) {
            log.warn("获取营业执照认证失败提示信息失败, originalReason:{}", originalFailReason, e);
        }
        if (CollectionUtils.isEmpty(promptMessages)) {
            messageMap.put(RiskEntryResult.SPA, "请审核人员按照审核规则正常审核，并将案例反馈给业务管理员。失败原因：" + originalFailReason);
            messageMap.put(RiskEntryResult.APP, defaultAppMessage);
            return messageMap;
        }
        Map<ErrorMsgViewEndpointTypeEnum, String> endpointTypeEnumStringMap = promptMessages.stream()
                .collect(ExtCollectors.toMap(ErrorInfoPromptTextRspDTO::getErrorMsgViewEndpointTypeEnum, ErrorInfoPromptTextRspDTO::getPromptText, (t1, t2) -> t1));
        com.wosai.upay.job.refactor.utils.MapUtils.ifPresent(endpointTypeEnumStringMap, ErrorMsgViewEndpointTypeEnum.APP, t -> messageMap.put(RiskEntryResult.APP, t));
        com.wosai.upay.job.refactor.utils.MapUtils.ifPresent(endpointTypeEnumStringMap, ErrorMsgViewEndpointTypeEnum.CRM, t -> messageMap.put(RiskEntryResult.CRM, t));
        com.wosai.upay.job.refactor.utils.MapUtils.ifPresent(endpointTypeEnumStringMap, ErrorMsgViewEndpointTypeEnum.SPA, t -> messageMap.put(RiskEntryResult.SPA, t));
        return messageMap;
    }


}
