package com.wosai.upay.job.controller;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.model.callback.req.LuzhouAuditPassCallBack;
import com.wosai.upay.job.model.callback.req.LuzhouCallBackReqBasic;
import com.wosai.upay.job.model.callback.req.LuzhouNetInCallBack;
import com.wosai.upay.job.model.callback.res.LuzhouCallBackRes;
import com.wosai.upay.job.service.LuzhouCallBackService;
import com.wosai.upay.job.util.luzhou.BodySignBlock;
import com.wosai.upay.job.util.luzhou.ISignBlock;
import com.wosai.upay.job.util.luzhou.SMSaltSigner;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.provider.LuZhouParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/contract/lz")
public class LuzhouBankController {
    private static final String SMS4_KEY_IN_REDIS = "sm4key_lzb";

    private final LuzhouCallBackService luzhouCallBackService;
    private final SMSaltSigner signer;
    private final ContractParamsBiz baseBiz;
    private final RedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    private static final String SM2_PRIVATE_KEY = "18A6363C93870834E1C15285082E7F213108455D6F534748C127DEED824F551C";
    private static final String SM2_PUBLIC_KEY = "03BEE7223D047B5AA716DC452F58801F9CFC5128F663ACE7EA699489DE12C7D0EB";

    private final ThreadPoolTaskExecutor executor;

    public LuzhouBankController(LuzhouCallBackService luzhouCallBackService,
                                SMSaltSigner signer,
                                ContractParamsBiz baseBiz,
                                RedisTemplate redisTemplate,
                                ObjectMapper objectMapper,
                                @Qualifier("luzhouThreadPool") ThreadPoolTaskExecutor executor) {
        this.luzhouCallBackService = luzhouCallBackService;
        this.signer = signer;
        this.baseBiz = baseBiz;
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.executor = executor;
    }

    @PostMapping(value = "/auditCallback/unsigned", produces = "application/json")
    public LuzhouCallBackRes contractMerchantCallback(@Valid @RequestBody LuzhouCallBackReqBasic req, @RequestParam String privateKey) {
        log.info("泸州银行进件回调请求报文:{}", req);
        try {
            if (req instanceof LuzhouAuditPassCallBack) {
                return luzhouCallBackService.handleAuditPassCallBack((LuzhouAuditPassCallBack) req);
            } else if (req instanceof LuzhouNetInCallBack) {
                luzhouCallBackService.handleNetInCallBack((LuzhouNetInCallBack) req, privateKey);
            }
        } catch (Exception e) {
            log.error("处理泸州银行回调请求错误", e);
        }
        return LuzhouCallBackRes.success("publicKey");
    }

    @PostMapping(value = "/auditCallback", produces = "application/json")
    public LuzhouCallBackRes contractMerchantCallback(@RequestBody String content,
                                                      @RequestHeader("Time-Stamp") String timeStamp,
                                                      @RequestHeader("Mer-Id") String merId,
                                                      @RequestHeader("Sign") String retSign) {
        final String retStr = content;
        LuZhouParam luZhouParam = baseBiz.buildContractParams("lzb", LuZhouParam.class);
        // 当通道对数据进行解密和签名校验失败时，返回的是明文。
        log.info("泸州银行返回报文：{}", retStr);
        log.info("泸州银行返回签名sign值：{}", retSign);
        ISignBlock signBlock = new BodySignBlock("/contract/lz/auditCallback", merId, timeStamp, retStr);
        if (!signer.sign(signBlock, luZhouParam.getSm3key()).equals(retSign)) {
            log.error("泸州银行返回报文验签失败");
            return LuzhouCallBackRes.fail("泸州银行返回报文验签失败");
        } else {
            executor.submit(() -> {
                String secretKey = requestSm4Key();
                String decryptedRetStr = signer.decryptData(secretKey, retStr);
                log.info("泸州银行商户:{},sm2 公钥：{},sm2 私钥{} .泸州银行返回参数(解密)：{}", merId, SM2_PUBLIC_KEY, SM2_PRIVATE_KEY, decryptedRetStr);
                LuzhouCallBackReqBasic req;
                try {
                    req = objectMapper.readValue(decryptedRetStr, LuzhouCallBackReqBasic.class);
                } catch (JsonProcessingException e) {
                    throw new ContractBizException("反序列化泸州银行回调失败", e);
                }
                try {
                    if (req instanceof LuzhouAuditPassCallBack) {
                        luzhouCallBackService.handleAuditPassCallBack((LuzhouAuditPassCallBack) req);
                    } else if (req instanceof LuzhouNetInCallBack) {
                        luzhouCallBackService.handleNetInCallBack((LuzhouNetInCallBack) req, SM2_PRIVATE_KEY);
                    }
                } catch (Exception e) {
                    log.error("处理泸州银行回调请求错误", e);
                }
            });
            return LuzhouCallBackRes.success(SM2_PUBLIC_KEY);
        }
    }

    private String requestSm4Key() {
        Object o = redisTemplate.opsForValue().get(SMS4_KEY_IN_REDIS);
        String sm4Key = (String) o;
        if (StrUtil.isBlank(sm4Key)) {
            log.error("redis中没有泸州银行的sm4key");
        }
        return sm4Key;
    }
}
