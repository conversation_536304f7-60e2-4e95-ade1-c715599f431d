package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.model.dto.ContractRuleDto;
import com.wosai.upay.job.refactor.mapper.McContractRuleMapper;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
@Repository
public class McContractRuleDAO {

    private static final Cache<String, List<McContractRuleDO>> CACHE = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    @Autowired
    private McContractRuleMapper mcContractRuleMapper;

    public int insert(McContractRuleDO ruleDO) {
        final int row = mcContractRuleMapper.insert(ruleDO);
        CACHE.invalidateAll();
        return row;
    }

    public int updateByRule(String rule, McContractRuleDO updateParams) {
        return mcContractRuleMapper.update(updateParams,
                new LambdaQueryWrapper<McContractRuleDO>().eq(McContractRuleDO::getRule, rule));
    }

    public List<McContractRuleDO> listAllRule() {
        try {
            return CACHE.get("allRule", () -> mcContractRuleMapper.selectList(new LambdaQueryWrapper<McContractRuleDO>().last("limit 1000")));
        } catch (ExecutionException e) {
            throw new ContractBizException("查询异常", e);
        }
    }


    public List<McContractRuleDO> listAllEffectiveRule() {
        try {
            return CACHE.get("allEffectiveRule", () -> mcContractRuleMapper.selectList(
                    new LambdaQueryWrapper<McContractRuleDO>()
                            .eq(McContractRuleDO::getStatus, UseStatusEnum.IN_USE.getValue())));
        } catch (ExecutionException e) {
            throw new ContractBizException("查询异常", e);
        }
    }

    public List<McContractRuleDO> getDOByRules(List<String> rules) {
        return mcContractRuleMapper.selectList(
                new LambdaQueryWrapper<McContractRuleDO>()
                        .in(McContractRuleDO::getRule, rules));
    }


    public Optional<McContractRuleDO> getDOByRule(String rule) {
        McContractRuleDO ruleDO = mcContractRuleMapper.selectOne(
                new LambdaQueryWrapper<McContractRuleDO>()
                        .eq(McContractRuleDO::getRule, rule)
                        .last("limit 1"));
        return Optional.ofNullable(ruleDO);
    }

    public Page<McContractRuleDO> pageMcContractRule(PageInfo pageInfo, ContractRuleDto contractRuleDto) {
        QueryWrapper queryWrapper = new QueryWrapper<McContractRuleDO>()
                .eq(contractRuleDto.getId() != null, "id", contractRuleDto.getId())
                .eq(WosaiStringUtils.isNotBlank(contractRuleDto.getRule()), "rule", contractRuleDto.getRule())
                .eq(contractRuleDto.getPayway() != null, "payway", contractRuleDto.getPayway())
                .eq(WosaiStringUtils.isNotBlank(contractRuleDto.getProvider()), "provider", contractRuleDto.getProvider())
                .eq(WosaiStringUtils.isNotBlank(contractRuleDto.getAcquirer()), "acquirer", contractRuleDto.getAcquirer())
                .eq(WosaiStringUtils.isNotBlank(contractRuleDto.getChannel()), "channel", contractRuleDto.getChannel())
                .eq(contractRuleDto.getStatus() != null, "status", contractRuleDto.getStatus())
                .eq(contractRuleDto.getType() != null, "type", contractRuleDto.getType())
                .like(WosaiStringUtils.isNotBlank(contractRuleDto.getName()), "name", contractRuleDto.getName());

        List<OrderBy> orderBys = pageInfo.getOrderBy();
        if (WosaiCollectionUtils.isEmpty(orderBys)) {
            queryWrapper.orderByDesc("create_at");
        } else {
            for (OrderBy orderBy : orderBys) {
                if (Objects.equals(OrderBy.OrderType.ASC, orderBy.getOrder())) {
                    queryWrapper.orderByAsc(orderBy.getField());
                } else {
                    queryWrapper.orderByDesc(orderBy.getField());
                }
            }
        }

        return PageHelper.startPage(pageInfo.getPage(), pageInfo.getPageSize()).doSelectPage(() -> mcContractRuleMapper.selectList(queryWrapper));

    }


}
