package com.wosai.upay.job.refactor.service.factory;

import com.google.common.collect.Maps;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.task.AbstractInternalScheduleTaskHandleTemplate;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.EnumMap;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21 07:51
 */
@Component
@Slf4j
public class InternalScheduleTaskFactory implements ApplicationRunner {

    private final EnumMap<InternalScheduleTaskTypeEnum, AbstractInternalScheduleTaskHandleTemplate> handlerMap = Maps.newEnumMap(InternalScheduleTaskTypeEnum.class);

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public void run(ApplicationArguments args) {
        applicationContext.getBeansOfType(AbstractInternalScheduleTaskHandleTemplate.class)
                .values().forEach(handler -> handlerMap.put(handler.getTaskType(), handler));
    }

    public AbstractInternalScheduleTaskHandleTemplate getHandler(InternalScheduleTaskTypeEnum taskType) {
        if (!handlerMap.containsKey(taskType)) {
            log.error("获取内部处理器失败,taskType: {}", taskType);
            throw new ContractBizException("获取内部处理器失败");
        }
        return handlerMap.get(taskType);
    }
}
