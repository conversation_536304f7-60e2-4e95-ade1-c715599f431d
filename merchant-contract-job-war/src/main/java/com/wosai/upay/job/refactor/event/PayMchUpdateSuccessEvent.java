package com.wosai.upay.job.refactor.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 在用AT更新成功后触发
 *
 * <AUTHOR>
 * @date 2025/1/22
 */
@Getter
public class PayMchUpdateSuccessEvent extends ApplicationEvent {

    /**
     * 商户号
     */
    private String merchantSn;

    private Integer provider;

    private Integer payWay;

    public PayMchUpdateSuccessEvent(Object source, String merchantSn, Integer provider, Integer payWay) {
        super(source);
        this.merchantSn = merchantSn;
        this.provider = provider;
        this.payWay = payWay;
    }
}
