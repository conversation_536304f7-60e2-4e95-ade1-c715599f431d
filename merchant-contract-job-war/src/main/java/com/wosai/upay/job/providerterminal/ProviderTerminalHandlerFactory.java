package com.wosai.upay.job.providerterminal;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.providerterminal.model.ProviderTerminalAddContext;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/28
 */
@Component
public class ProviderTerminalHandlerFactory {

    @Autowired
    private List<ProviderTerminalHandler> providerTerminalHandlerList;

    public void addProviderTerminal(ProviderTerminalAddContext context) {
        ProviderEnum provider = context.getAcquirerMerchantInfo().getProvider();
        for (ProviderTerminalHandler providerTerminalHandler : providerTerminalHandlerList) {
            if (providerTerminalHandler.getProvider().equals(provider)) {
                providerTerminalHandler.addProviderTerminal(context);
                return;
            }
        }
        throw new ContractBizException("找不到对应的处理类");
    }

}
