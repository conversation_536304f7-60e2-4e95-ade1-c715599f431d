package com.wosai.upay.job.refactor.model.entity;

import java.sql.Date;
import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 微信投诉记录表表实体对象
 *
 * <AUTHOR>
 */
@TableName("wx_complaint_record")
@Data
public class WxComplaintRecordDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 支付源商户号
     */
    @TableField(value = "pay_merchant_id")
    private String payMerchantId;
    /**
     * 投诉单号
     */
    @TableField(value = "complaint_id")
    private String complaintId;
    /**
     * 投诉时间
     */
    @TableField(value = "complaint_time")
    private String complaintTime;
    /**
     * 投诉内容
     */
    @TableField(value = "complaint_detail")
    private String complaintDetail;
    /**
     * 结算通道
     */
    @TableField(value = "provider")
    private Integer provider;
    /**
     * 创建日期
     */
    @TableField(value = "create_date")
    private Date createDate;
    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Timestamp mtime;
    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Timestamp ctime;


}

