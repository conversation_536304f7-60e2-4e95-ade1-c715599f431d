package com.wosai.upay.job.payactivity.alipay;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.MerchantConfigCustom;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.biz.LklPayMerchantBiz;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.MchSnapshot;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.payactivity.biz.AlipayActivityBiz;
import com.wosai.upay.job.payactivity.biz.AlipayUniversityBiz;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.DefaultValueUtil;
import com.wosai.upay.job.util.Utils;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.model.alipay.AlipayEducateCampusAddRequest;
import com.wosai.upay.merchant.contract.model.bluesea.CustomizedInfo;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wosai.upay.job.Constants.BlueSeaConstant.*;
import static com.wosai.upay.job.model.CommonModel.*;

/**
 * @Description: 支付宝高校活动
 * <AUTHOR>
 * @Date: 2021/7/28 11:02 上午
 */
@Slf4j
@Component
public class AlipayUniversityActivity extends AbstractAlipayActivityService {
    @Autowired
    private BlueSeaBiz blueSeaBiz;
    @Autowired
    private BlueSeaService blueSeaService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private BlueSeaTaskMapper blueSeaTaskMapper;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private CallBackService callBackService;
    @Autowired
    private ScheduleUtil scheduleUtil;
    @Autowired
    MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    private AlipayUniversityBiz alipayUniversityBiz;
    @Autowired
    private LklPayMerchantBiz lklPayMerchantBiz;
    @Autowired
    private AlipayActivityBiz activityBiz;

    /**
     * 高校活动支持的商户行业
     *
     * @return
     */
    @Override
    public String supportIndustry() {
        return "all";
    }

    /**
     * @param merchant 商户信息
     * @param formBody
     * @param direct   间直连表示 true 直连 false间连
     */
    @Override
    public void activityTypeCheck(Map merchant, Map formBody, Boolean direct) {
        String merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        boolean lklPayMerchant = lklPayMerchantBiz.isLklPayMerchant(merchantSn, merchantId);
        if (lklPayMerchant) {
            throw new ContractBizException("当前商户不支持参与该活动");
        }
        if (direct) {
            throw new ContractBizException("该活动仅支持间连商户参加");
        }
        String merchantType = (String) formBody.get("merchant_type");
        //团餐商户
        if ("group_meal".equals(merchantType)) {
            String merName = MapUtils.getString(formBody, "main_business_license");
            if (StringUtils.isBlank(merName)) {
                throw new ContractBizException("请填写主体营业执照名称，再提交活动报名");
            }
            formBody.put("merchant_name", merName);
        } else {
            //高校食堂
            //public_school  private_school
            String schoolType = (String) formBody.get("school_type");
            //公立
            if ("public_school".equals(schoolType)) {
                String publicName = MapUtils.getString(formBody, "public_certificate_name");
                if (StringUtils.isBlank(publicName)) {
                    throw new ContractBizException("请填写相关名称，再提交活动报名");
                }
                formBody.put("merchant_name", publicName);
            } else {
                //民办
                String privateName = MapUtils.getString(formBody, "private_certificate_name");
                if (StringUtils.isBlank(privateName)) {
                    throw new ContractBizException("请填写相关名称，再提交活动报名");
                }
                formBody.put("merchant_name", privateName);
            }
        }
    }

    @Override
    public MchSnapshot buildSnapshot(MchSnapshot snapshot, Map merchant, Map formBody, String aliMcc, String aliMchId) {
        String merchantType = (String) formBody.get("merchant_type");
        if ("group_meal".equals(merchantType)) {
            snapshot.setAliMcc("5880");
            //支付宝的商户
            snapshot.setBindUserId(aliMchId);
        } else {
            snapshot.setAliMcc("8220");
        }
        snapshot.setOnline("1");
        return snapshot;
    }

    /**
     * 活动前置处理
     * 前置状态包含三个PENDING/M3/SHOP_CREATED
     *
     * @param task
     */
    @Override
    public void activityApplyPreHandle(BlueSeaTask task) {
        //商户类型
        Map map = JSONObject.parseObject(task.getForm_body(), Map.class);
        //group_meal/university_school
        String merchantType = (String) map.get("merchant_type");


        //活动状态为为已提交未处理
        if (task.getStatus() == BlueSeaConstant.PENDING) {
            CustomizedInfo customizedInfo = blueSeaBiz.buildCustomizedInfo(task.getMerchant_sn());
            //团餐
            if ("group_meal".equals(merchantType)) {
                customizedInfo.setMcc("5880");
            } else {
                customizedInfo.setMcc("8220");
            }
            ///商户名
            String merchantName = (String) map.get("merchant_name");

            customizedInfo.setMerchant_name(merchantName);

            customizedInfo.setForceUpdate(true);
            Map<String, String> forceValue = new HashMap<>();
            forceValue.put("mcc", customizedInfo.getMcc());
            forceValue.put("name", merchantName);
            customizedInfo.setForceUpdateValue(forceValue);

            try {
                boolean result = blueSeaService.updateMerchantToM3(task.getMerchant_sn(), customizedInfo);
                if (result) {
                    blueSeaBiz.updateStatus(task.getId(), M3, null, null, null, null, null, 1);
                    return;
                }
            } catch (Exception e) {
                log.error("商户{}升级M3失败", task.getMchSnapshot(), e);
            }
            blueSeaBiz.updateStatus(task.getId(), FAIL, "商户升级M3失败", null, null, null, null, 1);
            return;
        }

        if (task.getStatus() == M3) {
            //团餐创建学校
            if (merchantType.equals("group_meal")) {
                //改为查询/创建学校 返回学校ID
                //查询/创建学校 参数组装
                try {
                    AlipayEducateCampusAddRequest request = activityBiz.assembleRequest(task);
                    log.info("商户{} 查询学校参数组装:{}",task.getMerchant_sn(),JSONObject.toJSONString(request));
                    String instId = activityBiz.getInstId(request);
                    if (StringUtils.isBlank(instId)) {
                        throw new ContractBizException("查询以及创建未返回学校ID");
                    }
                    MchSnapshot snapshot = MchSnapshot.builder()
                            .instId(instId)
                            .instName(request.getInst_name())
                            .build();
                    blueSeaBiz.updateStatus(task.getId(), WAIT_APPLY, null, snapshot, null, null, null, 1);
                    if(Utils.isNullOrZero(task.getApply_id())) {
                        CallBackBean callBackBean = CallBackBean.builder()
                                .auditId(task.getAudit_id()).templateId(MapUtils.getLong(JSONObject.parseObject(task.getForm_body(), Map.class), BlueSeaConstant.TEMPLATE_ID))
                                .build();
                        callBackBean.setResultType(1);
                        callBackBean.setMessage("更新MCC成功,商户类型创建学校成功 学校ID:" + instId);
                        callBackService.addComment(callBackBean);
                    }
                } catch (ContractBizException e) {
                    blueSeaBiz.updateStatus(task.getId(), FAIL, e.getMessage(), null, null, null, null, 1);
                } catch (Exception e) {
                    log.error("商户{} 学校ID查询 系统异常 等待下次定时任务执行",task.getMerchant_sn(),e);
                }

            } else {
                //高校 中职校园
                blueSeaBiz.updateStatus(task.getId(), WAIT_APPLY, null, null, null, null, null, 1);
                if(Utils.isNullOrZero(task.getApply_id())) {
                    CallBackBean callBackBean = CallBackBean.builder()
                            .auditId(task.getAudit_id()).templateId(MapUtils.getLong(JSONObject.parseObject(task.getForm_body(), Map.class), BlueSeaConstant.TEMPLATE_ID))
                            .build();
                    callBackBean.setResultType(1);
                    callBackBean.setMessage("更新MCC成功,商户类型无需创建学校");
                    callBackService.addComment(callBackBean);
                }
            }
        }
    }


    /**
     * 前置更新M3等已完成
     * 活动报名
     *
     * @param task
     */
    @Override
    public void activityApply(BlueSeaTask task) {
        //活动报名需业务点击审批通过
        MchSnapshot snapshot = task.getMchSnapshot();
        //线上报名
        if (snapshot != null && "1".equals(snapshot.getOnline())) {
            return;
        }
        //线下报名
        blueSeaBiz.updateStatus(task.getId(), ACTIVITY_CREATE_SUCCESS, null, null, null, null, null, 2);
    }

    /**
     * 活动成功后置处理
     *
     * @param task
     */
    @Override
    public void activityApplyPostHandle(BlueSeaTask task) {
        MchSnapshot snapshot = task.getMchSnapshot();
        //线上报名
        if (snapshot != null && "1".equals(snapshot.getOnline())) {
            //切换套餐
            changeFeeRate(task);
        }
    }

    /**
     * 切换费率
     *
     * @param blueSeaTask
     * @return
     */
    public CommonResult changeFeeRate(BlueSeaTask blueSeaTask) {
        CommonResult result = null;
        //查询状态是否为成功
        try {
            if (blueSeaTask.getStatus() == ACTIVITY_CREATE_SUCCESS) {
                //活动前置工作处理完成,切换费率
                doChangeFeeRate(blueSeaTask);
                //更新活动成功
                blueSeaBiz.updateStatus(blueSeaTask.getId(), SUCCESS, "切换费率套餐成功", null, null, null, null, 2);
                result = new CommonResult(CommonResult.SUCCESS, "切换费率套餐成功");
            }
        } catch (Exception e) {
            log.error("商户{}申请支付宝活动 切换费率套餐,异常", blueSeaTask.getMerchant_sn());
            result = new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
            //更新失败
            blueSeaBiz.updateStatus(blueSeaTask.getId(), FAIL, e.getMessage(), null, null, null, null, 2);
        }
        return result;

    }

    /**
     * 拒绝审批
     *
     * @param merchantSn
     * @param auditId
     * @param activityType
     * @param formBody
     * @return
     */
    @Override
    public CommonResult auditReject(String merchantSn, Long auditId, int activityType, Map formBody) {
        CommonResult result;
        try {
            BlueSeaTask task = blueSeaTaskMapper.selectTaskBySnAndTypeAndAudit(merchantSn, activityType, auditId);
            if (task != null) {
                blueSeaTaskMapper.updateStatusById(task.getId(), FAIL, "审批驳回");
            }
            String msg = task == null ? "商户没有此审批" : "审批驳回处理成功";
            result = new CommonResult(CommonResult.SUCCESS, msg);
        } catch (Exception e) {
            log.error("商户{}申请支付宝活动 审批驳回处理失败", merchantSn);
            result = new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        }
        return result;
    }

    /**
     * 审批通过
     *
     * @param merchantSn
     * @param auditId
     * @param activityType
     * @param formBody
     * @return
     */
    @Override
    public CommonResult auditApprove(String merchantSn, Long auditId, int activityType, Map formBody) {
        BlueSeaTask blueSeaTask = blueSeaTaskMapper.selectTaskBySnAndType(merchantSn, activityType);
        // 逻辑兼容
        //线下报名 审批通过是切换费率
        // 线上报名 审批通过 支付宝报名
        MchSnapshot snapshot = blueSeaTask.getMchSnapshot();

        CommonResult result;
        //线上报名
        if (snapshot != null && "1".equals(snapshot.getOnline())) {
            //前置工作还没有完成 业务点击通过
            if (blueSeaTask.getStatus() != WAIT_APPLY) {
                MchSnapshot mchSnapshot = new MchSnapshot();
                mchSnapshot.setHoldHandle("1");
                blueSeaBiz.updateStatus(blueSeaTask.getId(), blueSeaTask.getStatus(), null, mchSnapshot, null, null, null, 2);
                result = new CommonResult(CommonResult.SUCCESS, "支付宝高校活动报名中");
            } else {
                //报名
                result = alipayUniversityBiz.apply(blueSeaTask);
            }
        } else {
            //线下报名 切换套餐费率
            result = changeFeeRate(blueSeaTask);
        }
        return result;
    }


    /**
     * 切换费率
     *
     * @param blueSeaTask
     */
    private void doChangeFeeRate(BlueSeaTask blueSeaTask) {
        // 5880 更新shopID 或者学校ID
        MchSnapshot mchSnapshot = blueSeaTask.getMchSnapshot();
        if ("5880".equals(mchSnapshot.getAliMcc())) {
            String shopId = mchSnapshot.getShopId();
            String instId = mchSnapshot.getInstId();
            String instName = mchSnapshot.getInstName();
            Map merchant = merchantService.getMerchantBySn(blueSeaTask.getMerchant_sn());
            String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);

            //门店ID
            if (StringUtils.isNotBlank(shopId)) {
                //pay_way为2
                Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, 2);
                //得到原来 的交易参数
                Map merchantParams = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
                if (!CollectionUtils.isEmpty(merchantParams)) {
                    //把shop_id 学校ID 放到 XXX_trade_params里面
                    Map lkl = (Map) BeanUtil.getProperty(merchantParams, UP_DIRECT_TRADE_PARAMS);
                    if (!CollectionUtils.isEmpty(lkl)) {
                        lkl.put("alipay_store_id", shopId);
                    }
                    Map lklOrg = (Map) BeanUtil.getProperty(merchantParams, LKL_ORG_TRADE_PARAMS);
                    if (!CollectionUtils.isEmpty(lklOrg)) {
                        lklOrg.put("alipay_store_id", shopId);
                    }
                    Map tonglian = (Map) BeanUtil.getProperty(merchantParams, TONGLIAN_TRADE_PARAMS);
                    if (!CollectionUtils.isEmpty(tonglian)) {
                        tonglian.put("alipay_store_id", shopId);
                    }
                    //更新交易参数
                    tradeConfigService.updateMerchantConfig(CollectionUtil.hashMap(MerchantConfig.PARAMS, merchantParams,
                            DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID)));
                }
            }
            //学校ID
            if(StringUtils.isNotBlank(instId)){
                Map<String,Object> merchantConfigCustom=new HashMap();
                merchantConfigCustom.put(MerchantConfigCustom.MERCHANT_ID,merchantId);
                merchantConfigCustom.put(MerchantConfigCustom.TYPE,10);
                if(StringUtils.isNotBlank(instName)) {
                    instId = instId + ":" + instName;
                }
                merchantConfigCustom.put(MerchantConfigCustom.B2C_VALUE,instId);
                merchantConfigCustom.put(MerchantConfigCustom.C2B_VALUE,instId);
                merchantConfigCustom.put(MerchantConfigCustom.WAP_VALUE,instId);
                merchantConfigCustom.put(MerchantConfigCustom.MINI_VALUE,instId);
                tradeConfigService.createMerchantConfigCustom(merchantConfigCustom);
            }
        }
        //切换套餐ID
        String[] profiles = applicationContext.getEnvironment().getActiveProfiles();
        long tradeComboId = 81L;
        // fine TODO
        if (profiles.length > 0 && !profiles[0].equals("prod")) {
            tradeComboId = 761L;
        }

        if(Utils.isNullOrZero(blueSeaTask.getApply_id())) {
            ApplyFeeRateRequest request = new ApplyFeeRateRequest();
            request.setMerchantSn(blueSeaTask.getMerchant_sn());
            request.setAuditSn(String.valueOf(blueSeaTask.getAudit_id()));
            request.setTradeComboId(tradeComboId);
            feeRateService.applyFeeRateOne(request);
        }
    }

}
