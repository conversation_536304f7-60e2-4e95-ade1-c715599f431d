package com.wosai.upay.job.refactor.model.bo;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 进件收单机构决策规则计算结果
 *
 * <AUTHOR>
 * @date 2024/8/20 14:47
 */
@Data
@AllArgsConstructor
public class ContractRuleDecisionEvaluateResultBO {

    public ContractRuleDecisionEvaluateResultBO(boolean pass) {
        this.pass = pass;
    }

    /**
     * 是否通过
     */
    private boolean pass;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 是否不通过
     *
     * @return 结果
     */
    public boolean notPass() {
        return !pass;
    }

    /**
     * 通过
     *
     * @return 结果
     */
    public static ContractRuleDecisionEvaluateResultBO success(String message) {
        return new ContractRuleDecisionEvaluateResultBO(true, message);
    }

    /**
     * 不通过
     *
     * @return 结果
     */
    public static ContractRuleDecisionEvaluateResultBO fail() {
        return new ContractRuleDecisionEvaluateResultBO(false);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
