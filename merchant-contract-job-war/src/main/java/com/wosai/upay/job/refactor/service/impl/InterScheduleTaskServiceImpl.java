package com.wosai.upay.job.refactor.service.impl;

import com.google.common.collect.Lists;
import com.shouqianba.cua.utils.object.DateExtensionUtils;
import com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO;
import com.wosai.upay.job.refactor.dao.InternalScheduleSubTaskDAO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleMainTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.factory.InternalScheduleTaskFactory;
import com.wosai.upay.side.service.GeneralRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 内部任务调度服务
 *
 * <AUTHOR>
 * @date 2024/6/14 16:04
 */
@Slf4j
@Service
public class InterScheduleTaskServiceImpl {

    @Resource
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Resource
    private InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    @Resource(name = "generalRuleService")
    private GeneralRuleService generalRuleService;

    private static final Integer DEFAULT_EXPIRE_DAYS_LIMIT = 60;


    @Resource
    private InternalScheduleTaskFactory internalScheduleTaskFactory;



    /**
     * 是否存在商户指定类型正在进行中的任务
     *
     * @param merchantSn 商户号
     * @param taskType   任务类型
     * @return true-存在
     */
    public boolean isExistedProcessingTask(String merchantSn, InternalScheduleTaskTypeEnum taskType) {
        if (StringUtils.isBlank(merchantSn) || Objects.isNull(taskType)) {
            return false;
        }
        Long existedProcessingTaskNum = internalScheduleMainTaskDAO.countBySnAndTypeWithStatusNotIn(merchantSn, Lists.newArrayList(taskType.getValue()),
                Lists.newArrayList(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue(), InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue()));
        return existedProcessingTaskNum > 0;
    }

    /**
     * 是否存在商户指定多个类型正在进行中的任务
     *
     * @param merchantSn 商户号
     * @param taskTypes  任务类型列表
     * @return true-存在
     */
    public boolean isExistedProcessingTask(String merchantSn, List<InternalScheduleTaskTypeEnum> taskTypes) {
        if (StringUtils.isBlank(merchantSn) || CollectionUtils.isEmpty(taskTypes)) {
            return false;
        }
        Long existedProcessingTaskNum = internalScheduleMainTaskDAO.countBySnAndTypeWithStatusNotIn(merchantSn, taskTypes.stream().map(InternalScheduleTaskTypeEnum::getValue).collect(Collectors.toList()),
                Lists.newArrayList(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue(), InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue()));
        return existedProcessingTaskNum > 0;
    }

    /**
     * 新增主任务以及对于的子任务列表,不指定过期时间则默认5个工作日后过期
     *
     * @param mainTaskDO 主任务
     * @param subTaskDOList 子任务列表
     * @return 新增条数
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer insertTasks(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> subTaskDOList) {
        if (Objects.isNull(mainTaskDO)) {
            return 0;
        }
        populateMainTaskExpireTime(mainTaskDO);
        Integer insertTaskRows = internalScheduleMainTaskDAO.insertOne(mainTaskDO);
        if (CollectionUtils.isNotEmpty(subTaskDOList)) {
            subTaskDOList.forEach(t -> t.setMainTaskId(mainTaskDO.getId()));
            insertTaskRows += internalScheduleSubTaskDAO.batchInsert(subTaskDOList);
        }

        return insertTaskRows;
    }


    /**
     * 新增主任务以及对于的子任务列表,不指定过期时间则默认5个工作日后过期
     *
     * @param mainTaskDO 主任务
     * @param subTaskDOList 子任务列表
     * @return 新增条数
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer insertTasksWithExpireTime(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> subTaskDOList) {
        if (Objects.isNull(mainTaskDO)) {
            return 0;
        }
        mainTaskDO.setExpirationTime(Timestamp.valueOf(LocalDateTime.now().plusDays(15)));
        Integer insertTaskRows = internalScheduleMainTaskDAO.insertOne(mainTaskDO);
        if (CollectionUtils.isNotEmpty(subTaskDOList)) {
            subTaskDOList.forEach(t -> t.setMainTaskId(mainTaskDO.getId()));
            insertTaskRows += internalScheduleSubTaskDAO.batchInsert(subTaskDOList);
        }

        return insertTaskRows;
    }


    /**
     * 批量新增主任务以及对应的子任务列表,不指定过期时间则默认5个工作日后过期
     *
     * @param taskDOListMap 主任务及对应的子任务列表
     * @return 新增条数
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchInsertTasks(Map<InternalScheduleMainTaskDO, List<InternalScheduleSubTaskDO>> taskDOListMap) {
        if (MapUtils.isEmpty(taskDOListMap)) {
            return 0;
        }
        populateMainTasksExpireTime(taskDOListMap.keySet());
        Integer mainTaskRecords = internalScheduleMainTaskDAO.batchInsert(new ArrayList<>(taskDOListMap.keySet()));
        for (Map.Entry<InternalScheduleMainTaskDO, List<InternalScheduleSubTaskDO>> entry : taskDOListMap.entrySet()) {
            Long id = entry.getKey().getId();
            entry.getValue().forEach(t -> t.setMainTaskId(id));
        }
        internalScheduleSubTaskDAO.batchInsert(taskDOListMap.values().stream().flatMap(Collection::stream).sorted(Comparator.comparing(InternalScheduleSubTaskDO::getMainTaskId))
                .collect(Collectors.toList()));
        return mainTaskRecords;
    }



    /**
     * 初始化子任务 主要用于填充id
     *
     * @param size       子任务数量
     * @param mainTaskId 主任务id
     * @param type       任务类型
     * @return 子任务
     */
    public List<InternalScheduleSubTaskDO> initSubTask(int size, Long mainTaskId, Integer type) {
        ArrayList<InternalScheduleSubTaskDO> list = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            InternalScheduleSubTaskDO subTaskDO = new InternalScheduleSubTaskDO();
            subTaskDO.setMainTaskId(mainTaskId);
            subTaskDO.setType(type);
            list.add(subTaskDO);
        }
        internalScheduleSubTaskDAO.batchInsert(list);
        return list;
    }

    /**
     * 更新主任务以及对应的子任务列表
     *
     * @param mainTaskDO 主任务
     * @param subTaskDOS 子任务列表
     * @return 更新条数
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchUpdateTasks(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> subTaskDOS) {
        if (Objects.isNull(mainTaskDO)) {
            return 0;
        }
        mainTaskDO.setCtime(null);
        mainTaskDO.setMtime(null);
        if (CollectionUtils.isNotEmpty(subTaskDOS)) {
            subTaskDOS.forEach(subTaskDO -> {
                subTaskDO.setCtime(null);
                subTaskDO.setMtime(null);
            });
        }
        populateMainTaskExpireTime(mainTaskDO);
        Integer effectRows = 0;
        effectRows += internalScheduleMainTaskDAO.updateByPrimaryKeySelective(mainTaskDO);
        effectRows += internalScheduleSubTaskDAO.batchUpdateByIdSelective(subTaskDOS);
        return effectRows;
    }

    private void populateMainTaskExpireTime(InternalScheduleMainTaskDO mainTaskDO) {
        if (Objects.isNull(mainTaskDO.getExpirationTime())) {
            String expirationTime = generalRuleService.getWeekDayByDays(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), DEFAULT_EXPIRE_DAYS_LIMIT);
            try {
                mainTaskDO.setExpirationTime(DateExtensionUtils.parseTimestamp(expirationTime));
            } catch (ParseException e) {
                log.warn("日期转换错误", e);
                mainTaskDO.setExpirationTime(DateExtensionUtils.addDays(new Timestamp(System.currentTimeMillis()), DEFAULT_EXPIRE_DAYS_LIMIT));
            }
        }
    }

    private void populateMainTasksExpireTime(Set<InternalScheduleMainTaskDO> internalScheduleMainTaskDOS) {
        if (CollectionUtils.isEmpty(internalScheduleMainTaskDOS)) {
            return;
        }
        String expirationTime = generalRuleService.getWeekDayByDays(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), DEFAULT_EXPIRE_DAYS_LIMIT);
        for (InternalScheduleMainTaskDO internalScheduleMainTaskDO : internalScheduleMainTaskDOS) {
            try {
                internalScheduleMainTaskDO.setExpirationTime(DateExtensionUtils.parseTimestamp(expirationTime));
            } catch (ParseException e) {
                log.warn("日期转换错误", e);
                internalScheduleMainTaskDO.setExpirationTime(DateExtensionUtils.addDays(new Timestamp(System.currentTimeMillis()), DEFAULT_EXPIRE_DAYS_LIMIT));
            }
        }
    }

    /**
     * 根据商户号，任务类型，调度非不可调度的任务
     *
     * @param merchantSn 商户号
     * @param taskType   任务类型
     */
    public void scheduleProcessingTask(String merchantSn, InternalScheduleTaskTypeEnum taskType) {
        if (Objects.isNull(taskType) || StringUtils.isBlank(merchantSn)) {
            return;
        }
        List<InternalScheduleMainTaskDO> internalScheduleMainTaskDOS = internalScheduleMainTaskDAO
                .listBySnAndType(merchantSn, taskType.getValue())
                .stream()
                .filter(t -> !t.taskCanNotHandled())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(internalScheduleMainTaskDOS)) {
            return;
        }
        internalScheduleTaskFactory.getHandler(taskType).batchHandleMainTasks(internalScheduleMainTaskDOS);
    }
}
