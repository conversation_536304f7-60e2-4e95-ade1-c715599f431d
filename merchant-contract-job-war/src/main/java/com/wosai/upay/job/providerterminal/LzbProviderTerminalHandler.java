package com.wosai.upay.job.providerterminal;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.enume.ProviderTerminalBindLevel;
import com.wosai.upay.job.enume.ProviderTerminalIdTypeEnum;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.providerterminal.model.ProviderTerminalAddContext;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.ScaleConverterUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Component
public class LzbProviderTerminalHandler extends AbstractProviderTerminalHandler {

    @Override
    protected List<Integer> getSupportPaywayList() {
        return ALIPAY_WEIXIN_LIST;
    }

    @Override
    public ProviderEnum getProvider() {
        return ProviderEnum.PROVIDER_LZB;
    }

    @Override
    protected String contractProviderTerminalId(ProviderTerminalAddContext request) {
        if (ProviderTerminalBindLevel.MERCHANT.equals(request.getBindLevel())) {
            String merchantSn = request.getMerchantSn();
            ContractTask task = new ContractTask().setMerchant_sn(merchantSn)
                    .setStatus(0)
                    .setRule_group_id(McConstant.RULE_GROUP_LZB)
                    .setType(ProviderUtil.CONTRACT_TYPE_ADDTERM)
                    .setAffect_status_success_task_count(0)
                    .setAffect_sub_task_count(1);
            contractTaskBiz.insert(task);
            ContractSubTask subTask = new ContractSubTask()
                    .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM)
                    .setStatus_influ_p_task(1)
                    .setChannel(ProviderUtil.LZB_CHANNEL)
                    .setMerchant_sn(merchantSn)
                    .setContract_rule(McConstant.RULE_GROUP_LZB)
                    .setRule_group_id(McConstant.RULE_GROUP_LZB)
                    .setSchedule_dep_task_id(0L)
                    .setSchedule_status(1)
                    .setP_task_id(task.getId());
            contractSubTaskMapper.insert(subTask);
            return null;
        } else {
            throw new ContractBizException("不支持的绑定级别");
        }
    }
}
