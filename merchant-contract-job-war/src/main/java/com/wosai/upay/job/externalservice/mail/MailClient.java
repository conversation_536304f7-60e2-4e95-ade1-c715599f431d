package com.wosai.upay.job.externalservice.mail;

import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.wosai.upay.job.externalservice.mail.model.MailSendReq;
import com.wosai.upay.job.externalservice.mail.model.MailSendResp;
import com.wosai.upay.job.util.ChatBotUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
@Component
@Slf4j
public class MailClient {

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Value("${mail-gateway}")
    private String mailGateway;
    private static final String ERRCODE = "errcode";
    private static final String ERRMSG = "errmsg";

    private final RestTemplate restTemplate = new RestTemplate();
    // 创建重试器，失败后立即重试，最多重试3次
    private final static Retryer<MailSendResp> retryer = RetryerBuilder.<MailSendResp>newBuilder()
            .retryIfResult(result -> !result.getSuccess())
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            .withWaitStrategy(WaitStrategies.noWait())
            .build();

    public MailSendResp sendEmail(MailSendReq req) {
        try {
            Map result = restTemplate.postForObject(mailGateway, req.genMailSendRequest(), Map.class);
            if (result == null) {
                return MailSendResp.fail("响应为空");
            }
            if (MapUtils.getInteger(result, ERRCODE) == 0) {
                return MailSendResp.success();
            } else {
                return MailSendResp.fail(MapUtils.getString(result, ERRMSG));
            }
        } catch (Exception e) {
            return MailSendResp.fail("发送异常: " + e.getMessage());
        }
    }

    /**
     * 发送邮件带重试机制
     * 使用Guava Retry，失败后立即重试，最多重试3次
     * 如果最终失败则发送告警到飞书群
     *
     * @param req 邮件发送请求
     * @return 邮件发送响应
     */
    public MailSendResp sendEmailWithRetry(MailSendReq req) {
        try {
            Callable<MailSendResp> callable = () -> {
                MailSendResp response = sendEmail(req);
                if (response.getSuccess()) {
                    log.info("邮件发送成功，邮件ID: {}", req.getId());
                } else {
                    log.warn("邮件发送失败，邮件ID: {}, 错误信息: {}", req.getId(), response.getMessage());
                }
                return response;
            };

            return retryer.call(callable);
        } catch (RetryException e) {
            // 所有重试都失败了，发送告警
            MailSendResp lastResult = (MailSendResp) e.getLastFailedAttempt().getResult();
            String lastErrorMessage = lastResult.getMessage();
            String alertMessage = String.format("📧 邮件发送失败\n邮件ID: %s\n错误原因: %s", 
                    req.getId(), lastErrorMessage);
            
            chatBotUtil.sendMessageToContractWarnChatBot(alertMessage);
            log.error("邮件发送最终失败，已发送告警，邮件ID: {}", req.getId());
            
            return lastResult;
        } catch (Exception e) {
            // 其他异常情况
            String alertMessage = String.format("📧 邮件发送异常\n邮件ID: %s\n错误原因: %s", 
                    req.getId(), e.getMessage());
            
            try {
                chatBotUtil.sendMessageToContractWarnChatBot(alertMessage);
                log.error("邮件发送异常，已发送告警，邮件ID: {}", req.getId(), e);
            } catch (Exception alertException) {
                log.error("发送告警失败，邮件ID: {}", req.getId(), alertException);
                throw alertException;
            }
            
            return MailSendResp.fail("发送异常: " + e.getMessage());
        }
    }
}
