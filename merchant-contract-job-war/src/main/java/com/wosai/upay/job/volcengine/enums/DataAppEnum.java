package com.wosai.upay.job.volcengine.enums;

/**
 * 数据部应用枚举类
 *
 * @link https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********
 */
public enum DataAppEnum {

    B_MERCHANT("********", "********", "merchant_id", "B端商户"),
    B_USER("********", "********", "account_id", "B端用户"),
    ;
    /**
     * 应用ID
     */
    private final String appId;
    /**
     * 测试应用ID
     */
    private final String appTestId;
    /**
     * 应用主体ID
     */
    private final String subjectId;
    /**
     * 说明
     */
    private final String desc;

    DataAppEnum(String appId, String appTestId, String subjectId, String desc) {
        this.appId = appId;
        this.appTestId = appTestId;
        this.subjectId = subjectId;
        this.desc = desc;
    }

    public String getAppId() {
        return appId;
    }

    public String getAppTestId() {
        return appTestId;
    }

    public String getSubjectId() {
        return subjectId;
    }

    public String getDesc() {
        return desc;
    }
}
