package com.wosai.upay.job.scheduler;

import com.alibaba.fastjson.JSONObject;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.model.MerchantUserPushSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSidePushService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.mapper.SelfOpenCcbDecpMapper;
import com.wosai.upay.job.model.DO.SelfOpenCcbDecp;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/1/7
 */
@Component
@ConditionalOnProperty(name = "schedule.init", havingValue = "true")
@Slf4j
public class SelfOpenCcbDecpSchedule {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantUserService merchantUserService;

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private ClientSidePushService clientSidePushService;

    @Autowired
    private SelfOpenCcbDecpMapper selfOpenCcbDecpMapper;

    @Autowired
    private RedisLock redisLock;

    @Value("${ccb_app_dev_code}")
    public String ccbAppDevCode;

    @Value("${ccb_decp_success_notice_code}")
    public String decpSuccessNoticeCode;

    @Value("${ccb_decp_success_push_code}")
    private String decpSuccessPushCode;

    private static final String CHANGE_OPEN_STATUS_KEY = "change_decp_open_status_key";

    private static final long CHANGE_INTERVAL = 86400000L;

    /**
     * 将审核中的变更为开通成功, 并且发送通知
     */
    @Scheduled(fixedDelay = 60000L)
    public void changeProcessToSuccessAndSendNotice() {
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(CHANGE_OPEN_STATUS_KEY, value, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return;
        }
        try {
            long currentTime = System.currentTimeMillis();
            // 查询出两天内所有审核中的商户,并且按照mtime升序排列, 所以只要有一个是24小时内的，后面的肯定都在24小时内了
            List<SelfOpenCcbDecp> selfOpenCcbDecps = selfOpenCcbDecpMapper.selectProcessByMtime(currentTime - ScheduleUtil.DEFAULT_TWO_DAYS_MILLIS_QUERY);
            for (SelfOpenCcbDecp selfOpenCcbDecp : selfOpenCcbDecps) {
                if ((currentTime - selfOpenCcbDecp.getMtime()) >= CHANGE_INTERVAL) {
                    doChangeOpenStatusAndSendNotice(selfOpenCcbDecp);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("changeProcessToSuccessAndSendNotice schedule exception", e);
        } finally {
            redisLock.unlock(CHANGE_OPEN_STATUS_KEY, value);
        }
    }

    private void doChangeOpenStatusAndSendNotice(SelfOpenCcbDecp selfOpenCcbDecp) {
        try {
            // 1 将状态变更为开通成功
            selfOpenCcbDecpMapper.updateOpenStatusById(selfOpenCcbDecp.getId(), SelfOpenCcbDecp.SUCCESS_OPEN_STATUS);

            // 2 发送通知
            final Map merchant = merchantService.getMerchantBySn(selfOpenCcbDecp.getMerchant_sn());
            final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            if (userInfo != null) {
                final MerchantUserNoticeSendModel noticeSendModel = new MerchantUserNoticeSendModel();
                noticeSendModel.setDevCode(ccbAppDevCode);
                noticeSendModel.setTemplateCode(decpSuccessNoticeCode);
                noticeSendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                noticeSendModel.setTimestamp(System.currentTimeMillis());
                clientSideNoticeService.sendToMerchantUser(noticeSendModel);
                log.info("建行数币申请通过,发送通知:{}", JSONObject.toJSONString(noticeSendModel));

                MerchantUserPushSendModel pushSendModel = new MerchantUserPushSendModel();
                pushSendModel.setDevCode(ccbAppDevCode);
                pushSendModel.setTemplateCode(decpSuccessPushCode);
                pushSendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                pushSendModel.setTimestamp(System.currentTimeMillis());
                clientSidePushService.sendToMerchantUser(pushSendModel);
                log.info("建行数币申请通过,发送推送:{}", JSONObject.toJSONString(pushSendModel));
            }
        } catch (Exception e) {
            log.error("do changeProcessToSuccessAndSendNotice exception, 商户号:{}", selfOpenCcbDecp.getMerchant_sn(), e);
        }
    }
}
