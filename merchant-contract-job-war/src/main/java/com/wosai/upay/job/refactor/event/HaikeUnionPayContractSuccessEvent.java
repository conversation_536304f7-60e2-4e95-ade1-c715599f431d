package com.wosai.upay.job.refactor.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/22
 */
@Getter
public class HaikeUnionPayContractSuccessEvent extends ApplicationEvent {

    /**
     * 商户号
     */
    private String merchantSn;

    public HaikeUnionPayContractSuccessEvent(Object source, String merchantSn) {
        super(source);
        this.merchantSn = merchantSn;
    }
}
