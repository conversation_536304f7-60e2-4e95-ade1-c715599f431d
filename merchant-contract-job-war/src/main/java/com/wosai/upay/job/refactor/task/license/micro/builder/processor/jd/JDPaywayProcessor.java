package com.wosai.upay.job.refactor.task.license.micro.builder.processor.jd;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.bo.LklOpenUnionPayTradeParamBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.task.license.micro.builder.context.TradeParamsBuilderContext;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.PaywayProcessor;
import com.wosai.upay.job.refactor.utils.BeanCopyUtils;

import java.util.UUID;


/**
 * j京东白条支付方式处理器
 *
 * <AUTHOR>
 */
public class JDPaywayProcessor implements PaywayProcessor {

    private final String acquirerType;

    private WechatAuthBiz wechatAuthBiz;

    public JDPaywayProcessor(String acquirerType) {
        this.acquirerType = acquirerType;
    }

    @Override
    public void processParam(MerchantProviderParamsDO newParam,
                             MerchantProviderParamsDO oldParam,
                             TradeParamsBuilderContext context) {
        
        MerchantAcquireInfoBO merchantAcquireInfo = context.getMerchantAcquireInfo();
        newParam.setId(UUID.randomUUID().toString());
        newParam.setCtime(System.currentTimeMillis());
        newParam.setMtime(System.currentTimeMillis());
        newParam.setChannelNo(oldParam.getChannelNo());
        newParam.setContractRule(oldParam.getContractRule());
        newParam.setRuleGroupId(oldParam.getRuleGroupId());
        // 设置银联商户号,注意这里不设置 pay_merchant_id,原因是拉卡拉使用的云闪付做交易,如果设置了,就会在查询的时候查出来两条记录
        newParam.setProviderMerchantId(merchantAcquireInfo.getUnionNo());
        // 根据不同收单机构设置特定参数
        switch (acquirerType) {
            case "LKLV3":
                processLklV3UnionPay(newParam, merchantAcquireInfo);
                break;
            default:
                // 默认处理
                break;
        }
    }
    
    /**
     * 处理拉卡拉V3银联参数
     */
    private void processLklV3UnionPay(MerchantProviderParamsDO newParam, MerchantAcquireInfoBO merchantAcquireInfo) {
        LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = new LklOpenUnionPayTradeParamBO();
        lklOpenUnionPayTradeParamBO.setProviderMerchantId(merchantAcquireInfo.getUnionNo());
        lklOpenUnionPayTradeParamBO.setTermId(merchantAcquireInfo.getLklTermNo());
        newParam.setExtra(JSON.toJSONString(lklOpenUnionPayTradeParamBO));
    }
    

    
    @Override
    public Integer getSupportedPayway() {
        return PaywayEnum.JD_WALLET.getValue();
    }
    
    @Override
    public String getSupportedAcquirerType() {
        return acquirerType;
    }

    // Setter method for dependency injection
    public void setWechatAuthBiz(WechatAuthBiz wechatAuthBiz) {
        this.wechatAuthBiz = wechatAuthBiz;
    }
}
