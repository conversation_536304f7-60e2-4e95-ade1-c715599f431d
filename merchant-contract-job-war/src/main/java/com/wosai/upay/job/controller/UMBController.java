package com.wosai.upay.job.controller;

import cfca.sadk.util.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.model.callback.umb.res.UMBAuditCallBackRes;
import com.wosai.upay.job.model.callback.umb.res.UMBSignCallBackRes;
import com.wosai.upay.job.service.UMBCallBackService;
import com.wosai.upay.job.util.EnvUtil;
import com.wosai.upay.job.util.umb.SM2Utils;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.provider.UMBParam;

import java.nio.charset.StandardCharsets;

import com.wosai.upay.merchant.contract.model.umb.UMBReq;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;

import static org.springframework.core.Ordered.HIGHEST_PRECEDENCE;

@Slf4j
@RestController
@RequestMapping("/contract/umb")
public class UMBController {
    private static final String UMB_SUCCESS = "SUCC";
    private final ContractParamsBiz contractParamsBiz;
    private final UMBCallBackService umbCallBackService;
    private final ObjectMapper objectMapper;
    private final EnvUtil envUtil;
    private final ThreadPoolTaskExecutor executor;

    @Value("${umb_platmer_id_1}")
    @Order(HIGHEST_PRECEDENCE)
    public String umbPlatMerId;

    private UMBParam umbParam;

    public UMBController(ContractParamsBiz contractParamsBiz, UMBCallBackService umbCallBackService, ObjectMapper objectMapper, EnvUtil envUtil,
                         @Qualifier("umbThreadPool") ThreadPoolTaskExecutor executor) {
        this.contractParamsBiz = contractParamsBiz;
        this.umbCallBackService = umbCallBackService;
        this.objectMapper = objectMapper;
        this.envUtil = envUtil;
        this.executor = executor;
    }


    //将回调缩减到三个,
    @PostMapping(value = "/signCallback", produces = "application/json")
    public String signCallback(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleSignCallBack(verified, umbParam);
        return UMB_SUCCESS;
    }

    @PostMapping(value = "/auditCallback", produces = "application/json")
    public String auditCallback(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleAuditPassCallBack(verified);
        return UMB_SUCCESS;
    }

    @PostMapping(value = "/modifyAuditCallback", produces = "application/json")
    public String modifyAuditCallback(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleModifyAuditPassCallBack(verified);
        return UMB_SUCCESS;
    }
    // 下面的controller 2525/08/01 之后可以直接删除.

    @PostConstruct
    public void init() {
        umbParam = contractParamsBiz.buildContractParams(umbPlatMerId, UMBParam.class);
        umbParam.setRule_group_id(umbPlatMerId);
    }

    // 处理 审核结果回调
    //这里的回调地址是进件或者签约时上送的地址.务必保持两者一致.
    //测试环境的回调地址.
    @PostMapping(value = "/CF3000053368/auditCallback", produces = "application/json")
    public String auditCallback0(@RequestParam String data) {
        return handleAuditCallBack(data);
    }

    @NotNull
    private String handleAuditCallBack(String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleAuditPassCallBack(verified);
        return UMB_SUCCESS;
    }

    //生产回调地址
    @PostMapping(value = "/CF2002323280/auditCallback", produces = "application/json")
    public String auditCallback1(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleAuditPassCallBack(verified);
        return UMB_SUCCESS;
    }

    @PostMapping(value = "/CF2002323296/auditCallback", produces = "application/json")
    public String auditCallback2(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleAuditPassCallBack(verified);
        return UMB_SUCCESS;
    }

    //商户修改操作回调
    @PostMapping(value = "/CF2002323280/modifyAuditCallback", produces = "application/json")
    public String modifyAuditCallback1(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleModifyAuditPassCallBack(verified);
        return UMB_SUCCESS;
    }

    @PostMapping(value = "/CF2002323296/modifyAuditCallback", produces = "application/json")
    public String modifyAuditCallback2(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleModifyAuditPassCallBack(verified);
        return UMB_SUCCESS;
    }

    @PostMapping(value = "/CF3000053368/modifyAuditCallback", produces = "application/json")
    public String modifyAuditCallback3(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleModifyAuditPassCallBack(verified);
        return UMB_SUCCESS;
    }

    // 处理 签约回调
    //这里的回调地址是进件或者签约时上送的地址.务必保持两者一致.
    @PostMapping(value = "/CF3000053368/signCallback", produces = "application/json")
    public String signCallback0(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleSignCallBack(verified, umbParam);
        return UMB_SUCCESS;
    }

    @PostMapping(value = "/CF2002323280/signCallback", produces = "application/json")
    public String signCallback1(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleSignCallBack(verified, umbParam);
        return UMB_SUCCESS;
    }

    @PostMapping(value = "/CF2002323296/signCallback", produces = "application/json")
    public String signCallback2(@RequestParam String data) {
        String verified = verifyWithSm(data, umbParam.getMerchant_enc_sm2(), umbParam.getSm2pwd(), umbParam.getPlatform_sign_sm2());
        handleSignCallBack(verified, umbParam);
        return UMB_SUCCESS;
    }

    //TODO 优化两种返回类型的反序列化处理. 减少controller数量
    private void handleAuditPassCallBack(String string) {
        try {
            UMBReq.BodyAndHead<UMBAuditCallBackRes> bodyAndHead =
                    objectMapper.readValue(string, new TypeReference<UMBReq.BodyAndHead<UMBAuditCallBackRes>>() {
                    });
            executor.submit(() -> umbCallBackService.handleAuditPassCallBack(bodyAndHead));
        } catch (JsonProcessingException e) {
            throw new ContractBizException("反序列化中投科信 审核回调 失败", e);
        }
    }

    private void handleModifyAuditPassCallBack(String string) {
        try {
            UMBReq.BodyAndHead<UMBAuditCallBackRes> bodyAndHead =
                    objectMapper.readValue(string, new TypeReference<UMBReq.BodyAndHead<UMBAuditCallBackRes>>() {
                    });
            executor.submit(() -> umbCallBackService.handleModifyAuditPassCallBack(bodyAndHead));
        } catch (JsonProcessingException e) {
            throw new ContractBizException("反序列化中投科信 审核回调 失败", e);
        }
    }

    private void handleSignCallBack(String string, UMBParam umbParam) {
        try {
            UMBReq.BodyAndHead<UMBSignCallBackRes> bodyAndHead =
                    objectMapper.readValue(string, new TypeReference<UMBReq.BodyAndHead<UMBSignCallBackRes>>() {
                    });
            executor.submit(() -> umbCallBackService.handleSignCallBack(bodyAndHead, umbParam));
        } catch (JsonProcessingException e) {
            throw new ContractBizException("反序列化中投科信 签约回调 失败", e);
        }
    }


    public String verifyWithSm(String resMsg, String merchantEncFile, String merchantSm2Pwd, String platformSm2SignFile) {
        //非生产环境传入的数据为明文
        if (!envUtil.isProd()) {
            return resMsg;
        }
        String msg = null;
        try {
            msg = SM2Utils.openEnveloped(resMsg, merchantEncFile, merchantSm2Pwd);//返回数据
        } catch (Exception e) {
            e.getStackTrace();
        }
        log.info("验签 retdata：{}", msg);
        JSONObject obj = JSON.parseObject(msg, Feature.OrderedField);
        String messageStr = obj.getString("message");
        JSONObject messageObj = JSON.parseObject(messageStr, Feature.OrderedField);
        String dataStr = messageObj.getString("data");
        log.info("验签原串: {}", dataStr);
        String signStr = messageObj.getString("sign");
        try {
            log.info("读取到的平台公钥: {}", platformSm2SignFile);
            SM2Utils.P7VerifyMessageDetach(new String(Base64.encode(dataStr.getBytes(StandardCharsets.UTF_8))), signStr, platformSm2SignFile);
        } catch (Exception e) {
            log.error("验签异常", e);
        }
        return dataStr;
    }
}
