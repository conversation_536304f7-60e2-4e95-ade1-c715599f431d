package com.wosai.upay.job.refactor.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.ContractTaskProcessStatusEnum;
import com.shouqianba.cua.utils.thread.CollectionWorker;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * 抽象进件任务处理
 *
 * <AUTHOR>
 * @date 2024/5/13 09:56
 */
@Slf4j
public class AbstractContractTaskHandler {

    @Resource
    protected ContractTaskDAO contractTaskDAO;

    @Autowired
    protected MerchantService merchantService;

    private static final Long DEFAULT_BEGIN_TIME_GAP = 86400000L;

    private static final Integer DEFAULT_TASK_NUM = 10;

    /**
     * 批量处理主任务
     *
     * @param taskType       任务类型
     * @param beginTimeGap   开始时间间隔
     * @param parallel       是否开启并行 true-并行
     * @param sync           是否同步执行 true-同步
     * @param taskNum        任务数量
     * @param taskDOConsumer 任务处理
     */
    protected void batchProcessPrimaryTasks(String taskType, Integer taskStatus,
                                            Long beginTimeGap , Integer taskNum,
                                            boolean parallel, boolean sync,
                                            Consumer<ContractTaskDO> taskDOConsumer) {
        beginTimeGap = Objects.isNull(beginTimeGap) ? DEFAULT_BEGIN_TIME_GAP : beginTimeGap;
        taskNum = Objects.isNull(taskNum) ? DEFAULT_TASK_NUM : taskNum;
        List<ContractTaskDO> contractTaskDOS = contractTaskDAO.listContractTasksWithLimit(taskType,
                new Timestamp(System.currentTimeMillis() - beginTimeGap), taskStatus, taskNum);
        if (CollectionUtils.isEmpty(contractTaskDOS)) {
            return;
        }
        if (sync) {
            if (parallel) {
                CollectionWorker.of(contractTaskDOS).parallel(McJobThreadPoolFactory.getInstance()).forEach(t -> processSinglePrimaryTask(t, taskDOConsumer));
            } else {
                contractTaskDOS.forEach(t -> processSinglePrimaryTask(t, taskDOConsumer));
            }
            return;
        }
        if (parallel) {
            ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> {
                // 这里上下线程共用一个线程池,是否会发生意想不到的错误?
                CollectionWorker.of(contractTaskDOS).parallel(McJobThreadPoolFactory.getInstance()).forEach(t -> processSinglePrimaryTask(t, taskDOConsumer));
            });
        } else {
            ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> contractTaskDOS.forEach(t -> processSinglePrimaryTask(t, taskDOConsumer)));
        }
    }


    private void processSinglePrimaryTask(ContractTaskDO contractTaskDO, Consumer<ContractTaskDO> taskDOConsumer) {
        try {
            taskDOConsumer.accept(contractTaskDO);
        } catch (Exception e) {
            log.error("process single primary task error", e);
            contractTaskDO.setStatus(ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue());
            HashMap<String, String> map = Maps.newHashMap();
            map.put("result", e.getMessage());
            contractTaskDO.setResult(JSON.toJSONString(map));
            contractTaskDAO.updateByPrimaryKeySelective(contractTaskDO);
        }
    }



}
