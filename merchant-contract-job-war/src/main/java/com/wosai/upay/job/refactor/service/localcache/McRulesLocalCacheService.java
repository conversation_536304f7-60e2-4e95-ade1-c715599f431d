package com.wosai.upay.job.refactor.service.localcache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.upay.job.refactor.biz.rule.GroupCombinedStrategyBiz;
import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDAO;
import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDetailDAO;
import com.wosai.upay.job.refactor.dao.GroupRouteRuleDetailDAO;
import com.wosai.upay.job.refactor.dao.GroupRouteRulesDecisionDAO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRuleDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRulesDecisionDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 进件规则本地缓存服务
 *
 * <AUTHOR>
 * @date 2023/11/28 15:04
 */
@Slf4j
@Service
public class McRulesLocalCacheService {

    @Resource
    private GroupRouteRulesDecisionDAO groupRouteRulesDecisionDAO;

    @Resource
    private GroupRouteRuleDetailDAO groupRouteRuleDetailDAO;

    @Resource
    private GroupCombinedStrategyDAO groupCombinedStrategyDAO;

    @Resource
    private GroupCombinedStrategyDetailDAO groupCombinedStrategyDetailDAO;


    private static final Long DEFAULT_DURATION = 5L;

    private static final Integer INITIAL_CAPACITY = 100;

    private static final Integer MAXIMUM_SIZE = 2000;

    private static final Long DEFAULT_KEY = 1L;

    private static final String ALL_DECISION_KEY = "rule:decision:all";


    private final Cache<Long, List<GroupRouteRuleDetailDO>> ruleDetailCache =
            Caffeine.newBuilder()
                    .initialCapacity(INITIAL_CAPACITY)
                    .maximumSize(MAXIMUM_SIZE)
                    .expireAfterWrite(DEFAULT_DURATION, TimeUnit.MINUTES).build();

    private final Cache<String, List<GroupRouteRulesDecisionDO>> rulesDecisionCache =
            Caffeine.newBuilder()
                    .initialCapacity(INITIAL_CAPACITY)
                    .maximumSize(MAXIMUM_SIZE)
                    .expireAfterWrite(DEFAULT_DURATION, TimeUnit.MINUTES).build();

    private final Cache<Long, GroupCombinedStrategyDO> combinedStrategyCache =
            Caffeine.newBuilder()
                    .initialCapacity(INITIAL_CAPACITY)
                    .maximumSize(MAXIMUM_SIZE)
                    .expireAfterWrite(DEFAULT_DURATION, TimeUnit.MINUTES).build();

    private final Cache<Long, List<GroupCombinedStrategyDetailDO>> combinedStrategyDetailCache =
            Caffeine.newBuilder()
                    .initialCapacity(INITIAL_CAPACITY)
                    .maximumSize(MAXIMUM_SIZE)
                    .expireAfterWrite(DEFAULT_DURATION, TimeUnit.MINUTES).build();


    /**
     * 从缓存中获取有效的规则决策
     *
     * @return 规则决策
     */
    public List<GroupRouteRuleDetailDO> listAllValidRuleDetails() {
        return ruleDetailCache.get(DEFAULT_KEY, defaultKey -> groupRouteRuleDetailDAO.listAllValid());
    }

    /**
     * 从缓存中获取规则详情
     *
     * @return 规则详情
     */
    public List<GroupRouteRulesDecisionDO> listAllValidRuleDecisions() {
        return rulesDecisionCache.get(ALL_DECISION_KEY, t -> groupRouteRulesDecisionDAO.listAllValid());
    }


    /**
     * 根据进件报备规则组策略组合主键id获取
     *
     * @param id 主键id
     * @return 进件报备规则组策略组合
     */
    public Optional<GroupCombinedStrategyDO> getCombinedStrategyById(Long id) {
        return Optional.ofNullable(combinedStrategyCache.get(id, t -> groupCombinedStrategyDAO.getByPrimaryKey(t).orElse(null)));
    }


    /**
     * 根据进件报备规则组策略组合id获取策略组合detail
     *
     * @param strategyId 策略组合id
     * @return 策略组合detail列表
     */
    public List<GroupCombinedStrategyDetailDO> listStrategyDetailByStrategyId(Long strategyId) {
        if (Objects.isNull(strategyId)) {
            return Collections.emptyList();
        }
        return combinedStrategyDetailCache.get(strategyId, t -> groupCombinedStrategyDetailDAO
                .listByStrategyId(t).stream().filter(detailDO -> Objects.equals(detailDO.getValidStatus(), ValidStatusEnum.VALID.getValue()))
                .collect(Collectors.toList()));
    }

    /**
     * 刷新所有缓存
     * 当数据发生变动时调用此方法，确保缓存与数据库保持同步
     */
    public void refreshAllCaches() {
        try {
            log.info("开始刷新所有规则缓存");
            refreshRuleDetailCache();
            refreshRulesDecisionCache();
            refreshCombinedStrategyCache();
            refreshCombinedStrategyDetailCache();
            log.info("所有规则缓存刷新完成");
        } catch (Exception e) {
            log.error("刷新所有规则缓存失败", e);
        }
    }

    /**
     * 刷新规则详情缓存
     */
    public void refreshRuleDetailCache() {
        try {
            log.info("刷新规则详情缓存");
            ruleDetailCache.invalidate(DEFAULT_KEY);
            listAllValidRuleDetails();
        } catch (Exception e) {
            log.error("刷新规则详情缓存失败", e);
        }
    }

    /**
     * 刷新规则决策缓存
     */
    public void refreshRulesDecisionCache() {
        try {
            log.info("刷新规则决策缓存");
            rulesDecisionCache.invalidate(ALL_DECISION_KEY);
            listAllValidRuleDecisions();
        } catch (Exception e) {
            log.error("刷新规则决策缓存失败", e);
        }
    }

    /**
     * 刷新组合策略缓存
     * 
     * @param id 策略ID，如果为null则刷新所有
     */
    public void refreshCombinedStrategyCache(Long id) {
        try {
            if (id == null) {
                combinedStrategyCache.invalidateAll();
            } else {
                combinedStrategyCache.invalidate(id);
            }
        } catch (Exception e) {
            log.error("刷新组合策略缓存失败, id={}", id, e);
        }
    }

    /**
     * 刷新所有组合策略缓存
     */
    public void refreshCombinedStrategyCache() {
        refreshCombinedStrategyCache(null);
    }

    /**
     * 刷新组合策略详情缓存
     * 
     * @param strategyId 策略ID，如果为null则刷新所有
     */
    public void refreshCombinedStrategyDetailCache(Long strategyId) {
        try {
            if (strategyId == null) {
                combinedStrategyDetailCache.invalidateAll();
            } else {
                combinedStrategyDetailCache.invalidate(strategyId);
            }
        } catch (Exception e) {
            log.error("刷新组合策略详情缓存失败, strategyId={}", strategyId, e);
        }
    }

    /**
     * 刷新所有组合策略详情缓存
     */
    public void refreshCombinedStrategyDetailCache() {
        refreshCombinedStrategyDetailCache(null);
    }
}
