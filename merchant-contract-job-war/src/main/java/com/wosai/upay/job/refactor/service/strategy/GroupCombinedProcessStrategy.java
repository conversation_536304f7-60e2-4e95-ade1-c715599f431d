package com.wosai.upay.job.refactor.service.strategy;

import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.enums.GroupCombinedTypeEnum;

import java.util.List;
import java.util.Optional;

/**
 * 报备规则组策略组合处理策略
 *
 * <AUTHOR>
 */
public interface GroupCombinedProcessStrategy extends Strategy<GroupCombinedTypeEnum, Integer> {

    /**
     * 获取符合的收单机构进件规则detail
     *
     * @param groupCombinedStrategies 规则组对应的策略组合detail
     * @return 符合条件的策略组合detail
     */
    List<GroupCombinedStrategyDetailDO> listSatisfactionDetails(List<GroupCombinedStrategyDetailDO> groupCombinedStrategies);

    /**
     * 根据报备规则组策略组合detail获取进件报备规则组策略
     *
     * @param groupCombinedStrategyDetailDOS 进件报备规则组策略组合detail
     * @return 进件报备规则组策略
     */
    Optional<GroupCombinedStrategyDO> getGroupCombineStrategy(List<GroupCombinedStrategyDetailDO> groupCombinedStrategyDetailDOS);
}
