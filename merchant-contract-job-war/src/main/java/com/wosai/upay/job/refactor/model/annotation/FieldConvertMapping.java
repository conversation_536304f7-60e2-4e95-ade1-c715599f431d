package com.wosai.upay.job.refactor.model.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段转换映射注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface FieldConvertMapping {

    /**
     * 字段值来源
     *
     * @return 字段值来源
     */
    String source();
}
