package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 内部调度主任务状态枚举
 *
 * <AUTHOR>
 */
public enum InternalScheduleMainTaskStatusEnum implements ITextValueEnum<Integer> {

    /**
     * 待处理
     */
    WAIT_PROCESS(0, "待处理"),

    /**
     * 处理中
     */
    BEING_PROCESSING(1, "处理中"),

    /**
     * 等待下次调度
     */
    WAIT_NEXT_SCHEDULED(2, "等待下次调度"),

    /**
     * 处理失败
     */
    PROCESS_FAIL(3, "处理失败"),

    /**
     * 处理成功
     */
    PROCESS_SUCCESS(4, "处理成功");


    private final Integer value;
    private final String text;

    InternalScheduleMainTaskStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
