package com.wosai.upay.job.repository;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.mapper.ProviderTerminalMapper;
import com.wosai.upay.job.mapper.ProviderTerminalTaskMapper;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.model.ProviderTerminal;
import com.wosai.upay.job.model.dto.ProviderTerminalContext;
import com.wosai.upay.job.util.ChatBotUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.upay.job.constant.ProviderTerminalConstants.FAIL;
import static com.wosai.upay.job.constant.ProviderTerminalConstants.PROCESSING;
import static com.wosai.upay.job.util.StringUtil.localDateTimeToDate;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/3/3 4:16 下午
 */
@Component
@Slf4j
public class ProviderTerminalTaskRepository {

    @Autowired
    private ProviderTerminalTaskMapper terminalTaskMapper;

    @Autowired
    private ProviderTerminalMapper providerTerminalMapper;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    public static final String boundTerminalSystemFormat = "=子商户号绑定系统异常 重试后仍失败= 商户SN:%s,子商户号:%s";
    public static final String UNBOUND_TERMINAL_SYSTEM_FORMAT = "=子商户号解绑系统异常 重试后仍失败= 商户SN:%s,子商户号:%s";


    /**
     * 设置缓存,写入3后自动删除
     */
    Cache<String, Boolean> existCache = CacheBuilder.newBuilder()
            .maximumSize(4000)
            .expireAfterWrite(3, TimeUnit.SECONDS)
            .build();

    /**
     * 新增子商户绑定所有终端
     *
     * @param merchantSn  商户号
     * @param subMerchant 子商户号
     * @param provider
     * @param payWay      支付方式
     * @param type        1-"新增子商户号绑定所有终端",2-"新增终端绑定所有子商户号",3-解绑当前终端对应的所有子商户号,4-"新增门店级别终端绑定所有子商户号"
     * @param terminalSn  收钱吧对应的终端号
     */
    public void addBoundTerminalTask(String merchantSn, String subMerchant, int provider, int payWay, Integer type, String providerTerminalId, String storeSn, String terminalSn, String brandMerchantSn) {
        // 检查是否已经绑定
        if (!WosaiStringUtils.isEmptyAny(subMerchant, providerTerminalId) && !Objects.equals(type, ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType())) {
            List<ProviderTerminal> providerTerminals = Optional.ofNullable(providerTerminalMapper.selectByCondition(new ProviderTerminal().setProvider_terminal_id(providerTerminalId)))
                    .orElseGet(ArrayList::new);
            //商户级别
            if(ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType() == type) {
                providerTerminals = providerTerminals.stream().filter(ter -> StrUtil.isAllBlank(ter.getStore_sn(),ter.getTerminal_sn())).collect(Collectors.toList());
            }
            if(ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType() == type) {
                providerTerminals = providerTerminals.stream().filter(ter -> StrUtil.isAllNotBlank(ter.getStore_sn(),ter.getTerminal_sn())).collect(Collectors.toList());
            }
            if(ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType() == type) {
                providerTerminals = providerTerminals.stream().filter(ter -> StrUtil.isAllNotBlank(ter.getStore_sn())).collect(Collectors.toList());
            }
            if (WosaiCollectionUtils.isNotEmpty(providerTerminals)) {
                boolean anyMatch = providerTerminals.stream().anyMatch(providerTerminal ->
                        WosaiStringUtils.isNotEmpty(providerTerminal.getBound_sub_mch_ids())
                                && providerTerminal.getBound_sub_mch_ids().contains(subMerchant)
                                && Objects.equals(providerTerminalId,providerTerminal.getProvider_terminal_id())
                );
                if (anyMatch) {
                    return;
                }
            }
        }

        ProviderTerminalContext context = ProviderTerminalContext.builder()
                .subMerchant(subMerchant)
                .provider(provider)
                .payWay(payWay)
                .providerTerminalId(providerTerminalId)
                .sqbStoreSn(storeSn)
                .terminalSn(terminalSn)
                .brandMerchantSn(brandMerchantSn)
                .build();
        //是否存在相同的任务,避免重复创建
        final List<ProviderTerminalTask> taskList = terminalTaskMapper.selectByCondition(
                new ProviderTerminalTask()
                        .setMerchant_sn(merchantSn)
                        .setType(type)
        );
        final boolean match = taskList.stream()
                .filter(task -> !Objects.equals(task.getStatus(), FAIL))
                .anyMatch(task -> {
                    final ProviderTerminalContext contextInfo = task.getContextInfo();
                    return Objects.equals(JSONObject.toJSONString(context), JSONObject.toJSONString(contextInfo));
                });
        if (match) {
            return;
        }
        //不存在则插入
        ProviderTerminalTask task = new ProviderTerminalTask();
        task.setMerchant_sn(merchantSn);
        task.setType(type);
        task.setContext(JSONObject.toJSONString(context));
//        华夏对于同一个终端需要间隔一段时间执行所以出现一下逻辑
        if(Objects.equals(provider,ProviderEnum.PROVIDER_HXB.getValue()) && WosaiStringUtils.isNotEmpty(providerTerminalId)) {
            Boolean present =
                    existCache.getIfPresent(providerTerminalId);
            if (Objects.isNull(present) || Objects.equals(present, Boolean.FALSE)) {
                //设置已经存在
                existCache.put(providerTerminalId, Boolean.TRUE);
            } else {
                Random random = new Random();
                int delaySeconds = random.nextInt(30) + applicationApolloConfig.getDelayTerminalTask();
                //设置任务推迟时间
                task.setPriority(DateUtils.addSeconds(new Date(), delaySeconds));
            }
        }
        terminalTaskMapper.insertSelective(task);
    }

    /**
     * 新增子商户绑定所有终端
     *
     * @param merchantSn  商户号
     * @param subMerchant 子商户号
     * @param provider
     * @param payWay      支付方式
     * @param type        1-"新增子商户号绑定所有终端",2-"新增终端绑定所有子商户号",3-解绑当前终端对应的所有子商户号,4-"新增门店级别终端绑定所有子商户号"
     * @param terminalSn  收钱吧对应的终端号
     */
    public void addBoundTerminalTask(String merchantSn, String subMerchant, int provider, int payWay, Integer type, String providerTerminalId, String storeSn, String terminalSn) {
        addBoundTerminalTask(merchantSn, subMerchant, provider, payWay, type, providerTerminalId, storeSn, terminalSn, null);
    }


    /**
     * 更新task状态
     *
     * @param id
     * @param status
     * @param message
     */
    public void updateTaskStatusById(Long id, int status, String message) {
        ProviderTerminalTask update = new ProviderTerminalTask();
        update.setId(id);
        update.setStatus(status);
        update.setResult(message);
        terminalTaskMapper.updateByPrimaryKeySelective(update);
    }

    public void addProviderTerminalBindTaskForNewSubMch(String merchantSn, String subMerchant, ProviderEnum providerEnum, PaywayEnum paywayEnum) {
        ProviderTerminalContext context = ProviderTerminalContext.builder()
                .subMerchant(subMerchant)
                .provider(providerEnum.getValue())
                .payWay(paywayEnum.getValue())
                .build();
        terminalTaskMapper.insertSelective(new ProviderTerminalTask()
                .setMerchant_sn(merchantSn)
                .setType(ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType())
                .setContext(JSONObject.toJSONString(context)));
    }


    /**
     * 存在 绑定终端系统异常
     *
     * @param task
     */
    public void exitBoundTerminalException(ProviderTerminalTask task, String subMerchant) {
        int retry = task.getRetry();
        retry++;
        final Integer provider = task.getContextInfo().getProvider();
        final Integer taskType = task.getType();
        //默认处理10次
        if (retry >= 10) {
            //修改状态为失败 钉钉通知
            ProviderTerminalTask updateFail = new ProviderTerminalTask();
            updateFail.setId(task.getId());
            updateFail.setStatus(FAIL);
            updateFail.setResult(Objects.equals(taskType, ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType()) ? "子商户号解绑失败" : "子商户号绑定终端失败");
            updateFail.setRetry(retry);
            terminalTaskMapper.updateByPrimaryKeySelective(updateFail);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format(Objects.equals(taskType, ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType()) ? UNBOUND_TERMINAL_SYSTEM_FORMAT : boundTerminalSystemFormat,
                    task.getMerchant_sn(), subMerchant));
        } else {
            ProviderTerminalTask updateProcessing = new ProviderTerminalTask();
            updateProcessing.setId(task.getId());
            updateProcessing.setStatus(PROCESSING);
            updateProcessing.setRetry(retry);
            updateProcessing.setResult(Objects.equals(taskType, ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType()) ? "子商户号解绑终端处理中" : "子商户号绑定终端处理中");
            if(Objects.equals(provider, ProviderEnum.PROVIDER_PSBC.getValue())) {
                int addTime = retry ;
                updateProcessing.setPriority(localDateTimeToDate(LocalDateTime.now().plusMinutes(addTime * 30)));
            } else {
                int addMin = retry <= 5 ? retry * 2 : 10;
                updateProcessing.setPriority(localDateTimeToDate(LocalDateTime.now().plusMinutes(addMin)));
            }
            terminalTaskMapper.updateByPrimaryKeySelective(updateProcessing);
        }
    }
}
