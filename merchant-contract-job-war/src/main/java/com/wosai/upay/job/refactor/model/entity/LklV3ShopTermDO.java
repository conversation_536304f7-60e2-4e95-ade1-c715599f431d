package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 表实体对象
 *
 * <AUTHOR>
 */
@TableName("lkl_v3_shop_term")
@Data
public class LklV3ShopTermDO {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private String id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 门店号
     */
    @TableField(value = "store_sn")
    private String storeSn;
    /**
     * 拉卡拉网点号
     */
    @TableField(value = "shopid")
    private String shopid;
    /**
     * 终端信息
     */
    @TableField(value = "lkl_v3_term")
    private String lklV3Term;
    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    private Timestamp createAt;
    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    private Timestamp updateAt;


}

