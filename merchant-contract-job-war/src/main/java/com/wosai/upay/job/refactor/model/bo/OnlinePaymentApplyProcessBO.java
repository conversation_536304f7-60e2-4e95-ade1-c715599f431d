package com.wosai.upay.job.refactor.model.bo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/12/25
 */
@Data
@Accessors(chain = true)
public class OnlinePaymentApplyProcessBO {

    public static final String CONTRACTING = "contracting";
    public static final String AUTHING = "authing";
    public static final String AUDITING = "auditing";
    public static final String SUCCESS = "success";

    /**
     * 处于哪个阶段
     * contracting 报备中
     * authing 认证中
     * success 成功
     */
    private String stage;

    /**
     * 当前阶段是否完成
     * true完成 false未完成
     */
    private Boolean finish;
    /**
     * 阶段名称
     */
    private String stageName;
    /**
     * 阶段展示的文案
     */
    private String message;
}
