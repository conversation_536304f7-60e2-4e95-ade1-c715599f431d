package com.wosai.upay.job.refactor.event;

import com.wosai.upay.job.model.DO.MerchantProviderParams;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
@Getter
public class TradeParamsChangeEvent extends ApplicationEvent {

    /**
     * 切换成功的参数
     */
    private MerchantProviderParams merchantProviderParams;

    /**
     * 切换的业务方
     */
    private String tradeAppId;

    /**
     * 商户id
     */
    private String merchantId;

    private boolean checkSettlement;

    public TradeParamsChangeEvent(Object source, MerchantProviderParams merchantProviderParams, String tradeAppId, String merchantId, boolean checkSettlement) {
        super(source);
        this.merchantProviderParams = merchantProviderParams;
        this.tradeAppId = tradeAppId;
        this.merchantId = merchantId;
        this.checkSettlement = checkSettlement;
    }
}
