package com.wosai.upay.job.refactor.service.impl.task;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.refactor.task.TrackContractTaskResultTask;
import com.wosai.upay.job.service.task.TrackContractTaskResultTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 进件任务结果回显审批任务服务
 *
 * <AUTHOR>
 * @date 2024/11/11 11:16
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class TrackContractTaskResultTaskServiceImpl implements TrackContractTaskResultTaskService {

    @Resource
    private TrackContractTaskResultTask trackContractTaskResultTask;

    /**
     * 新增进件任务结果回显审批任务
     *
     * @param merchantSn     商户号
     * @param auditId        审批id
     * @param contractTaskId 进件任务id
     */
    @Override
    public void insertTask(int businessSceneType, String merchantSn, String auditId, Long contractTaskId, Long templateId) {
        trackContractTaskResultTask.insertTask(businessSceneType, merchantSn, auditId, contractTaskId, templateId);
    }
}
