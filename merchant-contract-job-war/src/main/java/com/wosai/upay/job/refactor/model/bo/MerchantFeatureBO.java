package com.wosai.upay.job.refactor.model.bo;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.upay.job.model.MerchantFeatureBaseDTO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;

/**
 * 商户特征信息
 *
 * <AUTHOR>
 * @date 2023/11/24 10:45
 */
@Setter
public class MerchantFeatureBO extends MerchantFeatureBaseDTO {

    public String getName() {
        return StringExtensionUtils.emptyToDefault(name, StringUtils.EMPTY);
    }

    public String getProvinceName() {
        return StringExtensionUtils.emptyToDefault(provinceName, StringUtils.EMPTY);
    }

    public String getCityName() {
        return StringExtensionUtils.emptyToDefault(cityName, StringUtils.EMPTY);
    }

    public String getIndustry() {
        return StringExtensionUtils.emptyToDefault(industry, StringUtils.EMPTY);
    }

    public String getPromotionOrganizationPath() {
        return StringExtensionUtils.emptyToDefault(promotionOrganizationPath, StringUtils.EMPTY);
    }

    public String getType() {
        return StringExtensionUtils.emptyToDefault(type, StringUtils.EMPTY);
    }

    public String getDistrictCode() {
        return StringExtensionUtils.emptyToDefault(districtCode, StringUtils.EMPTY);
    }

    public String getProvinceCode() {
        return StringExtensionUtils.emptyToDefault(provinceCode, StringUtils.EMPTY);
    }

    public String getCityCode() {
        return StringExtensionUtils.emptyToDefault(cityCode, StringUtils.EMPTY);
    }

    public String getBankAccountType() {
        return StringExtensionUtils.emptyToDefault(bankAccountType, StringUtils.EMPTY);
    }

    public String getAcquirer() {
        return StringExtensionUtils.emptyToDefault(acquirer, StringUtils.EMPTY);
    }

    public String getPaymentMode() {
        return StringExtensionUtils.emptyToDefault(paymentMode, StringUtils.EMPTY);
    }

    public String getLegalPersonType() {
        return StringExtensionUtils.emptyToDefault(legalPersonType, StringUtils.EMPTY);
    }

    public String getSettlementAccountType() {
        return StringExtensionUtils.emptyToDefault(settlementAccountType, StringUtils.EMPTY);
    }

    public String getOrganizationPath() {
        return StringExtensionUtils.emptyToDefault(organizationPath, StringUtils.EMPTY);
    }

    public String getPersonalCertificateType() {
        return StringExtensionUtils.emptyToDefault(personalCertificateType, StringUtils.EMPTY);
    }

    public String getOpenedBusinessAppIdListJson() {
        return StringExtensionUtils.emptyToDefault(openedBusinessAppIdListJson, StringUtils.EMPTY);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
