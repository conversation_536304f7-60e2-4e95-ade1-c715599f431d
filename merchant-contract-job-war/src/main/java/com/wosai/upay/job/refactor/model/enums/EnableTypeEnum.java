package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 可以类型枚举
 *
 * <AUTHOR>
 */
public enum EnableTypeEnum implements ITextValueEnum<Integer> {


    ENABLED(0, "可以"),


    NOT_ENABLED(1, "不可以");


    private final Integer value;
    private final String text;

    EnableTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
