package com.wosai.upay.job.refactor.config.datasource;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.github.pagehelper.PageInterceptor;
import com.wosai.core.crypto.client.CryptoClient;
import com.wosai.core.crypto.interceptor.CryptoAwareInterceptor;
import com.wosai.core.crypto.service.CryptoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;

import java.util.Properties;

/**
 * mybatis-plus配置
 *
 * <AUTHOR>
 * @date 2023/11/20 14:32
 */
@Configuration
public class MyBatisPlusConfig {


    @Autowired
    private CryptoService cryptoService;

    @Value("${merchant-contract.access_id}")
    private String accessId;

    @Value("${merchant-contract.access_secret}")
    private String accessSecret;

    /**
     * 兼容已有查询,关闭驼峰映射
     *
     * @return mybatis配置
     */
    @Bean
    public ConfigurationCustomizer mybatisConfigurationCustomizer() {
        return configuration -> configuration.setMapUnderscoreToCamelCase(false);
    }

    /**
     * 兼容分页插件
     *
     * @return 插件拦截器
     */
    @Bean
    PageInterceptor pageInterceptor(){
        PageInterceptor pageInterceptor = new PageInterceptor();
        Properties properties = new Properties();
        properties.setProperty("helperDialect", "mysql");
        pageInterceptor.setProperties(properties);
        return pageInterceptor;
    }

    /**
     * 确保cryptoAwareInterceptor在pageInterceptor的后面，执行的时候会cryptoInterceptor先执行
     */
    @Bean
    @Profile(value = {"default","beta","beta-copy","prod"})
    public CryptoAwareInterceptor cryptoAwareInterceptor() {
        CryptoClient cryptoClient = new CryptoClient(cryptoService, accessId, accessSecret);
        CryptoAwareInterceptor cryptoAwareInterceptor = new CryptoAwareInterceptor();
        cryptoAwareInterceptor.init(cryptoClient);
        return cryptoAwareInterceptor;
    }

}
