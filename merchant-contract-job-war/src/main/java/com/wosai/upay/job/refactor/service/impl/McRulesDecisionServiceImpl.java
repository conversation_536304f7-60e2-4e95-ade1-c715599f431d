package com.wosai.upay.job.refactor.service.impl;

import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.utils.json.JSON;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.shouqianba.cua.utils.stream.ExtCollectors;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.rule.GroupCombinedStrategyBiz;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.McRuleGroupDAO;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.ContractRuleDecisionEvaluateResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeaturePropertyBO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.model.enums.*;
import com.wosai.upay.job.refactor.service.McRulesDecisionService;
import com.wosai.upay.job.refactor.service.localcache.McRulesLocalCacheService;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;


/**
 * 进件规则决策表表Service层 {@link GroupRouteRulesDecisionDO}
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class McRulesDecisionServiceImpl implements McRulesDecisionService {

    private final AcquirerFacade acquirerFacade;
    private final McRulesLocalCacheService mcRulesLocalCacheService;
    private final GroupCombinedStrategyBiz groupCombinedStrategyBiz;
    private final ApplicationApolloConfig applicationApolloConfig;
    private final McRuleGroupDAO mcRuleGroupDAO;
    private final MerchantFeatureExtractor merchantFeatureExtractor;
    private final ContractStatusDAO contractStatusDAO;

    private EnumMap<LogicalOperationTypeEnum, BiFunction<MerchantFeaturePropertyBO, String, Boolean>> logicalOperationFunctionMap;

    @Autowired
    public McRulesDecisionServiceImpl(
            AcquirerFacade acquirerFacade,
            McRulesLocalCacheService mcRulesLocalCacheService,
            GroupCombinedStrategyBiz groupCombinedStrategyBiz,
            ApplicationApolloConfig applicationApolloConfig,
            McRuleGroupDAO mcRuleGroupDAO,
            MerchantFeatureExtractor merchantFeatureExtractor, ContractStatusDAO contractStatusDAO) {
        this.acquirerFacade = acquirerFacade;
        this.mcRulesLocalCacheService = mcRulesLocalCacheService;
        this.groupCombinedStrategyBiz = groupCombinedStrategyBiz;
        this.applicationApolloConfig = applicationApolloConfig;
        this.mcRuleGroupDAO = mcRuleGroupDAO;
        this.merchantFeatureExtractor = merchantFeatureExtractor;
        this.contractStatusDAO = contractStatusDAO;
    }

    @PostConstruct
    private void initLogicalOperationFunctionMap() {
        this.logicalOperationFunctionMap = LogicalOperationMatcher.initLogicalOperationFunctionMap();
    }

    /**
     * 根据商户号和所属推广组织获取进件通道组
     *
     * @param merchantSn     商户号
     * @param organizationId 组织id
     * @param netInScene     入网场景
     * @return 进件报备规则组策略组合detail列表
     */
    @Override
    @SuppressWarnings("java:S3516")
    public Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> chooseGroupBySnAndOrg(String merchantSn, String organizationId, NetInSceneEnum netInScene){
        if (StringUtils.isBlank(merchantSn)) {
            return new Tuple2<>(Collections.emptySet(), Collections.emptySet());
        }
        return chooseGroupByMerchantFeature(getMerchantFeature(merchantSn, organizationId, netInScene));
    }


    private MerchantFeatureBO getMerchantFeature(String merchantSn, String organizationId, NetInSceneEnum netInScene) {
        return merchantFeatureExtractor.getMerchantFeature(merchantSn, organizationId, netInScene);
    }

    private MerchantFeatureBO getMerchantFeatureByStoreId(String merchantSn, String storeId, NetInSceneEnum netInScene) {
        return merchantFeatureExtractor.getMerchantFeatureByStoreId(merchantSn,storeId, netInScene);
    }




    /**
     * 根据商户特征获取进件通道组
     *
     * @param merchantFeature 商户特征
     * @return 进件报备规则组策略组合detail列表
     */
    @Override
    public Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> chooseGroupByMerchantFeature(MerchantFeatureBO merchantFeature){
        if (Objects.isNull(merchantFeature)) {
            return new Tuple2<>(Collections.emptySet(), Collections.emptySet());
        }
        // 默认入网场景
        if (StringUtils.isBlank(merchantFeature.getNetInScene())) {
            merchantFeature.setNetInScene(NetInSceneEnum.BUSINESS_OPENING.getValue());
        }
        List<GroupRouteRulesDecisionDO> mcRulesDecisionList = listAllValidMcRulesDecisionDOS();
        List<GroupRouteRulesDecisionDO> topLevelRulesDecisions = mcRulesDecisionList
                .stream()
                .filter(t -> Objects.equals(t.getParentId(), 0L))
                .sorted(Comparator.comparingInt(GroupRouteRulesDecisionDO::getPriority))
                .collect(Collectors.toList());
        Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> result = loopRuleDecisionsReturnGroupDetails(merchantFeature, topLevelRulesDecisions);
        log.info("根据商户特征和进件规则匹配收单机构,merchantSn:{}, 商户特征:{}, 匹配结果: {}", merchantFeature.getMerchantSn(), merchantFeature, JSON.toJSONString(result));
        return result;
    }

    private Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>>  loopRuleDecisionsReturnGroupDetails(MerchantFeatureBO merchantFeature, List<GroupRouteRulesDecisionDO> topLevelRulesDecisions) {
        Set<String> forbiddenGroupIdSets = new HashSet<>();
        Set<String> forbiddenMessageSets = new HashSet<>();
        for (GroupRouteRulesDecisionDO decision : topLevelRulesDecisions) {
            log.debug("rule decision, priority={}, name={}, id={}, forbiddenGroupIdSets={}", decision.getPriority(), decision.getName(), decision.getId(), forbiddenGroupIdSets);
            ContractRuleDecisionEvaluateResultBO evaluateResult = evaluateRuleDecisionNodeWithCatchException(decision, merchantFeature);
            if (evaluateResult.notPass()) {
                continue;
            }
            Set<GroupCombinedStrategyDetailDO> validStrategyDetails = listValidAndSortedGroupCombinesDetails(decision, merchantFeature, forbiddenGroupIdSets, evaluateResult, forbiddenMessageSets);
            if (isChooseType(decision, ChooseTypeEnum.ONLY_CAN) || (isChooseType(decision, ChooseTypeEnum.ENABLE) && !validStrategyDetails.isEmpty())) {
                log.info("商户命中规则: sn={}, rule_name={}, 规则决策明细: {}, 商户特征: {}", merchantFeature.getMerchantSn(), decision.getName(), evaluateResult, merchantFeature);
                return new Tuple2<>(validStrategyDetails, forbiddenMessageSets);
            }
        }
        log.warn("商户进件规则匹配未命中任何收单机构, merchantSn:{}, forbiddenGroupIdSets:{}", merchantFeature.getMerchantSn(), forbiddenGroupIdSets);
        Set<GroupCombinedStrategyDetailDO> groupCombinedStrategyDetailDOS = defaultWhenNoMatchedGroup(forbiddenGroupIdSets, merchantFeature, forbiddenMessageSets).<Set<GroupCombinedStrategyDetailDO>>map(Sets::newHashSet).orElseGet(Sets::newHashSet);
        return new Tuple2<>(groupCombinedStrategyDetailDOS, forbiddenMessageSets);
    }

    public Optional<GroupCombinedStrategyDetailDO> defaultWhenNoMatchedGroup(Set<String> forbiddenGroupIdSets, MerchantFeatureBO merchantFeature, Set<String> evaluateResultBOs) {
        GroupCombinedStrategyDetailDO groupCombinedStrategyDetailDO = new GroupCombinedStrategyDetailDO();
        List<String> inUseDefaultMcRuleGroup = applicationApolloConfig.listInUseDefaultMcRuleGroup();
        Map<String, String> acquirerToDefaultRuleGroupIdMap = mcRuleGroupDAO.listAll().stream()
                .filter(t -> inUseDefaultMcRuleGroup.contains(t.getGroupId())
                        && !forbiddenGroupIdSets.contains(t.getGroupId())
                        && Objects.equals(t.getStatus(), ValidStatusEnum.VALID.getValue())
                        && Objects.equals(t.getDefaultStatus(), DefaultStatusEnum.DEFAULT.getValue())
                        && acquirerFacade.getStrategy(t.getAcquirer()).map(strategy -> {
                    ContractGroupRuleVerifyResultBO contractGroupRuleVerifyResultBO = strategy.checkSatisfactionToAcquirerTemplate(merchantFeature);
                    if (!contractGroupRuleVerifyResultBO.isCheckPass()) {
                        evaluateResultBOs.add(contractGroupRuleVerifyResultBO.getMessage());
                    }
                    return  contractGroupRuleVerifyResultBO.isCheckPass();
                }).orElse(true))
                .collect(ExtCollectors.toMap(McRuleGroupDO::getAcquirer, McRuleGroupDO::getGroupId, (existing, replacement) -> existing));
        if (MapUtils.isEmpty(acquirerToDefaultRuleGroupIdMap)) {
            log.warn("defaultWhenNoMatchedGroup, 收单机构选取为空, merchantSn:{}", merchantFeature.getMerchantSn());
            return Optional.empty();
        }
        String groupId = acquirerToDefaultRuleGroupIdMap.entrySet().iterator().next().getValue();
        groupCombinedStrategyDetailDO.setGroupId(groupId);
        return Optional.of(groupCombinedStrategyDetailDO);
    }

    private ContractRuleDecisionEvaluateResultBO evaluateRuleDecisionNodeWithCatchException(GroupRouteRulesDecisionDO decision, MerchantFeatureBO merchantFeature) {
        try {
            return evaluateRuleDecisionNode(merchantFeature, decision);
        } catch (Exception e) {
            log.error("规则决策校验异常,商户:{},规则决策:{},决策id:{}", merchantFeature.getMerchantSn(), decision.getName(), decision.getId(), e);
            return ContractRuleDecisionEvaluateResultBO.fail();
        }
    }


    private Set<GroupCombinedStrategyDetailDO> listValidAndSortedGroupCombinesDetails(GroupRouteRulesDecisionDO decision, MerchantFeatureBO merchantFeature,
                                                                                      Set<String> forbiddenGroupIdSets, ContractRuleDecisionEvaluateResultBO evaluateResult, Set<String> forbiddenMessageSets) {
        List<GroupCombinedStrategyDetailDO> groupDetails = groupCombinedStrategyBiz.listGroupDetail(decision.getGroupStrategyId());
        String chooseType = decision.getChooseType();
        if (StringUtils.equals(chooseType, ChooseTypeEnum.UNABLE.getValue())) {
            log.info("商户命中UNABLE规则:{},规则决策明细:{},merchantSn:{}", decision.getName(), evaluateResult, merchantFeature.getMerchantSn());
            forbiddenMessageSets.add(evaluateResult.getMessage());
            forbiddenGroupIdSets.addAll(groupDetails.stream().map(GroupCombinedStrategyDetailDO::getGroupId).collect(Collectors.toSet()));
            return Collections.emptySet();
        }
        return groupDetails.parallelStream()
                .filter(detail -> !forbiddenGroupIdSets.contains(detail.getGroupId()))
                .filter(detail -> acquirerFacade.getStrategy(detail.getAcquirer())
                        .map(strategy -> {
                            ContractGroupRuleVerifyResultBO contractGroupRuleVerifyResultBO = strategy.checkSatisfactionToAcquirerTemplate(merchantFeature);
                            if (!contractGroupRuleVerifyResultBO.isCheckPass()) {
                                forbiddenMessageSets.add(contractGroupRuleVerifyResultBO.getMessage());
                            }
                            return  contractGroupRuleVerifyResultBO.isCheckPass();
                        }).orElse(true))
                .sorted(GroupCombinedStrategyDetailDO::compareTo)
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(GroupCombinedStrategyDetailDO::getGroupId, detail -> detail, (existing, replacement) -> existing, LinkedHashMap::new),
                        map -> new LinkedHashSet<>(map.values())));
    }


    private boolean isChooseType(GroupRouteRulesDecisionDO decision, ChooseTypeEnum type) {
        return StringUtils.equals(decision.getChooseType(), type.getValue());
    }


    private Map<Long, List<GroupRouteRuleDetailDO>> getValidRuleDetailMap() {
        return mcRulesLocalCacheService.listAllValidRuleDetails().stream()
                .collect(Collectors.groupingBy(GroupRouteRuleDetailDO::getRuleDecisionId));
    }

    private List<GroupRouteRulesDecisionDO> listAllValidMcRulesDecisionDOS() {
        return mcRulesLocalCacheService.listAllValidRuleDecisions();
    }


    /**
     * 计算规则决策节点
     * 如果节点且没有叶子节点,计算节点对应的rule detail
     * 如果节点有子节点,需要递归处理子节点的rule detail,节点根据子节点的判断返回
     * 节点如果有子节点,则节点没有对应的detail
     *
     * @param merchantFeature         商户特征
     * @param decisionNode            规则决策节点
     * @return 规则决策结果
     */
    private ContractRuleDecisionEvaluateResultBO evaluateRuleDecisionNode(MerchantFeatureBO merchantFeature, GroupRouteRulesDecisionDO decisionNode) {
        Map<Long/*ruleDecisionId*/, List<GroupRouteRuleDetailDO>> mcRuleDetailMap = getValidRuleDetailMap();
        List<GroupRouteRulesDecisionDO> mcRulesDecisionList = listAllValidMcRulesDecisionDOS();
        Map<Long/*parentId*/, List<GroupRouteRulesDecisionDO>> decisionParentIdDOSMap = mcRulesDecisionList.stream().collect(Collectors.groupingBy(GroupRouteRulesDecisionDO::getParentId));
        boolean existChildrenNode = decisionParentIdDOSMap.containsKey(decisionNode.getId());
        if (!existChildrenNode) {
            return evaluateLeafRuleDecisionNode(merchantFeature,decisionNode, mcRuleDetailMap.get(decisionNode.getId()), decisionNode.getRuleDetailConnectionType());
        }
        List<GroupRouteRulesDecisionDO> childDecisions = decisionParentIdDOSMap.get(decisionNode.getId());
        boolean andConnection = Objects.equals(decisionNode.getRuleConnectionType(), RuleConnectionTypeEnum.AND.getValue());
        ContractRuleDecisionEvaluateResultBO evaluateResult = null;
        for (GroupRouteRulesDecisionDO childDecision : childDecisions) {
            ContractRuleDecisionEvaluateResultBO decisionEvaluateResultBO = evaluateRuleDecisionNode(merchantFeature, childDecision);
            if (andConnection && !decisionEvaluateResultBO.isPass()) {
                evaluateResult = ContractRuleDecisionEvaluateResultBO.fail();
                break;
            }
            if (!andConnection && decisionEvaluateResultBO.isPass()) {
                evaluateResult = ContractRuleDecisionEvaluateResultBO.success(decisionEvaluateResultBO.getMessage());
                break;
            }
        }
        // 对于or命中,会记录子节点名称，对于and，直接记录父节点名称
        if (Objects.isNull(evaluateResult)) {
            evaluateResult = andConnection ? ContractRuleDecisionEvaluateResultBO.success("命中具体规则: " + decisionNode.getId() + " " +  decisionNode.getName())
                    : ContractRuleDecisionEvaluateResultBO.fail();
        }
        return evaluateResult;
    }


    /**
     * 计算规则决策叶子节点(根据对应的规则detail)
     * 如果对应的规则detail为空(例如兜底的`lklorg`),直接返回true
     * 如果连接类型ruleDetailConnectionType为空,默认取AND
     *
     * @param merchantFeature          商户特征
     * @param matchedRuleDetailDOS     规则detail列表
     * @param ruleDetailConnectionType 规则detail连接类型 and连接且没有为false的，结果为true. or连接且没有为true的，结果为false
     * @return true:规则决策通过 false:规则决策不通过
     */
    private ContractRuleDecisionEvaluateResultBO evaluateLeafRuleDecisionNode(MerchantFeatureBO merchantFeature,
                                                                              GroupRouteRulesDecisionDO decisionNode,
                                                                              List<GroupRouteRuleDetailDO> matchedRuleDetailDOS,
                                                                              String ruleDetailConnectionType) {
        if (CollectionUtils.isEmpty(matchedRuleDetailDOS)) {
            return ContractRuleDecisionEvaluateResultBO.success("规则为空");
        }
        boolean andConnection = Objects.equals(EnumUtils.ofNullable(RuleDetailConnectionTypeEnum.class, ruleDetailConnectionType).orElse(RuleDetailConnectionTypeEnum.AND).getValue(),
                RuleDetailConnectionTypeEnum.AND.getValue());
        ContractRuleDecisionEvaluateResultBO decisionEvaluateResultBO = null;
        for (GroupRouteRuleDetailDO matchedRuleDetailDO : matchedRuleDetailDOS) {
            boolean singleRuleEvaluateResult = doEvaluateSingleRuleDetail(merchantFeature, decisionNode, matchedRuleDetailDO);
            if (andConnection && !singleRuleEvaluateResult) {
                decisionEvaluateResultBO = ContractRuleDecisionEvaluateResultBO.fail();
                break;
            } else if (!andConnection && singleRuleEvaluateResult) {
                String objectPropertyValue = matchedRuleDetailDO.getObjectPropertyValue();
                if (StringUtils.isNotBlank(objectPropertyValue) && objectPropertyValue.length() > 100) {
                    objectPropertyValue = StringUtils.substring(matchedRuleDetailDO.getObjectPropertyValue(), 0, 100) + "...";
                }
                String detail = matchedRuleDetailDO.getObjectPropertyType() + "->" + matchedRuleDetailDO.getLogicalOperationType() + "->" + objectPropertyValue;
                decisionEvaluateResultBO = ContractRuleDecisionEvaluateResultBO.success("命中具体规则: " + decisionNode.getId() + " " + decisionNode.getName() + " ,detail: " + detail);
                break;
            }
        }
        if (Objects.isNull(decisionEvaluateResultBO)) {
            decisionEvaluateResultBO = andConnection ? ContractRuleDecisionEvaluateResultBO.success("命中具体规则: " + decisionNode.getId() + " " + decisionNode.getName())
                    : ContractRuleDecisionEvaluateResultBO.fail();
        }
        return decisionEvaluateResultBO;
    }


    /**
     * 特殊属性逻辑处理
     * 
     * @param merchantFeature 商户特征
     * @param objectPropertyType 对象属性类型
     * @param ruleDetail 规则详情，可能为null
     * @return 如果需要特殊处理，返回处理结果；否则返回空
     */
    private Optional<Boolean> specialPropertyLogic(MerchantFeatureBO merchantFeature, GroupRouteRulesDecisionDO decisionNode, String objectPropertyType, GroupRouteRuleDetailDO ruleDetail) {
        // 经营名称为空，不校验。即：and-true or-false
        if (StringUtils.equals("businessName", objectPropertyType) && StringUtils.isBlank(merchantFeature.getBusinessName())) {
            if (StringUtils.equals(decisionNode.getRuleConnectionType(), RuleDetailConnectionTypeEnum.AND.getValue())) {
                return Optional.of(true);
            }
            return Optional.of(false);
        }
        // 门店省市为空，逻辑操作符为EQUAL，CONTAIN,IN，START_WITH，返回false，否则返回true
        if ((StringUtils.equals("storeProvinces", objectPropertyType) || StringUtils.equals("storeCities", objectPropertyType)) 
                && (StringUtils.isBlank(merchantFeature.getStoreProvinces()) || StringUtils.isBlank(merchantFeature.getStoreCities()))) {
            if (ruleDetail != null) {
                String logicalOperationType = ruleDetail.getLogicalOperationType();
                if (StringUtils.equalsAny(logicalOperationType, 
                        LogicalOperationTypeEnum.EQUAL.getValue(),
                        LogicalOperationTypeEnum.CONTAIN.getValue(),
                        LogicalOperationTypeEnum.IN.getValue(),
                        LogicalOperationTypeEnum.START_WITH.getValue())) {
                    return Optional.of(false);
                } else {
                    return Optional.of(true);
                }
            }
            return Optional.of(false);
        }
        return Optional.empty();
    }

    /**
     * 根据商户特征,计算每个规则detail是否合规
     *
     * @param merchantFeature         商户特征
     * @param matchedRuleDetailDO     规则detail
     * @return true:符合
     */
    private boolean doEvaluateSingleRuleDetail(MerchantFeatureBO merchantFeature, GroupRouteRulesDecisionDO decisionNode, GroupRouteRuleDetailDO matchedRuleDetailDO) {
        String objectPropertyType = matchedRuleDetailDO.getObjectPropertyType();
        String logicalOperationType = matchedRuleDetailDO.getLogicalOperationType();
        String ruleRequiredPropertyValue = matchedRuleDetailDO.getObjectPropertyValue();
        Optional<Boolean> specialPropertyLogic = specialPropertyLogic(merchantFeature, decisionNode, objectPropertyType, matchedRuleDetailDO);
        if (specialPropertyLogic.isPresent()) {
            return specialPropertyLogic.get();
        }
        String validatedRuleRequiredPropertyValue = StringUtils.isBlank(ruleRequiredPropertyValue) ? StringUtils.EMPTY : ruleRequiredPropertyValue;
        MerchantFeaturePropertyBO merchantFeaturePropertyBO = new MerchantFeaturePropertyBO(merchantFeature, objectPropertyType);
        return EnumUtils.ofNullable(LogicalOperationTypeEnum.class, logicalOperationType)
                .map(logicalOperationTypeEnum -> {
                    BiFunction<MerchantFeaturePropertyBO, String, Boolean> function = logicalOperationFunctionMap.get(logicalOperationTypeEnum);
                    return function != null ? function.apply(merchantFeaturePropertyBO, validatedRuleRequiredPropertyValue) : false;
                })
                .orElse(false);
    }

    /**
     * 校验商户是否符合收单机构的规则
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @param netInScene 入网场景 可以为空
     * @return 校验结果
     */
    @Override
    public ContractGroupRuleVerifyResultBO checkMerchantEligibilityToAcquirer(String merchantSn, String acquirer, NetInSceneEnum netInScene) {
        if (StringUtils.isBlank(merchantSn) || StringUtils.isBlank(acquirer)) {
            throw new CommonPubBizException("参数异常,商户号: " + merchantSn + "收单机构: " + acquirer);
        }
        MerchantFeatureBO merchantFeature = getMerchantFeature(merchantSn, null, netInScene);
        if (Objects.isNull(netInScene)) {
            merchantFeature.setNetInScene(NetInSceneEnum.CHANNEL_SWITCHING.getValue());
        }
        ContractGroupRuleVerifyResultBO contractGroupRuleVerifyResultBO = getContractGroupRuleVerifyResultBO("checkMerchantEligibilityToAcquirer, 商户特征: {}", merchantFeature, acquirer);
        if (Objects.nonNull(contractGroupRuleVerifyResultBO) && contractGroupRuleVerifyResultBO.isCheckFail()) {
            log.warn("商户准入收单机构校验不通过,商户号: {}, 收单机构: {}, 校验结果: {}", merchantSn, acquirer, contractGroupRuleVerifyResultBO);
        }
        return contractGroupRuleVerifyResultBO;
    }

    private ContractGroupRuleVerifyResultBO checkEligibilityByAcquirer(String acquirer, MerchantFeatureBO merchantFeature) {
        try {
            Optional<AcquirerSharedAbility> sharedAbilityByAcquirer = acquirerFacade.getSharedAbilityByAcquirer(acquirer);
            if (!sharedAbilityByAcquirer.isPresent()) {
                return ContractGroupRuleVerifyResultBO.success();
            }
            return sharedAbilityByAcquirer.get().checkSatisfactionToAcquirerTemplate(merchantFeature);
        } catch (Exception e) {
            log.error("商户准入收单机构校验异常,商户号: {}, 收单机构: {}, 异常信息: {}", merchantFeature.getMerchantSn(), acquirer, e.getMessage(), e);
            return ContractGroupRuleVerifyResultBO.fail("商户准入收单机构校验异常,请联系客服");
        }
    }

    public ContractGroupRuleVerifyResultBO checkEligibilityByUnableRule(MerchantFeatureBO merchantFeature, String acquirer) {
        List<String> groupIds = mcRuleGroupDAO.listByAcquirerAndStatus(acquirer, ValidStatusEnum.VALID.getValue()).stream()
                .map(McRuleGroupDO::getGroupId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupIds)) {
            return ContractGroupRuleVerifyResultBO.fail("收单机构对应的规则组为空");
        }
        Set<Long> groupCombinedStrategyIds = groupCombinedStrategyBiz.listStrategyIdsByTypeAndGroupIds(GroupCombinedTypeEnum.SINGLE_MASTER_NO_BACKUP, groupIds);
        if (CollectionUtils.isEmpty(groupCombinedStrategyIds)) {
            return ContractGroupRuleVerifyResultBO.success();
        }
        List<GroupRouteRulesDecisionDO> topLevelUnableRulesDecisions = listAllValidMcRulesDecisionDOS()
                .stream()
                .filter(t -> Objects.equals(t.getParentId(), 0L)
                        && Objects.equals(t.getChooseType(), ChooseTypeEnum.UNABLE.getValue())
                        && groupCombinedStrategyIds.contains(t.getGroupStrategyId()))
                .sorted(Comparator.comparingInt(GroupRouteRulesDecisionDO::getPriority))
                .collect(Collectors.toList());
        for (GroupRouteRulesDecisionDO topLevelUnableRulesDecision : topLevelUnableRulesDecisions) {
            ContractRuleDecisionEvaluateResultBO decisionEvaluateResultBO = evaluateRuleDecisionNode(merchantFeature, topLevelUnableRulesDecision);
            if (decisionEvaluateResultBO.isPass()) {
                log.warn("商户准入收单机构校验,命中UNABLE规则: {}, {},规则决策明细: {}", merchantFeature.getMerchantSn(), topLevelUnableRulesDecision.getName(), decisionEvaluateResultBO);
                return ContractGroupRuleVerifyResultBO.fail("命中UNABLE规则: " + topLevelUnableRulesDecision.getName() + " " + decisionEvaluateResultBO.getMessage());
            }
        }
        return ContractGroupRuleVerifyResultBO.success();
    }

    /**
     * 门店开通间连扫码，校验门店是否可以入指定的收单机构
     *
     * @param merchantSn 商户号
     * @param storeId    门店号
     * @param netInScene 入网场景
     * @return 校验结果
     */
    @Override
    public ContractGroupRuleVerifyResultBO checkStoreEligibilityToAcquirer(String merchantSn, String storeId, NetInSceneEnum netInScene) {
        Optional<ContractStatusDO> contractStatusOpt = contractStatusDAO.getByMerchantSn(merchantSn);
        if (!contractStatusOpt.isPresent()) {
            log.warn("checkStoreEligibilityToAcquirer, 商户未入网,商户号: {}", merchantSn);
            return ContractGroupRuleVerifyResultBO.fail("商户未入网");
        }
        ContractStatusDO contractStatus = contractStatusOpt.get();
        String acquirer = contractStatus.getAcquirer();
        MerchantFeatureBO merchantFeature = getMerchantFeatureByStoreId(merchantSn, storeId, netInScene);
        merchantFeature.setNetInScene(NetInSceneEnum.BUSINESS_OPENING.getValue());
        return getContractGroupRuleVerifyResultBO("checkStoreEligibilityToAcquirer, 商户特征: {}", merchantFeature, acquirer);
    }

    private ContractGroupRuleVerifyResultBO getContractGroupRuleVerifyResultBO(String format, MerchantFeatureBO merchantFeature, String acquirer) {
        log.info(format, merchantFeature);
        ContractGroupRuleVerifyResultBO contractGroupRuleVerifyResultBO = checkEligibilityByAcquirer(acquirer, merchantFeature);
        if (contractGroupRuleVerifyResultBO.isCheckPass()) {
            return checkEligibilityByUnableRule(merchantFeature, acquirer);
        }
        return contractGroupRuleVerifyResultBO;
    }
}

