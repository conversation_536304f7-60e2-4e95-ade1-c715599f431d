package com.wosai.upay.job.refactor.model.entity;

import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 签约申请记录表实体对象
 *
 * <AUTHOR>
 */
@TableName("lkl_ec_apply")
@Data
public class LklEcApplyDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;

    /**
     * 整个任务状态
     * 10-进行中,20-成功,30-失败
     */
    @TableField(value = "status")
    private Integer status;

    /**详细状态
     * 1-人工审核中,2-人工审核通过,3-人工审核失败,4-签约地址过期,5-签约成功,6-签约失败
     */
    @TableField(value = "detail_status")
    private Integer detailStatus;
    /**
     * 结果
     */
    @TableField(value = "result")
    private String result;
    /**
     * 本次提交的业务信息
     */
    @TableField(value = "form_body")
    private String formBody;
    /**
     * 电子合同申请编号
     */
    @TableField(value = "ec_apply_id")
    private String ecApplyId;
    /**
     * 电子合同链接
     */
    @TableField(value = "contract_url")
    private String contractUrl;
    /**
     * 额外记录一些返回值，以备不时之需
     */
    @TableField(value = "extra")
    private String extra;
    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Date ctime;
    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Date mtime;
    /**
     * 应用标识
     */
    @TableField(value = "dev_code")
    private String devCode;

    /**


    /**
     * 状态常量
     */
    public static class Status {
        /**
         * 进行中
         */
        public static final Integer PROCESSING = 10;

        /**
         * 成功
         */
        public static final Integer SUCCESS = 20;

        /**
         * 失败
         */
        public static final Integer FAIL = 30;

    }

    /**
     *
     */
    public static class ExtraMapConst {
        /**
         * 签约H5地址过期时间
         */
        public static final String SIGN_H5_URL_EXP_TM = "sign_h5_url_exp_tm" ;

        /**
         * crm待办ID
         */
        public static final String TO_DO_ID = "todoId" ;

        /**
         * FEE_MAP
         */
        public static final String FEE_MAP = "feeMap" ;

        /**
         * 套餐
         */
        public static final String TRADE_COMBO_ID = "tradeComboId" ;

    }





    public Map<String,Object> getExtraMap() {
        return JSONObject.parseObject(extra,Map.class);
    }

}

