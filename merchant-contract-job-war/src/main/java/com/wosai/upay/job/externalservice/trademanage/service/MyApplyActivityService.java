package com.wosai.upay.job.externalservice.trademanage.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.trade.service.activity.response.ApplyConditionQueryResponse;
import com.wosai.trade.service.result.ListResult;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@JsonRpcService("rpc/applyActivity")
public interface MyApplyActivityService {

    /**
     * 原来接口里面排序字段会报错
     *
     * @param request
     * @return
     * @see com.wosai.trade.service.ApplyActivityService.conditionQuery
     */
    ListResult<ApplyConditionQueryResponse> conditionQuery(Map request);
}
