package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 收单机构终端绑定详情表表实体对象
 *
 * <AUTHOR>
 */
@TableName("provider_terminal_bind_config")
@Data
public class ProviderTerminalBindConfigDO {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private String id;
    /**
     * 商户sn
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 门店sn
     */
    @TableField(value = "store_sn")
    private String storeSn;
    /**
     * 终端sn
     */
    @TableField(value = "terminal_sn")
    private String terminalSn;
    /**
     * provider
     */
    @TableField(value = "provider")
    private Integer provider;
    /**
     * 支付源
     */
    @TableField(value = "payway")
    private Integer payway;
    /**
     * 支付源商户号
     */
    @TableField(value = "sub_mch_id")
    private String subMchId;
    /**
     * 收单机构终端号
     */
    @TableField(value = "provider_terminal_id")
    private String providerTerminalId;
    /**
     * 状态:1绑定成功；2失败；3解绑
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 详情
     */
    @TableField(value = "result")
    private String result;
    /**
     * 请求报文
     */
    @TableField(value = "request_body")
    private String requestBody;
    /**
     * 返回保文
     */
    @TableField(value = "response_body")
    private String responseBody;
    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    private Timestamp createAt;
    /**
     * 修改时间
     */
    @TableField(value = "update_at")
    private Timestamp updateAt;


}

