package com.wosai.upay.job.refactor.model.bo;

import com.wosai.upay.job.refactor.model.enums.BusinessLicenseCertificationNodeStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessLicenseCertificationNodeInfo {
    public String name;
    public BusinessLicenseCertificationNodeStatus status;
    public String time; // 仅提交认证节点有时间
    public String failReason;

}
