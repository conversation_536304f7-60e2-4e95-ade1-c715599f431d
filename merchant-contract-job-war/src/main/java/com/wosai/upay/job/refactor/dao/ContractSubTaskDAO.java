package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.common.utils.WosaiCollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.ContractSubTaskDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 子任务表表数据库访问层 {@link ContractSubTaskDO}
 * 对ContractSubTaskMapper层做出简单封装 {@link ContractSubTaskDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class ContractSubTaskDAO extends AbstractBaseDAO<ContractSubTaskDO, ContractSubTaskDynamicMapper> {

    public ContractSubTaskDAO(SqlSessionFactory sqlSessionFactory, ContractSubTaskDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据筛选条件,获取子任务信息
     *
     * @param merchantSn   商户号
     * @param taskType     任务类型
     * @param contractRule 进件规则
     * @param payWay       支付源
     * @return 子任务
     */
    public Optional<ContractSubTaskDO> getByMerchantSnAndRule(String merchantSn, Integer taskType, String contractRule, Integer payWay) {
        LambdaQueryWrapper<ContractSubTaskDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(ContractSubTaskDO::getMerchantSn, merchantSn)
                .eq(ContractSubTaskDO::getTaskType, taskType)
                .eq(ContractSubTaskDO::getContractRule, contractRule)
                .and(wrapper -> wrapper.eq(ContractSubTaskDO::getPayway, 0).or().isNull(ContractSubTaskDO::getPayway))
                .orderByDesc(ContractSubTaskDO::getCreateAt);
        return super.selectOne(lambdaQueryWrapper);
    }

    /**
     * 根据主任务id获取所有的子任务
     *
     * @param primaryTaskId 主任务id
     * @return 子任务列表
     */
    public List<ContractSubTaskDO> listByPTaskId(Long primaryTaskId) {
        if (Objects.isNull(primaryTaskId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ContractSubTaskDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ContractSubTaskDO::getPTaskId, primaryTaskId);
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号,进件通道,任务类型,任务状态获取子任务列表
     *
     * @param merchantSn 商户号
     * @param channel    进件通道
     * @param taskType   任务类型
     * @param status     任务状态
     * @return 子任务列表
     */
    public List<ContractSubTaskDO> listContractSubTaskDOs(String merchantSn, String channel, Integer taskType, Integer status) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ContractSubTaskDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ContractSubTaskDO::getMerchantSn, merchantSn);
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(channel), ContractSubTaskDO::getChannel, channel);
        lambdaQueryWrapper.eq(Objects.nonNull(taskType), ContractSubTaskDO::getTaskType, taskType);
        lambdaQueryWrapper.eq(Objects.nonNull(status), ContractSubTaskDO::getStatus, status);
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    public List<ContractSubTaskDO> listContractSubTaskDOsByPayway(String merchantSn, Integer taskType, Integer payway) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ContractSubTaskDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ContractSubTaskDO::getMerchantSn, merchantSn);
        lambdaQueryWrapper.eq(Objects.nonNull(taskType), ContractSubTaskDO::getTaskType, taskType);
        lambdaQueryWrapper.eq(Objects.nonNull(payway), ContractSubTaskDO::getPayway, payway);
        lambdaQueryWrapper.orderByDesc(ContractSubTaskDO::getCreateAt);
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号， 任务类型， 进件规则和状态筛选进件子任务
     * @param merchantSn    商户号
     * @param taskType      任务类型
     * @param contractRule  进件规则
     * @param status        进件状态
     * @return  子任务
     */
    public Optional<ContractSubTaskDO> getByMerchantSnAndStatus(String merchantSn, Integer taskType, String contractRule, Integer status) {
        LambdaQueryWrapper<ContractSubTaskDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(ContractSubTaskDO::getMerchantSn, merchantSn)
                .eq(ContractSubTaskDO::getTaskType, taskType)
                .eq(ContractSubTaskDO::getContractRule, contractRule)
                .eq(ContractSubTaskDO::getStatus, status)
                .and(wrapper -> wrapper.eq(ContractSubTaskDO::getPayway, 0).or().isNull(ContractSubTaskDO::getPayway))
                .orderByDesc(ContractSubTaskDO::getCreateAt);
        return super.selectOne(lambdaQueryWrapper);
    }

    /**
     * 批量删除子任务
     *
     * @param pTaskIds
     */
    public int batchDeleteSubTaskByPTaskIds(List<Long> pTaskIds) {
        if (WosaiCollectionUtils.isEmpty(pTaskIds)) {
            return 0;
        }
        LambdaQueryWrapper<ContractSubTaskDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ContractSubTaskDO::getPTaskId, pTaskIds);
        return entityMapper.delete(lambdaQueryWrapper);
    }
}
