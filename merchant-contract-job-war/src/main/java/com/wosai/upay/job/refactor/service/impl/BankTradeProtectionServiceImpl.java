package com.wosai.upay.job.refactor.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.biz.AopBiz;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.model.dto.request.AppCommonReqDTO;
import com.wosai.upay.job.model.dto.request.BankTradeProtectionAuditApplyReqDTO;
import com.wosai.upay.job.model.dto.request.BankTradeProtectionConfigReqDTO;
import com.wosai.upay.job.model.dto.response.BankTradeProtectionConfigRspDTO;
import com.wosai.upay.job.refactor.biz.audit.BankTradeProtectionAuditApply;
import com.wosai.upay.job.refactor.dao.BankTradeProtectionMerchantConfigDAO;
import com.wosai.upay.job.refactor.model.entity.BankTradeProtectionMerchantConfigDO;
import com.wosai.upay.job.refactor.model.enums.BankTradeProtectionStatusEnum;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import com.wosai.upay.job.service.bank.BankTradeProtectionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 银行保障交易服务
 *
 * <AUTHOR>
 * @date 2024/8/15 11:18
 */
@Service
@Slf4j
@AutoJsonRpcServiceImpl
public class BankTradeProtectionServiceImpl implements BankTradeProtectionService {

    @Resource
    private BankTradeProtectionMerchantConfigDAO bankTradeProtectionConfigDAO;

    @Autowired
    private MerchantService merchantService;

    @Resource
    private AopBiz aopBiz;

    @Resource
    @Lazy
    private BankTradeProtectionAuditApply bankTradeProtectionAuditApply;
    
    @Value("${bank.cooperation.notice.crm.devCode}")
    private String crmDevCode;
    
    @Value("${bank.trade.protection.close.template_id}")
    private String closeProtectionTemplateId;

    @Value("${bank.trade.protection.not.close.template_id}")
    private String notCloseTemplateId;

    @Value("${bank.trade.protection.recover.template_id}")
    private String recoverProtectionTemplateId;

    @Value("${bank.trade.protection.not.recover.template_id}")
    private String notRecoverTemplateId;

    @Resource
    private BusinessLogBiz businessLogBiz;

    /**
     * 是否关闭银行保障交易
     * 白名单没有配置的商户默认为未关闭（即默认开启保障）
     *
     * @param merchantSn 商户号
     * @return 是否关闭银行保障交易 true:关闭 false:未关闭
     */
    public boolean isCloseBankTradeProtection(String merchantSn) {
        Optional<BankTradeProtectionMerchantConfigDO> configOpt = bankTradeProtectionConfigDAO.getByMerchantSn(merchantSn);
        return configOpt.map(BankTradeProtectionMerchantConfigDO::isClose).orElse(false);
    }

    /**
     * 获取商户银行保障交易配置
     *
     * @param appCommonReqDTO app通用请求DTO
     * @return 银行保障交易配置响应DTO
     */
    @Override
    public BankTradeProtectionConfigRspDTO getTradeProtectionStatus(AppCommonReqDTO appCommonReqDTO) {
        BankTradeProtectionConfigRspDTO bankTradeProtectionConfigRspDTO = new BankTradeProtectionConfigRspDTO();
        bankTradeProtectionConfigRspDTO.setMerchantId(appCommonReqDTO.getMerchantId());
        MerchantInfo merchantInfo = merchantService.getMerchantById(appCommonReqDTO.getMerchantId(), null);
        if (merchantInfo == null) {
            log.error("获取商户银行保障交易配置错误，商户不存在, merchantId:{}", appCommonReqDTO.getMerchantId());
            bankTradeProtectionConfigRspDTO.setOpen(true);
            return bankTradeProtectionConfigRspDTO;
        }
        Optional<BankTradeProtectionMerchantConfigDO> configOpt = bankTradeProtectionConfigDAO.getByMerchantSn(merchantInfo.getSn());
        if (configOpt.isPresent()) {
            BankTradeProtectionMerchantConfigDO config = configOpt.get();
            bankTradeProtectionConfigRspDTO.setOpen(config.isOpen());
            return bankTradeProtectionConfigRspDTO;
        }
        bankTradeProtectionConfigRspDTO.setOpen(true);  // 默认就是开启状态
        return bankTradeProtectionConfigRspDTO;
    }

    /**
     * 批量获取商户银行保障交易状态
     *
     * @param merchantSns 商户号列表
     * @return 商户银行保障交易状态列表
     */
    public List<BankTradeProtectionMerchantConfigDO> batchListByMerchantSns(Set<String> merchantSns) {
        if (CollectionUtils.isEmpty(merchantSns)) {
            return Collections.emptyList();
        }
        List<BankTradeProtectionMerchantConfigDO> allConfigs = Lists.newArrayListWithCapacity(merchantSns.size());
        Lists.partition(Lists.newArrayList(merchantSns), 200)
                .forEach(partition -> allConfigs.addAll(bankTradeProtectionConfigDAO.listByMerchantSns(partition)));
        return allConfigs;
    }

    /**
     * 更新商户银行保障交易状态
     *
     * @param bankTradeProtectionConfigReqDTO 银行保障交易配置请求DTO
     * @return 是否更新成功
     */
    @Override
    public Boolean updateTradeProtectionStatus(BankTradeProtectionConfigReqDTO bankTradeProtectionConfigReqDTO) {
        MerchantInfo merchantInfo = merchantService.getMerchantById(bankTradeProtectionConfigReqDTO.getMerchantId(), null);
        if (merchantInfo == null) {
            log.error("更新商户银行保障交易状态错误，商户不存在, merchantId:{}", bankTradeProtectionConfigReqDTO.getMerchantId());
            return Boolean.FALSE;
        }
        Integer openStatus = (bankTradeProtectionConfigReqDTO.isRecoverProtection() || bankTradeProtectionConfigReqDTO.isNotClose())
                ? BankTradeProtectionStatusEnum.OPEN.getValue() : BankTradeProtectionStatusEnum.CLOSE.getValue();
        try {
            bankTradeProtectionConfigDAO.insertOrUpdateOpenStatusByMerchantSn(merchantInfo.getSn(), openStatus);
            HashMap<String, String> noticeMap = Maps.newHashMap();
            noticeMap.put("merchant_sn", merchantInfo.getSn());
            noticeMap.put("merchant_name", merchantInfo.getName());
            aopBiz.sendNoticeToCrm(bankTradeProtectionConfigReqDTO.getMerchantId(), crmDevCode, getTemplateId(bankTradeProtectionConfigReqDTO), noticeMap);
            if (bankTradeProtectionConfigReqDTO.isRecoverProtection() || bankTradeProtectionConfigReqDTO.isCloseProtection()) {
                businessLogBiz.recordMerchantBankTradeProtectionConfigLog(merchantInfo.getId(),
                        Objects.equals(openStatus, BankTradeProtectionStatusEnum.OPEN.getValue()));
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("更新商户银行保障交易状态失败, merchantSn:{}, openStatus:{}", merchantInfo.getSn(), openStatus, e);
            return Boolean.FALSE;
        }
    }

    private String getTemplateId(BankTradeProtectionConfigReqDTO bankTradeProtectionConfigReqDTO) {
        if (bankTradeProtectionConfigReqDTO.isCloseProtection()) {
            return  closeProtectionTemplateId;
        }
        if (bankTradeProtectionConfigReqDTO.isNotClose()) {
            return notCloseTemplateId;
        }
        if (bankTradeProtectionConfigReqDTO.isRecoverProtection()) {
            return recoverProtectionTemplateId;
        }
        return notRecoverTemplateId;
    }

    /**
     * 处理银行保障交易审核申请
     * 触发给商户发通知，让商户选择是否需要银行保障交易
     *
     * @param auditApplyReqDTO 银行保障交易审核申请
     */
    @Override
    public void processBankTradeProtectionAuditApplyApply(BankTradeProtectionAuditApplyReqDTO auditApplyReqDTO) {
        log.info("processBankTradeProtectionAuditApplyApply auditApplyReqDTO:{}", auditApplyReqDTO);
        ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() ->bankTradeProtectionAuditApply.processApply(auditApplyReqDTO));
    }
}
