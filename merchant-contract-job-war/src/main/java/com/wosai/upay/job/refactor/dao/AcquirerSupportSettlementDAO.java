package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.AcquirerSupportSettlementMapper;
import com.wosai.upay.job.refactor.model.entity.AcquirerSupportSettlementDO;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 收单机构支持结算类型表表数据库访问层 {@link AcquirerSupportSettlementDO}
 * 对AcquirerSupportSettlementMapper层做出简单封装 {@link AcquirerSupportSettlementMapper}
 *
 * <AUTHOR>
 */
@Repository
public class AcquirerSupportSettlementDAO extends AbstractBaseDAO<AcquirerSupportSettlementDO, AcquirerSupportSettlementMapper>{


    public AcquirerSupportSettlementDAO(SqlSessionFactory sqlSessionFactory, AcquirerSupportSettlementMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据收单机构和营业执照类型类型查询
     *
     * @param acquirer            收单机构
     * @param businessLicenseType 营业执照类型
     * @return Optional<AcquirerSupportSettlementDO>
     */
    public Optional<AcquirerSupportSettlementDO> getByAcquirerAndLicenseType(String acquirer, Integer businessLicenseType) {
        LambdaQueryWrapper<AcquirerSupportSettlementDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(AcquirerSupportSettlementDO::getAcquirer, acquirer)
                .eq(AcquirerSupportSettlementDO::getBusinessLicenseType, businessLicenseType);
        return selectOne(lambdaQueryWrapper);
    }

    /**
     * 根据营业执照类型类型查询
     *
     * @param businessLicenseType 营业执照类型
     * @return List<AcquirerSupportSettlementDO>
     */
    public List<AcquirerSupportSettlementDO> listByLicenseType(Integer businessLicenseType) {
        if (Objects.isNull(businessLicenseType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AcquirerSupportSettlementDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AcquirerSupportSettlementDO::getBusinessLicenseType, businessLicenseType);
        return entityMapper.selectList(lambdaQueryWrapper);
    }
}
