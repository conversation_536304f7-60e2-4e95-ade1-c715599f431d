package com.wosai.upay.job.payactivity.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.request.AntMerchantExpandShopModifyRequest;
import com.alipay.api.request.AntMerchantExpandShopQueryRequest;
import com.alipay.api.response.AntMerchantExpandShopModifyResponse;
import com.alipay.api.response.AntMerchantExpandShopQueryResponse;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.MchSnapshot;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.bluesea.ActivityCreateResp;
import com.wosai.upay.merchant.contract.model.bluesea.AlipayIndirectActivityCreateRequest;
import com.wosai.upay.merchant.contract.model.bluesea.BankCardInfo;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import com.wosai.upay.merchant.contract.service.NewBlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wosai.upay.job.Constants.BlueSeaConstant.FAIL;
import static com.wosai.upay.merchant.contract.constant.alipay.AlipayIndirectActivityConstant.INDUSTRY_SPECIAL;

/**
 * @Description: 支付宝高校
 * <AUTHOR>
 * @Date: 2021/11/23 11:07 上午
 */
@Component
@Slf4j
public class AlipayUniversityBiz {
    @Autowired
    private BlueSeaBiz blueSeaBiz;
    @Autowired
    private NewBlueSeaService newBlueSeaService;

    @Autowired
    private BlueSeaTaskMapper blueSeaTaskMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private BlueSeaService blueSeaService;

    @Autowired
    private CallBackService callBackService;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    public static final String M3_LEVEL = "INDIRECT_LEVEL_M3";
    public static final String M4_LEVEL = "INDIRECT_LEVEL_M4";


    /**
     * 写评论
     *
     * @param auditId    审批ID
     * @param templateId 模版ID
     * @param type       评论类型 1成功 2失败
     * @param message
     */
    public void addComment(Long auditId, Long templateId, int type, String message) {
        CallBackBean callBackBean = CallBackBean.builder()
                .auditId(auditId).templateId(templateId)
                .build();
        callBackBean.setResultType(type);
        callBackBean.setMessage(message);
        callBackService.addComment(callBackBean);
    }

    /**
     * 提交申请单
     *
     * @param blueSeaTask
     * @return
     */
    public CommonResult apply(BlueSeaTask blueSeaTask) {
        CommonResult result;
        try {
            doApply(blueSeaTask);
            result = new CommonResult(CommonResult.SUCCESS, "支付宝高校活动报名中");
        } catch (Exception e) {
            log.error("商户{}支付宝活动线上报名提交申请单,异常{}", blueSeaTask.getMerchant_sn(), e.getMessage(), e);
            //更新失败
            blueSeaBiz.updateStatus(blueSeaTask.getId(), FAIL, e.getMessage(), null, null, null, null, 2);
            result = new CommonResult(CommonResult.BIZ_FAIL, "支付宝高校活动商户申请失败" + e.getMessage());
        }
        return result;
    }


    /**
     * 支付宝申请活动
     *
     * @param task
     */
    private void doApply(BlueSeaTask task) {
        //查询MCC码以及支付宝等级
        Map subMchMap;
        try {
            subMchMap = composeAcquirerBiz.getAlipayMchInfo(task.getAli_mch_id());
        } catch (Exception e) {
            log.error("{} {} 查询支付宝子商户号信息失败", task.getMerchant_sn(), task.getAli_mch_id(), e);
            return;
        }
        if (WosaiMapUtils.isEmpty(subMchMap)) {
            return;
        }
        //是否提交申请
        if (!checkApply(subMchMap, task)) {
            return;
        }
        //参数组装
        AlipayIndirectActivityCreateRequest request = build(subMchMap, task);
        //申请
        ActivityCreateResp activityCreateResp = blueSeaService.create(request);
        //报名申请单提交失败
        if (!activityCreateResp.isSuccess()) {
            String msg = "支付宝高校活动线上报名提交申请单失败" + activityCreateResp.getMessage();
            throw new ContractBizException(msg);
        } else {
            if (activityCreateResp.isHas_apply()) {
                //已报名
                blueSeaBiz.updateStatus(task.getId(), BlueSeaConstant.ACTIVITY_CREATE_SUCCESS, null, null, null, null, activityCreateResp.getOrder_id(), 2);
            } else {
                //报名申请单提交成功
                blueSeaBiz.updateStatus(task.getId(), BlueSeaConstant.ACTIVITY_CREATE, null, null, null, null, activityCreateResp.getOrder_id(), 2);
            }
        }
    }


    /**
     * 组装申请参数
     *
     * @param subMchMap
     * @param task
     * @return
     */
    public AlipayIndirectActivityCreateRequest build(Map subMchMap, BlueSeaTask task) {

        AlipayIndirectActivityCreateRequest request = new AlipayIndirectActivityCreateRequest();
        request.setName(BeanUtil.getPropString(subMchMap, "name"));
        request.setAlias_name(BeanUtil.getPropString(subMchMap, "alias_name"));
        request.setSub_merchant_id(task.getAli_mch_id());
        request.setActivity_type(INDUSTRY_SPECIAL);
        //行业code
        Map<String, String> categoryMap = applicationApolloConfig.getAlyThirdCategory();
        String aliMcc = task.getMchSnapshot().getAliMcc();
        request.setIndustry_code(BeanUtil.getPropString(categoryMap, aliMcc));

        Map formBody = task.getFormBody();
        Map picInfo = WosaiMapUtils.getMap(formBody, "pic_info");

        List<String> indoor = (List<String>) picInfo.get("indoor");
        List<String> outdoor = (List<String>) picInfo.get("outdoor");
        List<String> license = (List<String>) picInfo.get("license");
        List<String> legal = (List<String>) picInfo.get("legal");
        List<String> qualification = (List<String>) picInfo.get("qualification");
        if (CollectionUtils.isEmpty(legal) || legal.size() < 2) {
            throw new ContractBizException("审批必须上传法人身份证正反面照片");
        }

        //营业执照
        request.setBusiness_license_pic(license.get(0));
        //身份证正面照url
        request.setLegal_person_pic_front(legal.get(0));
        //身份证反面照URL
        request.setLegal_person_pic_back(legal.get(1));
        if (CollectionUtils.isNotEmpty(qualification)) {
            //商户行业资质图片
            request.setIndustry_qualification_image(qualification.get(0));
        }

        //门头照url
        request.setShop_entrance_pic(outdoor.get(0));
        //内景照url
        request.setIndoor_pic(indoor.get(0));


        //银行卡信息
        BankCardInfo bank_account = new BankCardInfo();
        List cardInfos = (List) picInfo.get("cardInfo");
        Map card = (Map) cardInfos.get(0);
        bank_account.setCard_name(WosaiMapUtils.getString(card, "cardName"));
        bank_account.setCard_no(WosaiMapUtils.getString(card, "cardNo"));
        bank_account.setBank_branch_name(WosaiMapUtils.getString(card, "bankBranchName"));
        request.setBank_account(bank_account);
        if ("8220".equals(aliMcc)) {
            String schoolType = (String) formBody.get("school_type");
            //民办
            if ("private_school".equals(schoolType)) {
                request.setIndustry_code("B0135");
            }
            //开户证明
            List<String> prove = (List<String>) picInfo.get("bankProve");
            request.setBank_account_prove(prove.get(0));
            //财务室
            List<String> financePic = (List<String>) picInfo.get("financePic");
            request.setFinance_pic(financePic.get(0));
        }
        return request;

    }

    /**
     * 是否提交申请
     * true 提交申请
     * false 不提交
     *
     * @param subMchMap
     * @param task
     * @return
     */
    public boolean checkApply(Map subMchMap, BlueSeaTask task) {

        MchSnapshot mchSnapshot = task.getMchSnapshot();
        Integer updateFail = mchSnapshot.getUpdateFail();
        if (updateFail == null) {
            updateFail = 0;
        }

        String mcc = BeanUtil.getPropString(subMchMap, "mcc");
        String level = BeanUtil.getPropString(subMchMap, "indirect_level");
        if (!M3_LEVEL.equals(level) && !M4_LEVEL.equals(level)) {
            log.error("收单机构升级M3失败,商户号:{}", task.getMerchant_sn());
            update(task, updateFail + 1);
            return false;
        }
        //商户类型
        Map map = JSONObject.parseObject(task.getForm_body(), Map.class);
        //group_meal/university_school
        String merchantType = (String) map.get("merchant_type");
        //合作食堂
        if ("group_meal".equals(merchantType) && !"5880".equals(mcc)) {
            log.error("合作食堂商户MCC不为5880,收单机构MCC码更新失败,商户号:{}", task.getMerchant_sn());
            update(task, updateFail + 1);
            return false;
        }
        //自营食堂
        if ("university_school".equals(merchantType) && !"8220".equals(mcc)) {
            log.error("自营食堂商户MCC不为8220,收单机构MCC码更新失败,商户号:{}", task.getMerchant_sn());
            update(task, updateFail + 1);
            return false;
        }
        return true;
    }


    public void update(BlueSeaTask task, Integer updateFail) {
        MchSnapshot mchSnapshot = new MchSnapshot();
        mchSnapshot.setUpdateFail(updateFail);
        if (updateFail > 3) {
            //失败
            blueSeaBiz.updateStatus(task.getId(), BlueSeaConstant.FAIL, "收单机构更新MCC码失败", mchSnapshot, null, null, null, 2);
        } else {
            blueSeaBiz.updateStatus(task.getId(), task.getStatus(), "收单机构更新MCC码失败定时任务继续处理", mchSnapshot, null, null, null, 2);
        }
    }


    /**
     * 存在门店并且门店类目不是1744 修改门店类目
     * https://confluence.wosai-inc.com/pages/viewpage.action?pageId=440434896
     * 校园团餐检查
     */
    public void checkGroupMeal(BlueSeaTask task) {
        AliCommResponse<AntMerchantExpandShopQueryRequest, AntMerchantExpandShopQueryResponse> response = blueSeaBiz.existAntShop(task.getMerchant_id(), task.getStore_sn(), task.getAli_mch_id());
        //先查询是否有店铺，
        if (response != null) {
            AntMerchantExpandShopQueryResponse resp = response.getResp();
            //有店铺需要判断一个code，不为1744的需要修改为B0007
            if (resp != null && WosaiStringUtils.isNotBlank(resp.getShopCategory())) {
                String shopCategory = resp.getShopCategory();
                if (!shopCategory.equals("1744")) {
                    Map map = new HashMap();
                    map.put("shop_category", "B0007");
                    map.put("shop_id", resp.getShopId());
                    AntMerchantExpandShopModifyRequest request = new AntMerchantExpandShopModifyRequest();
                    request.setBizContent(JSON.toJSONString(map));
                    //调用接口修改...
                    AliCommResponse<AntMerchantExpandShopModifyRequest, AntMerchantExpandShopModifyResponse> modifyResp = newBlueSeaService.antMerchantExpandShopModify(request);
                    if (!modifyResp.isSuccess()) {
                        //修改失败，发送钉钉通知
                        log.error("提交修改失败");
                        AntMerchantExpandShopModifyResponse subResp = modifyResp.getResp();
                    } else {
                        //成功 更新Change_order_id
                        String orderId = modifyResp.getResp().getOrderId();
                        BlueSeaTask change = new BlueSeaTask();
                        change.setId(task.getId());
                        change.setChange_order_id(orderId);
                        blueSeaTaskMapper.updateByPrimaryKeySelective(change);
                    }
                }
            }

        }
    }

}
