package com.wosai.upay.job.refactor.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * provider支付方式校验结果
 *
 * <AUTHOR>
 * @date 2025/07/18 09:54
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SatisfactionToProviderPayWayCheckResultBO {

    private boolean success;

    private String message;

    public static SatisfactionToProviderPayWayCheckResultBO success() {
        return new SatisfactionToProviderPayWayCheckResultBO(true, null);
    }

    public static SatisfactionToProviderPayWayCheckResultBO success(String message) {
        return new SatisfactionToProviderPayWayCheckResultBO(true, message);
    }

    public static SatisfactionToProviderPayWayCheckResultBO fail(String message) {
        return new SatisfactionToProviderPayWayCheckResultBO(false, message);
    }
}
