package com.wosai.upay.job.externalservice.brand;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.cua.brand.business.api.dto.request.MerchantIdDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandPaymentModeResponseDTO;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.cua.brand.business.api.facade.BrandFacade;
import com.wosai.upay.job.externalservice.brand.model.BrandDetailInfoQueryResp;
import com.wosai.upay.job.externalservice.brand.model.BrandMerchantInfoQueryResp;
import com.wosai.upay.job.externalservice.brand.model.BrandPaymentModeQueryResp;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/9
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BrandBusinessClient {

    private final BrandFacade brandFacade;

    public BrandMerchantInfoQueryResp getBrandMerchantInfoByMerchantId(String merchantId) {
        MerchantIdDTO merchantIdDTO = new MerchantIdDTO();
        merchantIdDTO.setMerchantId(merchantId);
        BrandMerchantInfoDTO brandMerchantInfoDTO = brandFacade.getBrandMerchantInfoByMerchantId(merchantIdDTO);
        if (Objects.nonNull(brandMerchantInfoDTO)) {
            return new BrandMerchantInfoQueryResp()
                    .setBrandId(brandMerchantInfoDTO.getBrandId())
                    .setPaymentMode(PaymentModeEnum.getPaymentModeEnum(brandMerchantInfoDTO.getPaymentMode()))
                    .setBrandMerchant(true)
                    .setMerchantType(brandMerchantInfoDTO.getMerchantType());
        }
        return new BrandMerchantInfoQueryResp().setBrandMerchant(false);
    }

    /**
     * 查询品牌详细信息
     *
     * @param brandId 品牌ID
     * @return 品牌详情
     */
    public BrandDetailInfoQueryResp getBrandDetailInfoByBrandId(String brandId) {
        BrandDetailInfoDTO brandDetailInfo = brandFacade.getBrandDetailInfoByBrandId(new QueryBrandsDTO().setBrandId(brandId));
        if (Objects.isNull(brandDetailInfo)) {
            throw new CommonInvalidParameterException(String.format("%s对应的品牌信息不存在", brandId));
        }
        return new BrandDetailInfoQueryResp().setMainMerchantSn(brandDetailInfo.getMerchantSn());
    }

    /**
     * 进件选择规则组时查询支付模式
     * @param merchantId 商户ID
     * @return 品牌ID和支付模式
     */
    public @Nullable BrandPaymentModeQueryResp getBrandPaymentModeForContract(String merchantId) {
        BrandPaymentModeResponseDTO brandPaymentModeResponseDTO = brandFacade.queryBrandPaymentModeForContract(merchantId);
        if (Objects.isNull(brandPaymentModeResponseDTO)) {
            return null;
        }
        return new BrandPaymentModeQueryResp()
                .setBrandId(brandPaymentModeResponseDTO.getBrandId())
                .setPaymentMode(brandPaymentModeResponseDTO.getPaymentMode());
    }
}
