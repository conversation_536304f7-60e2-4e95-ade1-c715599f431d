package com.wosai.upay.job.providerterminal;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.enume.ProviderTerminalBindLevel;
import com.wosai.upay.job.enume.ProviderTerminalIdTypeEnum;
import com.wosai.upay.job.providerterminal.model.ProviderTerminalAddContext;
import com.wosai.upay.job.util.ScaleConverterUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Component
public class HxbProviderTerminalHandler extends AbstractProviderTerminalHandler {

    @Override
    protected List<Integer> getSupportPaywayList() {
        return ALIPAY_WEIXIN_UNIONPAY_LIST;
    }

    @Override
    public ProviderEnum getProvider() {
        return ProviderEnum.PROVIDER_HXB;
    }

    @Override
    protected String contractProviderTerminalId(ProviderTerminalAddContext request) {
        ProviderTerminalIdTypeEnum type;
        if (request.getBindLevel() == ProviderTerminalBindLevel.MERCHANT) {
            type = isProdEnvironment ?
                    ProviderTerminalIdTypeEnum.MERCHANT_PREFIX :
                    ProviderTerminalIdTypeEnum.MERCHANT_TEST_PREFIX;
        } else if (request.getBindLevel() == ProviderTerminalBindLevel.STORE) {
            type = isProdEnvironment ?
                    ProviderTerminalIdTypeEnum.STORE_PREFIX :
                    ProviderTerminalIdTypeEnum.STORE_TEST_PREFIX;
        } else {
            type = isProdEnvironment ?
                    ProviderTerminalIdTypeEnum.TERMINAL_PREFIX :
                    ProviderTerminalIdTypeEnum.TERMINAL_TEST_PREFIX;
        }

        String prefix = type.getCode();
        String nextTerminalId = ScaleConverterUtil.encode36(Long.parseLong(snGenerator.nextProviderTerminalId()));
        return StringUtils.isNotBlank(prefix) ? prefix + nextTerminalId : nextTerminalId;
    }

}
