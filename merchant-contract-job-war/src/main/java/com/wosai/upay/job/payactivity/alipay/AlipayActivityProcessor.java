package com.wosai.upay.job.payactivity.alipay;

import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 支付宝 不同活动对应处理类
 * <AUTHOR>
 * @Date: 2021/7/28 6:14 下午
 */
@Component
public class AlipayActivityProcessor {

    @Autowired
    private AlipayUniversityActivity alipayUniversityActivity;
    @Autowired
    private AlipayEduActivity alipayEduActivity;

    public AbstractAlipayActivityService getHandleClass(int type) {
        switch (type) {
            case BlueSeaConstant.UNIVERSITY_ACTIVITY:
                return alipayUniversityActivity;
            case BlueSeaConstant.ALI_EDU_ACTIVITY:
                return alipayEduActivity;
            default:
                throw new ContractBizException("未找到活动类型的处理类");
        }

    }


}
