package com.wosai.upay.job.refactor.task.license.micro.builder.context;

import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV3Task;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV3MainTaskContext;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 交易参数构建器上下文
 * 包含构建过程中需要的所有数据
 * 
 * <AUTHOR>
 */
@Data
public class TradeParamsBuilderContext {
    
    /**
     * 主任务上下文
     */
    private BusinessLicenseCertificationV3MainTaskContext mainTaskContext;
    
    /**
     * 子任务上下文
     */
    private BusinessLicenceCertificationV3Task.SubTaskContextBOInner subTaskContext;
    
    /**
     * 商户信息
     */
    private Map<String, Object> merchant;
    
    /**
     * 微信认证时间
     */
    private Long wxAuthTime;
    
    /**
     * 支付宝认证时间
     */
    private Long aliAuthTime;
    
    /**
     * 合同任务ID
     */
    private Long contractTaskId;
    
    /**
     * 旧参数列表
     */
    private List<MerchantProviderParamsDO> oldParams;
    
    /**
     * 商户收单机构信息
     */
    private MerchantAcquireInfoBO merchantAcquireInfo;
    
    /**
     * 合同参数上下文（用于微信认证等）
     */
    private Map<String, Object> contractParamContext;
}
