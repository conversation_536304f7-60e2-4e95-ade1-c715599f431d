package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.mapper.MerchantProviderParamsExtDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsExtDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;


@Repository
public class MerchantProviderParamsExtDAO {

    @Resource
    private MerchantProviderParamsExtDynamicMapper merchantProviderParamsExtDynamicMapper;


    public Optional<MerchantProviderParamsExtDO> getUnionPayStatus(String paramsId) {
        LambdaQueryWrapper<MerchantProviderParamsExtDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MerchantProviderParamsExtDO::getParamId, paramsId)
                .eq(MerchantProviderParamsExtDO::getType, MerchantProviderParamsExtDO.UNION_PAY);
        return Optional.ofNullable(merchantProviderParamsExtDynamicMapper.selectOne(lambdaQueryWrapper));
    }

    /**
     * 保存交易参数
     *
     * @param merchantProviderParamsExtDO 交易参数
     * @return effect rows
     */
    public int saveMerchantParametersExt(MerchantProviderParamsExtDO merchantProviderParamsExtDO) {
        if (Objects.isNull(merchantProviderParamsExtDO)) {
            return 0;
        }
        merchantProviderParamsExtDO.setVersion(1L);
        return merchantProviderParamsExtDynamicMapper.insert(merchantProviderParamsExtDO);
    }

    public int updateMerchantParams(MerchantProviderParamsExtDO extDO) {
        return merchantProviderParamsExtDynamicMapper.updateById(extDO);
    }
}
