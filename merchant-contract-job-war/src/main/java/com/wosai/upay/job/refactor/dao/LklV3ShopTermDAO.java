package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.LklV3ShopTermDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.LklV3ShopTermDO;

import javax.annotation.Resource;
import java.util.List;


/**
 * 表数据库访问层 {@link LklV3ShopTermDO}
 * 对LklV3ShopTermMapper层做出简单封装 {@link LklV3ShopTermDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class LklV3ShopTermDAO extends AbstractBaseDAO<LklV3ShopTermDO, LklV3ShopTermDynamicMapper> {

    public LklV3ShopTermDAO(SqlSessionFactory sqlSessionFactory, LklV3ShopTermDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户号获取拉卡拉门店终端信息
     *
     * @param merchantSn 商户号
     * @return 拉卡拉门店终端信息
     */
    public List<LklV3ShopTermDO> listByMerchantSn(String merchantSn) {
        return entityMapper.selectList(new LambdaQueryWrapper<LklV3ShopTermDO>().eq(LklV3ShopTermDO::getMerchantSn, merchantSn));
    }

    /**
     * 根据商户号删除拉卡拉门店终端信息
     *
     * @param merchantSn 商户号
     */
    public void deleteByMerchantSn(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return;
        }
        entityMapper.delete(new LambdaQueryWrapper<LklV3ShopTermDO>().eq(LklV3ShopTermDO::getMerchantSn, merchantSn));
    }
}
