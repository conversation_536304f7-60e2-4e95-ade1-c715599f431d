package com.wosai.upay.job.refactor.service.strategy.concrete.group;

import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDAO;
import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDetailDAO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRuleDetailDO;
import com.wosai.upay.job.refactor.model.enums.GroupCombinedTypeEnum;
import com.wosai.upay.job.refactor.service.strategy.GroupCombinedProcessStrategy;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 一主一备-报备规则组策略组合处理策略
 *
 * <AUTHOR>
 * @date 2023/12/13 17:42
 */
@Component
public class SingleMSingleBProcessStrategy implements GroupCombinedProcessStrategy {

    @Resource
    private GroupCombinedStrategyDetailDAO groupCombinedStrategyDetailDAO;

    @Resource
    private GroupCombinedStrategyDAO groupCombinedStrategyDAO;

    /**
     * 获取策略类型
     *
     * @return 报备规则组策略组合类型枚举
     */
    @Override
    public GroupCombinedTypeEnum getType() {
        return GroupCombinedTypeEnum.SINGLE_MASTER_SINGLE_BACKUP;
    }

    /**
     * 获取符合的收单机构进件规则detail
     *
     * @param groupCombinedStrategies 规则组对应的策略组合detail
     * @return 符合条件的策略组合detail
     */
    @Override
    public List<GroupCombinedStrategyDetailDO> listSatisfactionDetails(List<GroupCombinedStrategyDetailDO> groupCombinedStrategies) {
        if (CollectionUtils.isEmpty(groupCombinedStrategies)) {
            return Collections.emptyList();
        }
        Map<Integer, GroupCombinedStrategyDetailDO> resMap = Maps.newHashMap();
        groupCombinedStrategies.forEach(detail -> resMap.computeIfAbsent(detail.getGroupType(), k -> detail));
        return new ArrayList<>(resMap.values());
    }

    /**
     * 新增并且返回收单机构进件规则,不存在新增后返回,存在直接返回
     *
     * @param groupCombinedStrategyDetailDOS 进件报备规则组策略组合detail
     * @return 进件报备规则组策略
     */
    @Override
    public Optional<GroupCombinedStrategyDO> getGroupCombineStrategy(List<GroupCombinedStrategyDetailDO> groupCombinedStrategyDetailDOS) {
        if (CollectionUtils.isEmpty(groupCombinedStrategyDetailDOS) || groupCombinedStrategyDetailDOS.size() != 2) {
            throw new ContractBizException("进件报备规则组策略组合detail数量错误");
        }
        groupCombinedStrategyDetailDOS.sort(Comparator.comparing(GroupCombinedStrategyDetailDO::getGroupType));
        GroupCombinedStrategyDetailDO masterStrategyDetailDO = groupCombinedStrategyDetailDOS.get(0);
        GroupCombinedStrategyDetailDO backupStrategyDetailDO = groupCombinedStrategyDetailDOS.get(1);
        Map<Long, GroupCombinedStrategyDO> strategyDOMap = groupCombinedStrategyDAO.listByGroupType(getTypeValue()).stream().collect(Collectors.toMap(GroupCombinedStrategyDO::getId, Function.identity()));
        Map<Long, List<GroupCombinedStrategyDetailDO>> strategyDetailMap = groupCombinedStrategyDetailDAO.listByStrategyIds(strategyDOMap.values().stream()
                        .map(GroupCombinedStrategyDO::getId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(GroupCombinedStrategyDetailDO::getGroupStrategyId));
        for (Map.Entry<Long, List<GroupCombinedStrategyDetailDO>> entry : strategyDetailMap.entrySet()) {
            Long strategyId = entry.getKey();
            List<GroupCombinedStrategyDetailDO> details = entry.getValue();
            if (CollectionUtils.isEmpty(details) || details.size() != 2) {
                continue;
            }
            details.sort(Comparator.comparing(GroupCombinedStrategyDetailDO::getGroupType));
            GroupCombinedStrategyDetailDO existedMasterStrategyDetailDO = details.get(0);
            GroupCombinedStrategyDetailDO existedBackupStrategyDetailDO = details.get(1);
            if (StringUtils.equals(masterStrategyDetailDO.getAcquirer(), existedMasterStrategyDetailDO.getAcquirer())
                && StringUtils.equals(backupStrategyDetailDO.getAcquirer(), existedBackupStrategyDetailDO.getAcquirer())
                && StringUtils.equals(masterStrategyDetailDO.getGroupId(), existedMasterStrategyDetailDO.getGroupId())
                && StringUtils.equals(backupStrategyDetailDO.getGroupId(), existedBackupStrategyDetailDO.getGroupId())) {
                return Optional.of(strategyDOMap.get(strategyId));
            }
        }
        return Optional.empty();
    }
}
