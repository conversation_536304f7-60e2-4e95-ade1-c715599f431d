package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/19
 */
@Data
@TableName("mc_acquirer")
@Accessors(chain = true)
public class McAcquirerDO {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一标识
     */
    @TableField(value = "acquirer")
    private String acquirer;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 收单机构本身对应的provider
     */
    private String provider;

    /**
     * 交易类型  1间连交易  2直连交易
     */
    @TableField(value = "trade_type")
    private Integer tradeType;

    /**
     * 清算类型  1间接清算   2直接清算
     */
    @TableField(value = "clear_type")
    private Integer clearType;

    /**
     * 类型  1三方机构  2银行机构  3支付源直连机构
     */
    private Integer type;

    /**
     * 费率生效类型  1 以收钱吧为准  2 以收单机构为准
     */
    @TableField(value = "fee_effective_type")
    private Integer feeEffectiveType;

    /**
     * 清算通道标识 交易侧定义，间清时必须
     */
    @TableField(value = "clearance_provider")
    private Integer clearanceProvider;

    /**
     * 是否支持特殊行业
     */
    @TableField(value = "support_special_industry")
    private Integer supportSpecialIndustry;


    /**
     * 支持的结算通道列表
     */
    @TableField(value = "metadata")
    private String metadata;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    private Date createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    private Date updateAt;

}
