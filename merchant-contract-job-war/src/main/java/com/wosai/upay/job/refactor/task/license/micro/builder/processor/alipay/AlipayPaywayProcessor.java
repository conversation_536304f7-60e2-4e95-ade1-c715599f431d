package com.wosai.upay.job.refactor.task.license.micro.builder.processor.alipay;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.AuthStatusEnum;
import com.wosai.upay.job.refactor.task.license.micro.builder.context.TradeParamsBuilderContext;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.PaywayProcessor;
import com.wosai.data.bean.BeanUtil;

/**
 * 支付宝支付方式处理器
 *
 * <AUTHOR>
 */
public class AlipayPaywayProcessor implements PaywayProcessor {
    
    private final String acquirerType;

    private IndustryMappingCommonBiz industryMappingCommonBiz;

    private WechatAuthBiz wechatAuthBiz;
    
    public static final String AUTH_TIME = "auth_time";
    
    public AlipayPaywayProcessor(String acquirerType) {
        this.acquirerType = acquirerType;
    }
    
    @Override
    public void processParam(MerchantProviderParamsDO newParam,
                             MerchantProviderParamsDO oldParam,
                             TradeParamsBuilderContext context) {
        
        MerchantAcquireInfoBO merchantAcquireInfo = context.getMerchantAcquireInfo();
        
        // 设置支付宝商户号
        newParam.setPayMerchantId(merchantAcquireInfo.getAliNo());
        newParam.setStatus(1);
        // 设置行业分类码
        String industryId = BeanUtil.getPropString(context.getMerchant(), Merchant.INDUSTRY);
        String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
        newParam.setAliMcc(aliMcc);
        newParam.setAuthStatus(AuthStatusEnum.YES.getValue());
        // 设置认证时间
        newParam.setExtra(JSONObject.toJSONString(
                CollectionUtil.hashMap(AUTH_TIME, context.getAliAuthTime())));
        
        // 根据不同收单机构设置特定参数
        switch (acquirerType) {
            case "LKLV3":
                newParam.setProviderMerchantId(merchantAcquireInfo.getUnionNo());
                newParam.setMerchantName(wechatAuthBiz.getWechatAuthMerchantName(context.getContractParamContext()));
                break;
            case "FUYOU":
            case "HAIKE":
                newParam.setProviderMerchantId(merchantAcquireInfo.getAcquireMerchantId());
                newParam.setMerchantName(wechatAuthBiz.getWechatAuthMerchantName(context.getContractParamContext()));
                break;
            default:
                // 默认处理
                break;
        }
    }
    
    @Override
    public Integer getSupportedPayway() {
        return PaywayEnum.ALIPAY.getValue();
    }
    
    @Override
    public String getSupportedAcquirerType() {
        return acquirerType;
    }

    // Setter methods for dependency injection
    public void setIndustryMappingCommonBiz(IndustryMappingCommonBiz industryMappingCommonBiz) {
        this.industryMappingCommonBiz = industryMappingCommonBiz;
    }

    public void setWechatAuthBiz(WechatAuthBiz wechatAuthBiz) {
        this.wechatAuthBiz = wechatAuthBiz;
    }
}
