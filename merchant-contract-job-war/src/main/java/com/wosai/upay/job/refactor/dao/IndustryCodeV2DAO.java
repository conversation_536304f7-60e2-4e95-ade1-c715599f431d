package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.IndustryCodeV2DynamicMapper;
import com.wosai.upay.job.refactor.model.entity.IndustryCodeV2DO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;


/**
 * industry_code_v2表数据库访问层 {@link IndustryCodeV2DO}
 * 对IndustryCodeV2Mapper层做出简单封装 {@link IndustryCodeV2DynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class IndustryCodeV2DAO {

    @Resource
    private IndustryCodeV2DynamicMapper industryCodeV2DynamicMapper;


    /**
     * 根据行业id查询
     *
     * @param industryId 行业id
     * @return 行业信息
     */
    public Optional<IndustryCodeV2DO> getIndustryCodeV2ByIndustryId(String industryId) {
        if (StringUtils.isBlank(industryId)) {
            return Optional.empty();
        }
        return Optional.ofNullable(industryCodeV2DynamicMapper
                .selectOne(new LambdaQueryWrapper<IndustryCodeV2DO>().eq(IndustryCodeV2DO::getIndustryId, industryId).last("limit 1")));
    }

    /**
     * 获取所有的二级映射行业(数据量较少,直接获取所有)
     *
     * @return 所有的二级映射行业
     */
    public List<IndustryCodeV2DO> listAllIndustryCodeV2s() {
        return industryCodeV2DynamicMapper.selectList(null);
    }
}
