package com.wosai.upay.job.refactor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRulesDecisionDO;
import org.apache.ibatis.annotations.Select;

/**
 * 进件规则决策表表Mapper层 {@link GroupRouteRulesDecisionDO}
 *
 * <AUTHOR>
 */
public interface GroupRouteRulesDecisionMapper extends BaseMapper<GroupRouteRulesDecisionDO> {

    /**
     * 查询最大id
     *
     * @return 最大id
     */
    @Select("select max(id) from group_route_rules_decision")
    Long selectMaxId();
}

