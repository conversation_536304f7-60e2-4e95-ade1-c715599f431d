package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 收单组路由规则详情表表实体对象
 *
 * <AUTHOR>
 */
@TableName("group_route_rules_decision")
@Data
public class GroupRouteRulesDecisionDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 父规则决策id 0代表最上层规则决策
     */
    @TableField(value = "parent_id")
    private Long parentId;
    /**
     * 进件规则决策名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * 优先级
     */
    @TableField(value = "priority")
    private Integer priority;
    /**
     * 子规则连接类型(嵌套子规则） AND OR
     */
    @TableField(value = "rule_connection_type")
    private String ruleConnectionType;
    /**
     * 规则详情连接类型 AND OR
     */
    @TableField(value = "rule_detail_connection_type")
    private String ruleDetailConnectionType;
    /**
     * 选择类型 ENABLE-可选择 UNABLE-不可选择 ONLY_CAN-只可选择
     */
    @TableField(value = "choose_type")
    private String chooseType;
    /**
     * 收单组策略id，对应group_combined_strategy.id
     */
    @TableField(value = "group_strategy_id")
    private Long groupStrategyId;
    /**
     * 分类
     */
    @TableField(value = "classification")
    private String classification;
    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 有效状态 0-失效 1-生效
     */
    @TableField(value = "valid_status")
    private Integer validStatus;
    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;
    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Timestamp createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Timestamp updateTime;

}

