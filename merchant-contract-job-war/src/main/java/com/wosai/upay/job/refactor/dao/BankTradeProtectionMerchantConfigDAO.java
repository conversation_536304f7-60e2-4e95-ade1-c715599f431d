package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRulesDecisionDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.BankTradeProtectionMerchantConfigMapper;
import com.wosai.upay.job.refactor.model.entity.BankTradeProtectionMerchantConfigDO;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;


/**
 * 银行交易保障商户配置表表数据库访问层 {@link BankTradeProtectionMerchantConfigDO}
 * 对BankTradeProtectionMerchantConfigMapper层做出简单封装 {@link BankTradeProtectionMerchantConfigMapper}
 *
 * <AUTHOR>
 */
@Repository
public class BankTradeProtectionMerchantConfigDAO extends AbstractBaseDAO<BankTradeProtectionMerchantConfigDO, BankTradeProtectionMerchantConfigMapper> {

    private static final int DEFAULT_MAX_RETRY_ATTEMPTS = 2;

    private static final int DEFAULT_RETRY_DELAY_TIME = 1000;

    public BankTradeProtectionMerchantConfigDAO(SqlSessionFactory sqlSessionFactory, BankTradeProtectionMerchantConfigMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户sn更新银行交易保障开关状态
     *
     * @param merchantSn 商户sn
     * @param openStatus 开关状态
     * @return effect rows
     */
    @Retryable(value = Exception.class,maxAttempts = DEFAULT_MAX_RETRY_ATTEMPTS,backoff = @Backoff(delay = DEFAULT_RETRY_DELAY_TIME,multiplier = 0))
    public Integer insertOrUpdateOpenStatusByMerchantSn(String merchantSn, Integer openStatus) {
        BankTradeProtectionMerchantConfigDO configDO = new BankTradeProtectionMerchantConfigDO();
        configDO.setMerchantSn(merchantSn);
        configDO.setOpenStatus(openStatus);
        Optional<BankTradeProtectionMerchantConfigDO> existedConfig = getByMerchantSn(merchantSn);
        if (existedConfig.isPresent()) {
            configDO.setId(existedConfig.get().getId());
            return updateByPrimaryKeySelective(configDO);
        }
        return entityMapper.insert(configDO);
    }

    /**
     * 根据商户sn查询银行交易保障配置
     *
     * @param merchantSn 商户sn
     * @return 银行交易保障配置
     */
    public Optional<BankTradeProtectionMerchantConfigDO> getByMerchantSn(String merchantSn) {
        LambdaQueryWrapper<BankTradeProtectionMerchantConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BankTradeProtectionMerchantConfigDO::getMerchantSn, merchantSn);
        return selectOne(lambdaQueryWrapper);
    }

    /**
     * 根据商户sn列表查询银行交易保障配置
     *
     * @param merchantSns 商户sn列表
     * @return 银行交易保障配置列表
     */
    public List<BankTradeProtectionMerchantConfigDO> listByMerchantSns(List<String> merchantSns) {
        if (CollectionUtils.isEmpty(merchantSns)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BankTradeProtectionMerchantConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(BankTradeProtectionMerchantConfigDO::getMerchantSn, merchantSns);
        return entityMapper.selectList(lambdaQueryWrapper);
    }
}
