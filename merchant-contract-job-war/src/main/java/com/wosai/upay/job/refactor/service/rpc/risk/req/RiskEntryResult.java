package com.wosai.upay.job.refactor.service.rpc.risk.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 风控结果请求
 *
 * <AUTHOR>
 * @date 2024/9/30 16:30
 */
@JsonIgnoreProperties(
        ignoreUnknown = true
)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskEntryResult {

    public static final String CRM = "crm";

    public static final String SPA = "spa";

    public static final String APP = "app";

    private String merchantSn;
    private boolean success;

    private Map<String, Object> multiPlatformReason;

    @Deprecated
    private String reason;
}
