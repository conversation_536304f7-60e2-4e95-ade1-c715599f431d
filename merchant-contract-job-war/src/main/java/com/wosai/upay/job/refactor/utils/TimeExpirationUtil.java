package com.wosai.upay.job.refactor.utils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;

public class TimeExpirationUtil {

    /**
     * 判断时间和当前时间相比，是否超过指定天数（自然日）
     *
     * @param createTime 任务创建时间 (非空)
     * @param days 有效期天数阈值
     * @return true: 已过期 | false: 未过期
     * @throws IllegalArgumentException
     *         1. 如果 createTime 为 null
     *         2. 如果 days 为负数
     */
    public static boolean isTimeExpired(Timestamp createTime, int days) {
        validateParameters(createTime, days);
        LocalDate createDate = createTime.toInstant()
                .atZone(ZoneId.of("UTC"))
                .toLocalDate();
        long daysBetween = ChronoUnit.DAYS.between(
                createDate,
                LocalDate.now(ZoneId.of("UTC"))
        );
        return daysBetween >= days;
    }

    private static void validateParameters(Timestamp createTime, int days) {
        if (createTime == null) {
            throw new IllegalArgumentException("任务创建时间不能为空");
        }
        if (days < 0) {
            throw new IllegalArgumentException("有效期天数不能为负数，当前值：" + days);
        }
    }
}
