package com.wosai.upay.job.payactivity.alipay;

import com.google.common.base.Splitter;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.MchSnapshot;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.merchant.contract.model.bluesea.CustomizedInfo;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.wosai.upay.job.Constants.BlueSeaConstant.*;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/9/26 17:34
 */
@Slf4j
@Component
public class AlipayEduActivity extends AbstractAlipayActivityService {
    @Autowired
    private BlueSeaTaskMapper blueSeaTaskMapper;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private BlueSeaBiz blueSeaBiz;
    @Autowired
    private BlueSeaService blueSeaService;
    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;

    /**
     * 活动task生成
     *
     * @param merchantSn
     * @param auditId
     * @param activityType
     * @param formBody
     * @return
     */
    @Override
    public CommonResult createTaskApply(String merchantSn, Long auditId, int activityType, Map formBody) {
        CommonResult result;
        try {
            final String stores = BeanUtil.getPropString(formBody, "stores");
            final List<String> storeList = Splitter.on(",").splitToList(stores).stream().collect(Collectors.toList());
           storeList.stream().forEach(x -> {
               final BlueSeaTask blueSeaTask = blueSeaTaskMapper.selectTaskByStoreSnAndType(x, ALI_EDU_ACTIVITY);
               final BlueSeaTask task = Optional.ofNullable(blueSeaTask).orElseGet(BlueSeaTask::new);
               final Integer status = task.getStatus();
               if(Objects.equals(status, SUCCESS)) {
                   throw new ContractBizException(String.format("门店号%s间连教育培训活动已报名成功",x));
               }
               if(Objects.nonNull(status) && !Objects.equals(status,FAIL)) {
                   throw new ContractBizException(String.format("门店号%s间连教育培训活动正在进行中",x));
               }

           });
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            String merchantId = MapUtils.getString(merchant, CommonModel.ID);
            if (WosaiMapUtils.isEmpty(merchant)) {
                throw new ContractBizException("商户不存在");
            }
            Tuple2<String, Boolean> aliMchInfo = blueSeaBiz.getInUseMchId(merchantSn);
            String aliMchId = aliMchInfo.get_1();
            if (WosaiStringUtils.isEmpty(aliMchId)) {
                throw new ContractBizException("商户没有支付宝子商户号");
            }

            String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
            if (WosaiStringUtils.isEmpty(industryId)) {
                throw new ContractBizException("行业不满足活动要求");
            }
            String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
            // 活动支持的行业code 返回all支持所有的行业
            String supportIndustry = supportIndustry();
            if (!supportIndustry.equals("all") && !supportIndustry.contains(aliMcc)) {
                throw new ContractBizException("行业不满足活动要求");
            }

            /**不同活动特殊校验*/
            activityTypeCheck(merchant,formBody,aliMchInfo.get_2());

            /**不同活动mch_snapshot*/
            MchSnapshot snapshot = MchSnapshot.builder().aliMcc(aliMcc).direct(aliMchInfo.get_2()).build();
            buildSnapshot(snapshot, merchant,formBody,aliMcc,aliMchId);

            /**新增task表*/
            storeList.stream().forEach(x -> creatTaskWithStoreSn(merchantSn, merchantId, aliMchId, auditId, activityType, formBody, snapshot,x));

            result = new CommonResult(CommonResult.SUCCESS, "商户支付宝信息更新中");

        } catch (ContractException e) {
            log.error("商户{}申请支付宝活动{},业务异常", merchantSn, activityType);
            result = new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        } catch (Exception e) {
            log.error("商户{}申请支付宝活动{},系统异常", merchantSn, activityType);
            result = new CommonResult(CommonResult.ERROR, e.getMessage());
        }
        return result;
    }






    /**
     * 高校活动支持的商户行业
     *
     * @return
     */
    @Override
    public String supportIndustry() {
        return "all";
    }

    /**
     *
     * @param merchant 商户信息
     * @param formBody
     * @param direct 间直连表示 true 直连 false间连
     */
    @Override
    public void activityTypeCheck(Map merchant, Map formBody,Boolean direct) {
        if (direct) {
            throw new ContractBizException("该活动仅支持间连商户参加");
        }
        formBody.forEach((k,v) -> {
            if(Objects.isNull(v)) {
                throw new ContractBizException(k+"的值为空");
            }
        });
    }

    @Override
    public MchSnapshot buildSnapshot(MchSnapshot snapshot, Map merchant, Map formBody, String aliMcc, String aliMchId) {
        final String mcc = BeanUtil.getPropString(formBody, "mcc");
        snapshot.setAliMcc(mcc);
        return snapshot;
    }

    /**
     * 活动前置处理
     * 前置状态包含三个PENDING/M3/SHOP_CREATED
     *
     * @param task
     */
    @Override
    public void activityApplyPreHandle(BlueSeaTask task) {
        return;
    }


    /**
     * 活动报名
     *
     * @param task
     */
    @Override
    public void activityApply(BlueSeaTask task) {
    }

    /**
     * 活动成功后置处理
     *
     * @param task
     */
    @Override
    public void activityApplyPostHandle(BlueSeaTask task) {
        return;
//        //根据门店号获取门店Id
//        Map store = storeService.getStoreByStoreSn(task.getStore_sn());
//        if (MapUtils.isEmpty(store)) {
//            throw new CommonPubBizException("门店不存在");
//        }
//        final String id = BeanUtil.getPropString(store, DaoConstants.ID);
//        final int tagValue = apolloParamsConfig.getInt(TAG_FORMPAY, 1);
//        final Map map = CollectionUtil.hashMap("store_id", id, "key", TAG_FORMPAY,"value",tagValue);
//        qrcodeService.saveStoreTag(map);
    }



    /**
     * 拒绝审批
     *
     * @param merchantSn
     * @param auditId
     * @param activityType
     * @param formBody
     * @return
     */
    @Override
    public CommonResult auditReject(String merchantSn, Long auditId, int activityType, Map formBody) {
        CommonResult result;
        try {
            List<BlueSeaTask> tasks = blueSeaTaskMapper.selectListByAuditId(merchantSn, activityType, auditId);
            if (!CollectionUtils.isEmpty(tasks)) {
                tasks.stream().forEach(task-> {
                    blueSeaTaskMapper.updateStatusById(task.getId(), FAIL, "审批驳回");
                });
            }
            String msg = tasks == null ? "商户没有此审批" : "审批驳回处理成功";
            result = new CommonResult(CommonResult.SUCCESS, msg);
        } catch (Exception e) {
            log.error("商户{}申请支付宝活动 审批驳回处理失败", merchantSn);
            result = new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        }
        return result;
    }

    @Override
    public CommonResult auditApprove(String merchantSn, Long auditId, int activityType, Map formBody) {
        final CommonResult result = new CommonResult().setCode(CommonResult.SUCCESS).setMsg("处理中");
        List<BlueSeaTask> blueSeaTasks = blueSeaTaskMapper.selectListByAuditId(merchantSn, activityType, auditId);
        if (CollectionUtils.isEmpty(blueSeaTasks)) {
            return result.setCode(CommonResult.ERROR).setMsg("审批失败,请关注上文提示信息");
        }
        try {
            blueSeaTasks.stream().forEach(blueSeaTask -> {
                if (Objects.equals(blueSeaTask.getStatus(), PENDING)) {
                    CustomizedInfo customizedInfo = blueSeaBiz.getCustomizedInfo(blueSeaTask);
                    //升级M3同时修改mcc和商户名称
                    try {
                        final boolean toM3 = blueSeaService.updateMerchantToM3(blueSeaTask.getMerchant_sn(), customizedInfo);
                        if(toM3) {
                            MchSnapshot mchSnapshot = new MchSnapshot();
                            mchSnapshot.setHoldHandle("1");
                            blueSeaBiz.updateStatus(blueSeaTask.getId(), M3, null, mchSnapshot, null, null, null,  2);
                        }else {
                            blueSeaBiz.updateStatus(blueSeaTask.getId(), FAIL, "商户升级M3失败", null, null, null, null, 2);
                        }

                    } catch (Exception exception) {
                        log.error("approveEduActivity updateMerchantToM3 error:{}", exception);
                    }
                }
                if (Objects.equals(blueSeaTask.getStatus(), M3)) {
                    MchSnapshot mchSnapshot = new MchSnapshot();
                    mchSnapshot.setHoldHandle("1");
                    blueSeaBiz.updateStatus(blueSeaTask.getId(), blueSeaTask.getStatus(), null, mchSnapshot, null, null, null, 2);
                }
                if (Objects.equals(blueSeaTask.getStatus(), FAIL)) {
                    throw new CommonPubBizException(blueSeaTask.getDescription());
                }
            });
        } catch (Exception exception) {
            log.error("approveEduActivity error:{}", exception);
            //将任务置为失败
            result.setCode(CommonResult.BIZ_FAIL).setMsg(ExceptionUtil.getThrowableMsg(exception));
        }
        return result;
    }


}
