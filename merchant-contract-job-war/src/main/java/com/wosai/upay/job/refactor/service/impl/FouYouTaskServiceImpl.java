package com.wosai.upay.job.refactor.service.impl;

import com.beust.jcommander.internal.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.*;
import com.shouqianba.cua.utils.object.DateExtensionUtils;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.model.dto.response.InsertFuYouDayZeroTaskResultRspDTO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.refactor.model.enums.PaywayEnum;
import com.wosai.upay.job.refactor.biz.acquirer.fuyou.FuYouAcquirerFacade;
import com.wosai.upay.job.service.FuYouTaskService;
import com.wosai.upay.side.service.GeneralRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;

/**
 * 触发(xxl-job)富友任务调度
 *
 * <AUTHOR>
 * @date 2024/5/13 09:38
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class FouYouTaskServiceImpl extends AbstractContractTaskHandler implements FuYouTaskService  {

    @Resource
    private FuYouAcquirerFacade fuyouAcquirerFacade;

    @Resource(name = "generalRuleService")
    private GeneralRuleService generalRuleService;

    private static final Integer DEFAULT_OPEN_DAY_ZERO_DAYS_LIMIT = 3;


    /**
     * 新增富友开通D0任务
     *
     * @param merchantSn        商户号
     * @param duplicationCheck  重复校验 true-判断任务是否已存在,存在不会创建任务 false-不判断
     * @param completeDaysLimit 任务完成天数限制 null-默认天数限制
     * @return 是否成功 true-成功
     */
    @Override
    public InsertFuYouDayZeroTaskResultRspDTO insertFuYouOpenDayZeroTask(String merchantSn, boolean duplicationCheck, Integer completeDaysLimit) {
        if (!isFuYouMerchantStatusNormal(merchantSn)) {
            return new InsertFuYouDayZeroTaskResultRspDTO(false, "该商户状态在富友侧已关闭");
        }
        InsertFuYouDayZeroTaskResultRspDTO rspDTO = new InsertFuYouDayZeroTaskResultRspDTO();
        if (duplicationCheck) {
            Optional<ContractTaskDO> taskDOOptional = contractTaskDAO.getLastedTaskBySnAndType(merchantSn, ContractTaskTypeEnum.OPEN_DAY_ZERO.getValue());
            if (taskDOOptional.isPresent()) {
                rspDTO.setInsertSuccess(false);
                rspDTO.setMessage("任务已存在");
                return rspDTO;
            }
        }
        // 删除失败的任务
        contractTaskDAO.batchDeleteFailTaskAndSubTask(merchantSn, ContractTaskTypeEnum.OPEN_DAY_ZERO.getValue());

        ContractTaskDO contractTaskDO = new ContractTaskDO();
        int limitDay = Objects.nonNull(completeDaysLimit) ? completeDaysLimit : DEFAULT_OPEN_DAY_ZERO_DAYS_LIMIT;
        try {
            String completeAt = generalRuleService.getWeekDayByDays(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), limitDay);
            contractTaskDO.setCompleteAt(DateExtensionUtils.parseTimestamp(completeAt));
        } catch (ParseException e) {
            log.warn("日期转换错误", e);
            contractTaskDO.setCompleteAt(DateExtensionUtils.addDays(new Timestamp(System.currentTimeMillis()), limitDay));
        }
        contractTaskDO.setMerchantSn(merchantSn);
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        if (Objects.isNull(merchant)) {
            rspDTO.setInsertSuccess(false);
            rspDTO.setMessage("商户不存在");
            return rspDTO;
        }
        contractTaskDO.setMerchantName(merchant.getName());
        contractTaskDO.setType(ContractTaskTypeEnum.OPEN_DAY_ZERO.getValue());
        contractTaskDO.setStatus(ContractTaskProcessStatusEnum.WAIT_PROCESS.getValue());
        contractTaskDO.setRuleGroupId(McConstant.RULE_GROUP_FUYOU);
        contractTaskDO.setAffectSubTaskCount(2);
        contractTaskDO.setAffectStatusSuccessTaskCount(0);
        ContractSubTaskDO openD0SubTask = buildOpenD0SubTask(merchantSn);
        ContractSubTaskDO queryOpenD0ResultTask = buildQueryOpenD0ResultTask(merchantSn);
        contractTaskDAO.batchInsertTasks(contractTaskDO, Lists.newArrayList(openD0SubTask, queryOpenD0ResultTask));
        rspDTO.setInsertSuccess(true);
        return rspDTO;
    }

    private boolean isFuYouMerchantStatusNormal(String merchantSn) {
        try {
            AcquirerMerchantStatusEnum acquirerMerchantStatus = fuyouAcquirerFacade.getAcquirerMerchantStatus(merchantSn);
            return Objects.equals(AcquirerMerchantStatusEnum.NORMAL, acquirerMerchantStatus);
        } catch (Exception e) {
            log.error("get fuYou merchant status error, merchantSn:{}", merchantSn, e);
            return true;
        }

    }

    private ContractSubTaskDO buildQueryOpenD0ResultTask(String merchantSn) {
        ContractSubTaskDO queryOpenD0ResultTask = new ContractSubTaskDO();
        queryOpenD0ResultTask.setPayway(PaywayEnum.ACQUIRER.getValue());
        queryOpenD0ResultTask.setMerchantSn(merchantSn);
        queryOpenD0ResultTask.setStatus(ContractTaskProcessStatusEnum.WAIT_PROCESS.getValue());
        queryOpenD0ResultTask.setScheduleStatus(ScheduleStatusEnum.NOT_CAN.getValue());
        queryOpenD0ResultTask.setStatusInfluPTask(AffectPrimaryTaskStatusEnum.YES.getValue());
        queryOpenD0ResultTask.setTaskType(ContractSubTaskTypeEnum.QUERY_DAY_ZERO_OPEN_RESULT.getValue());
        queryOpenD0ResultTask.setRuleGroupId(McConstant.RULE_GROUP_FUYOU);
        return queryOpenD0ResultTask;
    }

    private ContractSubTaskDO buildOpenD0SubTask(String merchantSn) {
        ContractSubTaskDO openD0SubTask = new ContractSubTaskDO();
        openD0SubTask.setPayway(PaywayEnum.ACQUIRER.getValue());
        openD0SubTask.setMerchantSn(merchantSn);
        openD0SubTask.setStatus(ContractTaskProcessStatusEnum.WAIT_PROCESS.getValue());
        openD0SubTask.setScheduleStatus(ScheduleStatusEnum.CAN.getValue());
        openD0SubTask.setStatusInfluPTask(AffectPrimaryTaskStatusEnum.YES.getValue());
        openD0SubTask.setTaskType(ContractSubTaskTypeEnum.OPEN_DAY_ZERO.getValue());
        openD0SubTask.setRuleGroupId(McConstant.RULE_GROUP_FUYOU);
        return openD0SubTask;
    }

    /**
     * 根据任务id触发富友开通D0任务
     *
     * @param taskId 任务id
     * @param force 是否强制处理任务
     */
    @Override
    public void processFuYouOpenDayZeroTaskByTaskId(Long taskId, boolean force) {
        fuyouAcquirerFacade.openDayZero(contractTaskDAO.getByPrimaryKey(taskId).orElse(null), force);
    }

    /**
     * 处理富友开通D0定时任务(xxl-job调度)
     *
     * @param beginTimeGapDays 开始时间间隔(天数)
     * @param taskNum          任务数量
     * @param force            是否强制处理任务
     */
    @Override
    public void processFuYouOpenDayZeroTask(Integer beginTimeGapDays , Integer taskNum, boolean force) {
        Long timeGap = (long) (beginTimeGapDays * 24 * 60 * 60 * 1000);
        super.batchProcessPrimaryTasks(ContractTaskTypeEnum.OPEN_DAY_ZERO.getValue(), ContractTaskProcessStatusEnum.WAIT_PROCESS.getValue(),
                timeGap, taskNum, false, true,
                contractTaskDO -> fuyouAcquirerFacade.openDayZero(contractTaskDO, force));
    }

    /**
     * 获取富友D0开通结果定时任务(xxl-job调度)
     *
     * @param beginTimeGapDays 开始时间间隔(天数)
     * @param taskNum          任务数量
     * @param force            是否强制处理任务
     */
    @Override
    public void queryFuYouOpenDayZeroResultTask(Integer beginTimeGapDays, Integer taskNum, boolean force) {
        Long timeGap = (long) (beginTimeGapDays * 24 * 60 * 60 * 1000);
        super.batchProcessPrimaryTasks(ContractTaskTypeEnum.OPEN_DAY_ZERO.getValue(), ContractTaskProcessStatusEnum.AUDITING.getValue(),
                timeGap, taskNum, false, true,
                contractTaskDO -> fuyouAcquirerFacade.queryOpenDayZeroResult(contractTaskDO, force));
    }

    /**
     * 根据任务id手动触发查询富友D0开通结果
     *
     * @param taskId 任务id
     * @return 开通结果
     */
    @Override
    public String queryFuYouOpenDayZeroResultByTaskId(Long taskId) {
        return fuyouAcquirerFacade.queryOpenDayZeroResult(contractTaskDAO.getByPrimaryKey(taskId).orElse(null), true);
    }

}
