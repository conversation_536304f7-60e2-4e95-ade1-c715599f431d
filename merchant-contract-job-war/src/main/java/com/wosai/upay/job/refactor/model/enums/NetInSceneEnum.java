package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 入网场景枚举
 *
 * <AUTHOR>
 */
public enum NetInSceneEnum implements ITextValueEnum<String> {

    BUSINESS_OPENING("BUSINESS_OPENING", "业务开通"),

    CHANNEL_SWITCHING("CHANNEL_SWITCHING", "通道切换");

    private final String value;
    private final String text;

    NetInSceneEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
