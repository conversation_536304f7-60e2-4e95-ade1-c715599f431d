package com.wosai.upay.job.refactor.model.enums;

/**
 * 授权状态枚举
 *
 * <AUTHOR>
 */
public enum AuthStatusEnum implements ITextValueEnum {
    /**
     * 未授权
     */
    NOT(0, "未授权"),
    /**
     * 已授权
     */
    YES(1, "已授权");

    private final int value;
    private final String text;

    AuthStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
