package com.wosai.upay.job.refactor.model.enums;

/**
 * 使用状态枚举
 *
 * <AUTHOR>
 */
public enum UseStatusEnum implements ITextValueEnum {
    /**
     * 未使用
     */
    NO_USE(0, "未使用"),
    /**
     * 已使用
     */
    IN_USE(1, "已使用");

    private final int value;
    private final String text;

    UseStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
