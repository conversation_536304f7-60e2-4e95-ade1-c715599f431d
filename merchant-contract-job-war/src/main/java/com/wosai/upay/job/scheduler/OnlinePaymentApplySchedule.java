package com.wosai.upay.job.scheduler;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.OnlinePaymentConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.model.MailConfigModel;
import com.wosai.upay.job.biz.OnlinePaymentBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.externalservice.mail.MailClient;
import com.wosai.upay.job.externalservice.mail.model.MailSendReq;
import com.wosai.upay.job.externalservice.mail.model.MailSendResp;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.OpenOnlinePaymentApplyDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.OpenOnlinePaymentApplyDO;
import com.wosai.upay.job.util.DateUtil;
import com.wosai.upay.job.util.ExcelUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import sun.misc.BASE64Encoder;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/21
 */
@Component
@Slf4j
public class OnlinePaymentApplySchedule {

    @Autowired
    private RedisLock redisLock;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private OnlinePaymentBiz onlinePaymentBiz;
    @Autowired
    private OpenOnlinePaymentApplyDAO openOnlinePaymentApplyDAO;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private DateUtil dateUtil;
    @Autowired
    private MailClient mailClient;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    private static final String OPEN_ONLINE_PAYMENT_KEY = "open_online_payment::";

    public void reContractAndSubmitAuth() {
        String key = OPEN_ONLINE_PAYMENT_KEY + "reContractAndSubmitAuth";
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return;
        }
        try {
            List<OpenOnlinePaymentApplyDO> applyList = openOnlinePaymentApplyDAO.queryWaitForReContractApplies();
            if (CollectionUtils.isEmpty(applyList)) {
                return;
            }
            applyList.parallelStream().forEach(apply -> {
                try {
                    onlinePaymentBiz.reContractAndSubmitAuth(apply);
                } catch (Exception exception) {
                    onlinePaymentBiz.processApplyException(apply, OnlinePaymentConstant.ApplyProcessStatus.CONTRACT_FAIL, exception);
                    log.error("商户开通线上收款异常:重新报备失败 {} ", apply.getMerchantSn(), exception);
                }
            });
        } catch (Exception e) {
            log.error("open_online_payment_apply exception ", e);
        } finally {
            redisLock.unlock(key, value);
        }
    }

    public void queryAuth() {
        String key = OPEN_ONLINE_PAYMENT_KEY + "queryAuth";
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return;
        }
        try {
            List<OpenOnlinePaymentApplyDO> applyList = openOnlinePaymentApplyDAO.queryWaitForAuthApplies();
            if (CollectionUtils.isEmpty(applyList)) {
                return;
            }
            applyList.forEach(apply -> {
                try {
                    onlinePaymentBiz.queryAuthStatusAndSetDefault(apply);
                } catch (ContractSysException exception) {
                    log.error("商户开通线上收款异常:查询微信子商户号授权状态系统异常 {} ", apply.getMerchantSn(), exception);
                } catch (Exception exception) {
                    onlinePaymentBiz.processApplyException(apply, OnlinePaymentConstant.ApplyProcessStatus.QUERY_AUTH_FAIL, exception);
                    log.error("商户开通线上收款异常:查询微信子商户号授权状态失败 {} ", apply.getMerchantSn(), exception);
                }
            });
        } catch (Exception e) {
            log.error("open_online_payment_apply exception ", e);
        } finally {
            redisLock.unlock(key, value);
        }
    }


    public void setFailIfAuthTimeout() {
        String key = OPEN_ONLINE_PAYMENT_KEY + "setFailIfAuthTimeout";
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return;
        }
        try {
            List<OpenOnlinePaymentApplyDO> applyList = openOnlinePaymentApplyDAO.queryTimeoutWaitForAuthApplies();
            if (CollectionUtils.isEmpty(applyList)) {
                return;
            }
            applyList.forEach(apply -> {
                try {
                    onlinePaymentBiz.queryAuthStatusAndSetDefault(apply);
                } catch (ContractSysException exception) {
                    log.error("商户开通线上收款异常:查询授权过期申请单系统异常 {} ", apply.getMerchantSn(), exception);
                } catch (Exception exception) {
                    onlinePaymentBiz.processApplyException(apply, OnlinePaymentConstant.ApplyProcessStatus.QUERY_AUTH_FAIL, exception);
                    log.error("商户开通线上收款异常:查询授权过期申请单失败 {} ", apply.getMerchantSn(), exception);
                }
            });
        } catch (Exception e) {
            log.error("open_online_payment_apply setFailIfAuthTimeout exception ", e);
        } finally {
            redisLock.unlock(key, value);
        }
    }

    public void sendEmail() {
        String key = OPEN_ONLINE_PAYMENT_KEY + "sendEmail";
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return;
        }
        try {
            List<OpenOnlinePaymentApplyDO> applies = openOnlinePaymentApplyDAO.queryAliWaitForAuditApplies();
            List<String[]> list = new ArrayList<>();
            list.add(new String[]{"收钱吧商户号", "支付宝子商户号", "收单机构商户号", "收单机构", "商户名"});

            Tuple2<Map<String, MerchantProviderParamsDO>, Map<String, String>> merchantProviderParamsAndMerchantNames = getMerchantProviderParamsAndMerchantNames(applies);
            Map<String, MerchantProviderParamsDO> merchantProviderParams = merchantProviderParamsAndMerchantNames.get_1();
            Map<String, String> merchantMap = merchantProviderParamsAndMerchantNames.get_2();
            for (OpenOnlinePaymentApplyDO apply : applies) {
                String merchantSn = apply.getMerchantSn();
                String payMerchantId = apply.getPayMerchantId();
                String merchantName = WosaiMapUtils.getString(merchantMap, merchantSn);
                String providerMerchantId = Optional.ofNullable(merchantProviderParams.get(payMerchantId)).orElse(new MerchantProviderParamsDO()).getProviderMerchantId();
                String acquirer = apply.getAcquirer();
                list.add(new String[]{merchantSn, payMerchantId, providerMerchantId, acquirer, merchantName});
            }
            byte[] content = ExcelUtil.writeExcel("支付宝大额待加白商户", list);
            MailConfigModel mailConfig = applicationApolloConfig.getMailConfig();
            MailConfigModel.MailConfigDetail onlineSqb = mailConfig.getOnlineSqb();
            if (Objects.isNull(onlineSqb) || WosaiStringUtils.isEmpty(onlineSqb.getId()) || !onlineSqb.isEnable()) {
                log.error("[收钱吧]未配置邮件配置信息或未启用");
                return;
            }
            MailSendReq sendReq = new MailSendReq()
                    .setId(Long.parseLong(onlineSqb.getId()))
                    .setTo(onlineSqb.getTo());
            DateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            if (Objects.isNull(content) || WosaiCollectionUtils.isEmpty(applies)) {
                sendReq.setContent(String.format("<p>今日没有支付宝大额待加白商户%s</p>", formatter.format(new Date())));
            } else {
                sendReq.setContent(String.format("<p>支付宝大额待加白商户%s</p>", formatter.format(new Date())));
                sendReq.setAttachments(Collections.singletonList(
                        CollectionUtil.hashMap(
                                "name", String.format("支付宝大额待加白商户%s.xlsx", formatter.format(new Date())),
                                "data", new BASE64Encoder().encode(content))
                ));
            }
            MailSendResp mailSendResp = mailClient.sendEmailWithRetry(sendReq);
            log.info("发送支付宝大额待加白商户邮件结果 {}", JSON.toJSONString(mailSendResp));
        } catch (Exception e) {
            log.error("open_online_payment_apply send email exception ", e);
        } finally {
            redisLock.unlock(key, value);
        }
    }

    public void sendEmailForTargetDataAndAcquirer(String targetDateTime, String acquirer) {
        LocalDateTime localDateTime = LocalDateTime.parse(targetDateTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        if (AcquirerTypeEnum.HAI_KE.getValue().equals(acquirer)) {
            sendHaikeEmail(localDateTime);
        } else {
            sendLklEmail(localDateTime);
        }
    }

    public void sendHaikeEmail(LocalDateTime targetDateTime) {
        String key = OPEN_ONLINE_PAYMENT_KEY + "sendHaikeEmail";
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return;
        }
        try {
            List<String[]> list = new ArrayList<>();
            list.add(new String[]{"申报日期", "支付宝smid", "商户名称", "作业模式（新增/存量）", "海科商户号"});
            generateExcelDataByAcquirer(list, targetDateTime, AcquirerTypeEnum.HAI_KE.getValue());
            if (list.size() == 1) {
                log.info("[海科]今日没有支付宝大额待加白商户");
                return;
            }
            byte[] content = ExcelUtil.writeExcel("商户明细", list);
            if (Objects.isNull(content)) {
                log.info("[海科]今日没有支付宝大额待加白商户");
                return;
            }
            MailConfigModel mailConfig = applicationApolloConfig.getMailConfig();
            MailConfigModel.MailConfigDetail onlineHaike = mailConfig.getOnlineHaike();
            if (Objects.isNull(onlineHaike) || WosaiStringUtils.isEmpty(onlineHaike.getId()) || !onlineHaike.isEnable()) {
                log.error("[海科]未配置邮件配置信息或未启用");
                return;
            }
            MailSendReq sendReq = new MailSendReq()
                    .setId(Long.parseLong(onlineHaike.getId()))
                    .setTo(onlineHaike.getTo())
                    .setContent(onlineHaike.getContent())
                    .setAttachments(Collections.singletonList(
                            CollectionUtil.hashMap(
                                    "name", String.format("海科商户明细_%s.xlsx", LocalDate.now().format(DateUtil.DATE_FORMATTER)),
                                    "data", new BASE64Encoder().encode(content))
                    ));
            MailSendResp mailSendResp = mailClient.sendEmailWithRetry(sendReq);
            log.info("[海科]发送支付宝大额待加白商户邮件结果 {}", JSON.toJSONString(mailSendResp));
        } catch (Exception e) {
            log.error("[海科]发送支付宝大额待加白商户邮件异常", e);
        } finally {
            redisLock.unlock(key, value);
        }
    }

    public void sendLklEmail(LocalDateTime targetDateTime) {
        String key = OPEN_ONLINE_PAYMENT_KEY + "sendLklEmail";
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return;
        }
        try {
            List<String[]> list = new ArrayList<>();
            list.add(new String[]{"申报日期", "支付宝smid", "商户名称", "作业模式（新增/存量）", "拉卡拉商户号"});
            generateExcelDataByAcquirer(list, targetDateTime, AcquirerTypeEnum.LKL_V3.getValue());
            if (list.size() == 1) {
                log.info("[拉卡拉]今日没有支付宝大额待加白商户");
                return;
            }
            byte[] content = ExcelUtil.writeExcel("商户明细", list);
            if (Objects.isNull(content)) {
                log.info("[拉卡拉]今日没有支付宝大额待加白商户");
                return;
            }
            MailConfigModel mailConfig = applicationApolloConfig.getMailConfig();
            MailConfigModel.MailConfigDetail onlineLkl = mailConfig.getOnlineLkl();
            if (Objects.isNull(onlineLkl) || WosaiStringUtils.isEmpty(onlineLkl.getId()) || !onlineLkl.isEnable()) {
                log.error("[拉卡拉]未配置邮件配置信息或未启用");
                return;
            }
            MailSendReq sendReq = new MailSendReq()
                    .setId(Long.parseLong(onlineLkl.getId()))
                    .setTo(onlineLkl.getTo())
                    .setContent(onlineLkl.getContent())
                    .setAttachments(Collections.singletonList(
                            CollectionUtil.hashMap(
                                    "name", String.format("拉卡拉商户明细_%s.xlsx", LocalDate.now().format(DateUtil.DATE_FORMATTER)),
                                    "data", new BASE64Encoder().encode(content))
                    ));
            MailSendResp mailSendResp = mailClient.sendEmailWithRetry(sendReq);
            log.info("[拉卡拉]发送支付宝大额待加白商户邮件结果 {}", JSON.toJSONString(mailSendResp));
        } catch (Exception e) {
            log.error("[拉卡拉]发送支付宝大额待加白商户邮件异常", e);
        } finally {
            redisLock.unlock(key, value);
        }
    }

    private void generateExcelDataByAcquirer(List<String[]> list, LocalDateTime targetDateTime, String acquirer) {
        LocalTime localTime = targetDateTime.toLocalTime();
        List<LocalDate> localDates = dateUtil.getNonWorkingDaysBefore(targetDateTime.toLocalDate());
        for (LocalDate localDate : localDates) {
            LocalDateTime start = localDate.minusDays(1).atTime(localTime);
            LocalDateTime end = localDate.atTime(localTime);
            List<OpenOnlinePaymentApplyDO> applyList = openOnlinePaymentApplyDAO.queryAliWaitForAuditAppliesByPriorityAndAcquirer(start, end, acquirer);
            if (WosaiCollectionUtils.isEmpty(applyList)) {
                continue;
            }
            Tuple2<Map<String, MerchantProviderParamsDO>, Map<String, String>> merchantProviderParamsAndMerchantNames = getMerchantProviderParamsAndMerchantNames(applyList);
            Map<String, MerchantProviderParamsDO> merchantProviderParams = merchantProviderParamsAndMerchantNames.get_1();
            Map<String, String> merchantMap = merchantProviderParamsAndMerchantNames.get_2();
            for (OpenOnlinePaymentApplyDO apply : applyList) {
                String applyDate = localDate.format(DateUtil.SLASH_DATE_FORMATTER);
                String payMerchantId = apply.getPayMerchantId();
                String merchantName = WosaiMapUtils.getString(merchantMap, apply.getMerchantSn());
                String providerMerchantId = Optional.ofNullable(merchantProviderParams.get(payMerchantId)).orElse(new MerchantProviderParamsDO()).getProviderMerchantId();
                list.add(new String[]{applyDate, payMerchantId, merchantName, "存量", providerMerchantId});
            }
        }
    }

    private Tuple2<Map<String, MerchantProviderParamsDO>, Map<String, String>> getMerchantProviderParamsAndMerchantNames(List<OpenOnlinePaymentApplyDO> applyList) {
        if (WosaiCollectionUtils.isEmpty(applyList)) {
            return new Tuple2<>(Collections.emptyMap(), Collections.emptyMap());
        }
        List<String> merchantSns = new ArrayList<>();
        List<String> payMerchantIds = new ArrayList<>();
        applyList.forEach(apply -> {
            merchantSns.add(apply.getMerchantSn());
            payMerchantIds.add(apply.getPayMerchantId());
        });
        Map<String, MerchantProviderParamsDO> merchantProviderParams = merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantIdList(payMerchantIds);
        Map<String, String> merchantMap = findMerchantNames(merchantSns);
        return new Tuple2<>(merchantProviderParams, merchantMap);
    }

    private Map<String, String> findMerchantNames(List<String> merchantSns) {
        if (WosaiCollectionUtils.isEmpty(merchantSns)) {
            return Collections.emptyMap();
        }
        ListResult listResult = merchantService.findSimpleMerchants(new PageInfo(1, merchantSns.size()), CollectionUtil.hashMap("merchant_sns", merchantSns));
        if (Objects.isNull(listResult) || WosaiCollectionUtils.isEmpty(listResult.getRecords())) {
            return Collections.emptyMap();
        }
        return listResult.getRecords().stream().collect(Collectors.toMap(m -> (String) m.get(Merchant.SN), m -> (String) m.get(Merchant.NAME)));
    }
}
