package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.MerchantAcquirerInfoMapper;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 商户所在收单机构信息表表数据库访问层 {@link MerchantAcquirerInfoDO}
 * 对MerchantAcquirerInfoMapper层做出简单封装 {@link MerchantAcquirerInfoMapper}
 *
 * <AUTHOR>
 */
@Repository
public class MerchantAcquirerInfoDAO extends AbstractBaseDAO<MerchantAcquirerInfoDO, MerchantAcquirerInfoMapper> {

    public MerchantAcquirerInfoDAO(SqlSessionFactory sqlSessionFactory, MerchantAcquirerInfoMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    private static final int DEFAULT_MAX_RETRY_ATTEMPTS = 2;

    private static final int DEFAULT_RETRY_DELAY_TIME = 1000;

    /**
     * 根据商户号和收单机构获取商户所在收单机构的商户信息
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @return 商户所在收单机构的商户信息
     */
    public Optional<MerchantAcquirerInfoDO> getByMerchantSnAndAcquirer(String merchantSn, String acquirer) {
        if (StringUtils.isBlank(merchantSn) || StringUtils.isBlank(acquirer)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<MerchantAcquirerInfoDO> query = new LambdaQueryWrapper<>();
        query.eq(MerchantAcquirerInfoDO::getMerchantSn, merchantSn).eq(MerchantAcquirerInfoDO::getAcquirer, acquirer);
        return selectOne(query);
    }

    public Optional<MerchantAcquirerInfoDO> getByAcquirerMerchantId(String acquirerMerchantId) {
        if (StringUtils.isBlank(acquirerMerchantId)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<MerchantAcquirerInfoDO> query = new LambdaQueryWrapper<>();
        query.eq(MerchantAcquirerInfoDO::getAcquirerMerchantId, acquirerMerchantId);
        return selectOne(query);
    }

    public List<MerchantAcquirerInfoDO> getByAcquirerAndCtime(String acquirer, String cTime) {
        if (StringUtils.isBlank(acquirer)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MerchantAcquirerInfoDO> query = new LambdaQueryWrapper<>();
        query.eq(MerchantAcquirerInfoDO::getAcquirer, acquirer);
        query.ge(MerchantAcquirerInfoDO::getCtime, cTime);
        return entityMapper.selectList(query);
    }

    /**
     * 插入或更新商户所在收单机构的商户信息
     *
     * @param merchantAcquirerInfoDO 商户所在收单机构的商户信息
     */
    @Retryable(value = Exception.class, maxAttempts = DEFAULT_MAX_RETRY_ATTEMPTS, backoff = @Backoff(delay = DEFAULT_RETRY_DELAY_TIME, multiplier = 0))
    public void insertOrUpdateOne(MerchantAcquirerInfoDO merchantAcquirerInfoDO) {
        if (Objects.isNull(merchantAcquirerInfoDO) || StringUtils.isBlank(merchantAcquirerInfoDO.getMerchantSn()) || StringUtils.isBlank(merchantAcquirerInfoDO.getAcquirer())) {
            return;
        }
        Optional<MerchantAcquirerInfoDO> merchantInfoOpt = getByMerchantSnAndAcquirer(merchantAcquirerInfoDO.getMerchantSn(), merchantAcquirerInfoDO.getAcquirer());
        if (merchantInfoOpt.isPresent()) {
            merchantAcquirerInfoDO.setId(merchantInfoOpt.get().getId());
            updateByPrimaryKeySelective(merchantAcquirerInfoDO);
        } else {
            insertOne(merchantAcquirerInfoDO);
        }
    }

}
