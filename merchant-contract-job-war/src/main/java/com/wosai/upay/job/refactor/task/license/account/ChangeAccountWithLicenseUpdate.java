package com.wosai.upay.job.refactor.task.license.account;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.businesslog.LogPlatformEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.DeleteStatusEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.model.Request;
import com.wosai.upay.bank.service.BankAccountService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.enume.PlatformEnum;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.service.rpc.bank.AccountBankAccountService;
import com.wosai.upay.job.refactor.service.rpc.bank.BankLicenseUpdateChangeCardService;
import com.wosai.upay.job.refactor.task.license.entity.BankAccountCertificateBO;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseAuditApplyDTO;
import com.wosai.upay.job.refactor.model.enums.DefaultStatusEnum;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV2MainTaskContext;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import com.wosai.upay.job.service.BankDirectService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 营业执照变更-保存或者更新结算账户到收钱吧侧
 *
 * <AUTHOR>
 * @date 2025/2/20 11:19
 */
@Service
@Slf4j
public class ChangeAccountWithLicenseUpdate {

    /**
     * 修改银行卡信息一级code
     */
    public final static String MERCHANT_BANK_ACCOUNT_CHANGE_TEMPLATE_CODE = "411LO8R0SNPB";

    /**
     * 营业执照变更-修改银行卡信息-CRM场景模版code
     */
    public final static String ACCOUNT_CHANGE_CRM_SCENE_TEMPLATE_CODE = "OIC7TVNTVE5B";

    /**
     * 营业执照变更-修改银行卡信息-APP场景模版code
     */
    public final static String ACCOUNT_CHANGE_APP_SCENE_TEMPLATE_CODE = "SNV7MGAFB2SR";

    public final static String MERCHANT_BANK_ACCOUNT_TABLE = "merchant_bank_account#";


    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private BankAccountService bankAccountService;

    @Resource
    private AccountBankAccountService accountBankAccountService;

    @Autowired
    private MerchantService merchantService;


    /**
     * 更新默认账户的验证状态
     *
     * @param merchantId   商户id
     * @param verifyStatus 验证状态
     */
    public void updateDefaultAccountVerifyStatus(String merchantId, Integer verifyStatus, String userId, String userName, String platform) {
        Map existed = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        if (MapUtils.isEmpty(existed)) {
            log.warn("updateDefaultAccountVerifyStatus bankAccount is empty, merchantId:{}", merchantId);
            return;
        }
        Map<String, Object> bankAccountMap = Maps.newHashMap();
        bankAccountMap.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        bankAccountMap.put(MerchantBankAccount.VERIFY_STATUS, verifyStatus);
        bankAccountMap.put(MerchantBankAccount.NUMBER, MapUtils.getString(existed, MerchantBankAccount.NUMBER));
        // 保持预存表的状态一致
        Map existedAccountPre = merchantBankService.getMerchantBankAccountPreByMerchantIdAndNumber(merchantId, MapUtils.getString(existed, MerchantBankAccount.NUMBER));
        if (MapUtils.isEmpty(existedAccountPre)) {
            return;
        }
        Map<String, Object> updateBankAccountPreMap = Maps.newHashMap();
        updateBankAccountPreMap.put(DaoConstants.ID, MapUtils.getString(existedAccountPre, DaoConstants.ID));
        updateBankAccountPreMap.put(MerchantBankAccountPre.VERIFY_STATUS, verifyStatus);
        updateBankAccountPreMap.put(MerchantBankAccountPre.MERCHANT_ID, merchantId);
        merchantBankService.updateMerchantBankAccountPre(updateBankAccountPreMap);
        recordDefaultAccountVerifyStatus(merchantId, userId, userName, platform, bankAccountMap);

    }

    private void recordDefaultAccountVerifyStatus(String merchantId, String userId, String userName, String platform, Map<String, Object> bankAccountMap) {
        try {
            LogParamsDto logParamsDto = new LogParamsDto();
            logParamsDto.setLogPlatformEnum(getLogPlatformEnum(platform));
            logParamsDto.setUserId(userId);
            logParamsDto.setUserName(userName);
            logParamsDto.setRemark("营业执照变更-更新默认卡验证状态");
            logParamsDto.setSceneTemplateCode(MERCHANT_BANK_ACCOUNT_CHANGE_TEMPLATE_CODE);
            bankAccountService.updateMerchantBankAccountInfoWithLog(bankAccountMap, logParamsDto);
        } catch (Exception e) {
            log.error("recordDefaultAccountVerifyStatus 记录变更默认卡验证状态失败, merchantId:{}", merchantId, e);
        }

    }


    /**
     * 更新预存表校验状态
     *
     * @param merchantId   商户id
     * @param number       银行账户
     * @param verifyStatus 验证状态
     */
    public void updateAccountPreVerifyStatus(String merchantId, String number, Integer verifyStatus) {
        Map existedAccount = merchantBankService.getMerchantBankAccountPreByMerchantIdAndNumber(merchantId, number);
        if (MapUtils.isEmpty(existedAccount)) {
            log.warn("updateAccountPreVerifyStatus bankAccount is empty, merchantId:{}, number:{}", merchantId, number);
            return;        }
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put(DaoConstants.ID,  MapUtils.getString(existedAccount, DaoConstants.ID));
        updateMap.put(MerchantBankAccountPre.MERCHANT_ID,  MapUtils.getString(existedAccount, MerchantBankAccountPre.MERCHANT_ID));
        updateMap.put(MerchantBankAccountPre.VERIFY_STATUS, verifyStatus);
        merchantBankService.updateMerchantBankAccountPre(updateMap);
    }

    public void updateAccountPreVerifyAndDefaultStatus(String merchantId, String number, Integer verifyStatus, Integer defaultStatus) {
        Map existedAccount = merchantBankService.getMerchantBankAccountPreByMerchantIdAndNumber(merchantId, number);
        if (MapUtils.isEmpty(existedAccount)) {
            log.warn("预存表银行卡不存在, number:{}", number);
            return;
        }
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put(DaoConstants.ID,  MapUtils.getString(existedAccount, DaoConstants.ID));
        updateMap.put(MerchantBankAccountPre.MERCHANT_ID,  MapUtils.getString(existedAccount, MerchantBankAccountPre.MERCHANT_ID));
        updateMap.put(MerchantBankAccountPre.VERIFY_STATUS, verifyStatus);
        updateMap.put(MerchantBankAccountPre.DEFAULT_STATUS, defaultStatus);
        merchantBankService.updateMerchantBankAccountPre(updateMap);
    }

    /**
     * 确保关键字段存在于银行账户Map中
     * 即使这些字段的值为null，也会被显式包含在Map中
     *
     * @param bankAccount 银行账户Map
     */
    private void ensureRequiredFieldsExist(Map<String, Object> bankAccount) {
        String[] requiredFields = {
            MerchantBankAccountPre.HOLDER_ID_CARD_ADDRESS,
            MerchantBankAccountPre.HOLDER_ID_CARD_ISSUING_AUTHORITY,
            MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO,
            MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO
        };
        for (String field : requiredFields) {
            if (!bankAccount.containsKey(field)) {
                bankAccount.put(field, null);
            }
        }
    }


    /**
     * 保存或者更新账户预存表
     *
     * @param merchantId   商户id
     * @param bankAccount  银行账户
     * @param verifyStatus 验证状态
     */
    public void saveOrUpdateAccountPre(String merchantId, Map<String, Object> bankAccount, Integer verifyStatus, Integer defaultStatus) {
        if (MapUtils.isEmpty(bankAccount) || StringUtils.isBlank(MapUtils.getString(bankAccount, MerchantBankAccount.NUMBER))) {
            throw new ContractBizException("银行卡号为空");
        }
        ensureRequiredFieldsExist(bankAccount);
        Set<String> allowedFields = new HashSet<>(MerchantBankAccountPre.merchantBankAccountPreParams);
        bankAccount.keySet().removeIf(key -> !allowedFields.contains(key));
        Map existedAccount = merchantBankService.getMerchantBankAccountPreByMerchantIdAndNumber(merchantId, MapUtils.getString(bankAccount, MerchantBankAccount.NUMBER));
        if (Objects.nonNull(defaultStatus)) {
            bankAccount.put(MerchantBankAccountPre.DEFAULT_STATUS, defaultStatus);
        }
        bankAccount.put(MerchantBankAccountPre.VERIFY_STATUS, verifyStatus);
        if (MapUtils.isEmpty(existedAccount)) {
            bankAccount.put(MerchantBankAccountPre.MERCHANT_ID, merchantId);
            merchantBankService.saveMerchantBankAccountPre(bankAccount);
        } else {
            bankAccount.put(DaoConstants.ID, MapUtils.getString(existedAccount, DaoConstants.ID));
            merchantBankService.updateMerchantBankAccountPre(bankAccount);
        }
    }

    /**
     * 更新默认账户
     *
     * @param merchantId   商户id
     * @param bankAccount  银行账户
     * @param verifyStatus 验证状态
     */
    public void updateDefaultAccount(String merchantId, Map<String, Object> bankAccount,
                                     Integer verifyStatus, String userId, String userName, String platform) {
        if (MapUtils.isEmpty(bankAccount) || StringUtils.isBlank(MapUtils.getString(bankAccount, MerchantBankAccount.NUMBER))) {
            throw new ContractBizException("银行卡信息为空");
        }
        ensureRequiredFieldsExist(bankAccount);
        Map existed = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        if (MapUtils.isEmpty(existed)) {
            throw new ContractBizException("商户尚未录入银行卡信息");
        }
        Set<String> allowedFields = new HashSet<>(getAllBankAccountFields());
        bankAccount.keySet().removeIf(key -> !allowedFields.contains(key));
        bankAccount.remove(DaoConstants.VERSION);
        bankAccount.put(MerchantBankAccount.VERIFY_STATUS, verifyStatus);
        bankAccount.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        bankAccount.put(DaoConstants.ID, MapUtils.getString(existed, DaoConstants.ID));
        recordUpdateDefaultAccountLog(merchantId, bankAccount, userId, userName, platform);
    }

    private void recordUpdateDefaultAccountLog(String merchantId, Map<String, Object> bankAccount, String userId, String userName, String platform) {
        try {
            LogParamsDto logParamsDto = new LogParamsDto();
            logParamsDto.setLogPlatformEnum(getLogPlatformEnum(platform));
            logParamsDto.setUserId(userId);
            logParamsDto.setUserName(userName);
            logParamsDto.setRemark("营业执照变更-更新默认卡信息");
            logParamsDto.setSceneTemplateCode(MERCHANT_BANK_ACCOUNT_CHANGE_TEMPLATE_CODE);
            accountBankAccountService.updateMerchantBankAccountInfoWithLog(bankAccount, logParamsDto, false);
        } catch (Exception e) {
            log.error("recordUpdateDefaultAccountLog 记录更新默认卡信息日志失败, merchantId:{}", merchantId, e);
        }

    }

    private LogPlatformEnum getLogPlatformEnum(String crmPlatform) {
        if (StringUtils.containsIgnoreCase(crmPlatform, PlatformEnum.CRM.getValue())) {
            return LogPlatformEnum.CRM_APP;
        }
        if (StringUtils.containsIgnoreCase(crmPlatform, PlatformEnum.SPA.getValue())) {
            return LogPlatformEnum.SPA;
        }
        if (StringUtils.containsIgnoreCase(crmPlatform, PlatformEnum.MSP.getValue())) {
            return LogPlatformEnum.MSP;
        }
        return LogPlatformEnum.APP;
    }


    /**
     * 证件地址 证件签发机关 证件正面照 证件反面照 证件有效期
     */
    public void syncBankAccountCertificateUnnecessaryInfo(String merchantId,
                                           BankAccountCertificateBO bankAccountCertificateBO, String platform) {
        Map existedDefaultCard = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        if (MapUtils.isEmpty(existedDefaultCard)) {
            throw new ContractBizException("商户尚未录入银行卡信息");
        }
        if (!Objects.equals(MapUtils.getInteger(existedDefaultCard, MerchantBankAccount.TYPE), BankAccountTypeEnum.PERSONAL.getValue())) {
            return;
        }
        if (!Objects.equals(bankAccountCertificateBO.getCertificateType(), MapUtils.getInteger(existedDefaultCard, MerchantBankAccount.ID_TYPE))
                || !StringUtils.equals(MapUtils.getString(existedDefaultCard, MerchantBankAccount.IDENTITY), bankAccountCertificateBO.getCertificateNum())) {
            return;
        }
        Map<String, Object> bankAccount = Maps.newHashMap();
        bankAccount.put(MerchantBankAccount.HOLDER_ID_CARD_ADDRESS, bankAccountCertificateBO.getCertificateAddress());
        bankAccount.put(MerchantBankAccount.HOLDER_ID_CARD_ISSUING_AUTHORITY, bankAccountCertificateBO.getCertificateIssuingAuthority());
        bankAccount.put(MerchantBankAccount.ID_VALIDITY, bankAccountCertificateBO.getCertificateValidity());
        bankAccount.put(MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, bankAccountCertificateBO.getCertificateFrontPhoto());
        bankAccount.put(MerchantBankAccount.HOLDER_ID_BACK_PHOTO, bankAccountCertificateBO.getCertificateBackPhoto());
        bankAccount.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        bankAccount.put(DaoConstants.ID, MapUtils.getString(existedDefaultCard, DaoConstants.ID));
        recordUpdateDefaultAccountLog(merchantId, bankAccount, "system", "system", platform);
        syncBankAccountPreCertificateUnnecessaryInfo(merchantId, bankAccountCertificateBO);
    }

    private void syncBankAccountPreCertificateUnnecessaryInfo(String merchantId, BankAccountCertificateBO bankAccountCertificateBO) {
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId, DaoConstants.DELETED, DeleteStatusEnum.NO_DELETED.getValue()));
        if (Objects.nonNull(listResult) && CollectionUtils.isNotEmpty(listResult.getRecords())) {
            List<Map> records = listResult.getRecords();
            for (Map record : records) {
                // 对私，且身份证类型和号码一致，才会更新身份证件信息
                if (Objects.equals(MapUtils.getInteger(record, MerchantBankAccountPre.TYPE), BankAccountTypeEnum.PERSONAL.getValue())
                        && Objects.equals(MapUtils.getInteger(record, MerchantBankAccountPre.ID_TYPE), bankAccountCertificateBO.getCertificateType())
                        && StringUtils.equals(MapUtils.getString(record, MerchantBankAccountPre.IDENTITY), bankAccountCertificateBO.getCertificateNum())) {
                    Map<String, Object> updateMap = Maps.newHashMap();
                    updateMap.put(MerchantBankAccountPre.HOLDER_ID_CARD_ADDRESS, bankAccountCertificateBO.getCertificateAddress());
                    updateMap.put(MerchantBankAccountPre.HOLDER_ID_CARD_ISSUING_AUTHORITY, bankAccountCertificateBO.getCertificateIssuingAuthority());
                    updateMap.put(MerchantBankAccountPre.ID_VALIDITY, bankAccountCertificateBO.getCertificateValidity());
                    updateMap.put(MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO, bankAccountCertificateBO.getCertificateFrontPhoto());
                    updateMap.put(MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO, bankAccountCertificateBO.getCertificateBackPhoto());
                    updateMap.put(MerchantBankAccount.MERCHANT_ID, merchantId);
                    updateMap.put(DaoConstants.ID, MapUtils.getString(record, DaoConstants.ID));
                    try {
                        merchantBankService.updateMerchantBankAccountPre(updateMap);
                    } catch (Exception e) {
                        log.error("更新预存表同名身份证件信息错误, merchantId:{}, updateCard:{}", merchantId, JSON.toJSONString(updateMap), e);
                    }
                }
            }
        }
    }


    public static List<String> getAllBankAccountFields() {
        return new ArrayList<>(Arrays.asList(
                MerchantBankAccount.MERCHANT_ID,
                MerchantBankAccount.TYPE,
                MerchantBankAccount.HOLDER,
                MerchantBankAccount.ID_TYPE,
                MerchantBankAccount.IDENTITY,
                MerchantBankAccount.TAX_PAYER_ID,
                MerchantBankAccount.NUMBER,
                MerchantBankAccount.VERIFY_STATUS,
                MerchantBankAccount.BANK_NAME,
                MerchantBankAccount.BRANCH_NAME,
                MerchantBankAccount.CARD_VALIDITY,
                MerchantBankAccount.CLEARING_NUMBER,
                MerchantBankAccount.OPENING_NUMBER,
                MerchantBankAccount.CITY,
                MerchantBankAccount.CELLPHONE,
                MerchantBankAccount.HOLDER_ID_CARD_ADDRESS,
                MerchantBankAccount.HOLDER_ID_CARD_ISSUING_AUTHORITY,
                MerchantBankAccount.EXTRA,
                MerchantBankAccount.CHANGE_TIME,
                MerchantBankAccount.HOLDER_ID_FRONT_PHOTO,
                MerchantBankAccount.HOLDER_ID_BACK_PHOTO,
                MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS,
                MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS,
                MerchantBankAccount.HOLDER_ID_STATUS,
                MerchantBankAccount.BANK_CARD_IMAGE,
                MerchantBankAccount.TRANSFER_VOUCHER,
                MerchantBankAccount.ID_VALIDITY,
                MerchantBankAccount.LETTER_OF_AUTHORIZATION,
                MerchantBankAccount.HAND_LETTER_OF_AUTHORIZATION,
                MerchantBankAccount.RELATION_OR_LEGAL_REP_HELD_AUTH,
                MerchantBankAccount.BANK_CARD_STATUS,
                MerchantBankAccount.EXTEND
        ));
    }

    public Integer getDefaultAccountVerifyStatus(String merchantId) {
        Map existed = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        return MapUtils.getInteger(existed, MerchantBankAccount.VERIFY_STATUS);
    }

    public Integer getAccountPreVerifyStatus(String merchantId, String number) {
        Map existedAccount = merchantBankService.getMerchantBankAccountPreByMerchantIdAndNumber(merchantId, number);
        return MapUtils.getInteger(existedAccount, MerchantBankAccountPre.VERIFY_STATUS);
    }

    @Resource
    private BankDirectService bankDirectService;

    @Resource
    private BankLicenseUpdateChangeCardService bankLicenseUpdateChangeCardService;

    /**
     * 进件成功后，更新银行卡主表和辅表
     * 同时还要删除异名卡，关闭数币通道
     * 发消息
     *
     * @param mainTaskContextBOInner 主任务上下文
     * @param merchantId             商户id
     * @param auditApplyDTO          提交审核的参数
     */
    public void updateAccountWhenContractSuccess(BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner,
                                                 String merchantId,
                                                 BusinessLicenseAuditApplyDTO auditApplyDTO) {
        String newDefaultAccountNum = MapUtils.getString(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.NUMBER);
        updateDefaultAccount(merchantId, mainTaskContextBOInner.getBankAccount(),
                MerchantBankAccount.VERIFY_STATUS_SUCC, auditApplyDTO.getSubmitUserId(), auditApplyDTO.getSubmitUserName(), auditApplyDTO.getPlatform());
        saveOrUpdateAccountPre(merchantId, mainTaskContextBOInner.getBankAccount(),
                MerchantBankAccountPre.VERIFY_STATUS_SUCC, DefaultStatusEnum.DEFAULT.getValue());
        if (!StringUtils.equals(mainTaskContextBOInner.getOriginalDefaultAccountNumber(), newDefaultAccountNum)) {
            updateAccountPreVerifyAndDefaultStatus(merchantId,
                    mainTaskContextBOInner.getOriginalDefaultAccountNumber(),
                    MerchantBankAccountPre.VERIFY_STATUS_SUCC, DefaultStatusEnum.NOT_DEFAULT.getValue());
        }
        try {
            changeCardPostProcess(mainTaskContextBOInner, merchantId, auditApplyDTO);
        } catch (Exception e) {
            log.error("营业执照变更-换卡后处理失败, merchantId:{}, auditApplyDTO:{}", merchantId, JSON.toJSONString(auditApplyDTO), e);
        }
    }

    private void changeCardPostProcess(BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner, String merchantId, BusinessLicenseAuditApplyDTO auditApplyDTO) {
        // 删除异名卡
        String newHolder = MapUtils.getString(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.HOLDER);
        String oldHolder = mainTaskContextBOInner.getOriginalDefaultAccountHolder();
        Integer newType = MapUtils.getInteger(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.TYPE);
        Integer oldType = mainTaskContextBOInner.getOriginalDefaultAccountType();
        String newIdentity = MapUtils.getString(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.IDENTITY);
        String oldIdentity = mainTaskContextBOInner.getOriginalDefaultAccountIdentity();
        boolean differentHolderChangeCard = isDifferentHolderChangeCard(oldHolder, newHolder, oldType, newType, oldIdentity, newIdentity);
        if (differentHolderChangeCard) {
            deleteDifferentHolderAccount(merchantId, mainTaskContextBOInner);
            cancelDecp(merchantId, oldIdentity);
        }
        if (isThreeElementChange(mainTaskContextBOInner)) {
            Map<String, Object> sourceMap = Maps.newHashMap();
            sourceMap.put(Request.KEY_PLATFORM, auditApplyDTO.getPlatform());
            sourceMap.put(Request.KEY_OPERATOR, auditApplyDTO.getSubmitUserName());
            noticeChangeCardSuccess(merchantId, differentHolderChangeCard
                    ? BankLicenseUpdateChangeCardService.DIFF_NAME
                    : BankLicenseUpdateChangeCardService.SAME_NAME, mainTaskContextBOInner.getBankAccount(), Objects.equals(newType, oldType), sourceMap);
        }
    }

    /**
     * 三要素变更
     */
    public boolean isThreeElementChange(BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner) {
        String newHolder = MapUtils.getString(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.HOLDER);
        String oldHolder = mainTaskContextBOInner.getOriginalDefaultAccountHolder();
        Integer newType = MapUtils.getInteger(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.TYPE);
        Integer oldType = mainTaskContextBOInner.getOriginalDefaultAccountType();
        String newIdentity = MapUtils.getString(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.IDENTITY);
        String oldIdentity = mainTaskContextBOInner.getOriginalDefaultAccountIdentity();
        String newDefaultAccountNum = MapUtils.getString(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.NUMBER);
        String oldDefaultAccountNum = mainTaskContextBOInner.getOriginalDefaultAccountNumber();
        if (!Objects.equals(newType, oldType)) {
            return true;
        }
        if (Objects.equals(newType, BankAccountTypeEnum.PUBLIC.getValue())) {
            if (!Objects.equals(newHolder, oldHolder) || !Objects.equals(newDefaultAccountNum, oldDefaultAccountNum)) {
                return true;
            }
        }
        if (Objects.equals(newType, BankAccountTypeEnum.PERSONAL.getValue())) {
            return !Objects.equals(newHolder, oldHolder)
                    || !Objects.equals(newDefaultAccountNum, oldDefaultAccountNum)
                    || !Objects.equals(newIdentity, oldIdentity);
        }
        return false;
    }

    private void noticeChangeCardSuccess(String merchantId, Integer changeType, Map<String, Object> newBank, boolean sameAccountType, Map<String, Object> source) {
        ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> {
            try {
                bankLicenseUpdateChangeCardService.notifyChangeCardSuccess(merchantId, changeType, newBank, sameAccountType, source);
            } catch (Exception e) {
                log.error("营业执照变更-换卡成功通知失败, merchantId:{}, changeType:{}", merchantId, changeType, e);
            }
        });
    }

    private void cancelDecp(String merchantId, String oldIdentity) {
        try {
            bankDirectService.cancelCcbDecp(CollectionUtil.hashMap("merchant_id", merchantId, "identity", oldIdentity));
        } catch (Exception e) {
            log.error("cancelDecp 取消数币通道失败, merchantId:{}, oldIdentity:{}", merchantId, oldIdentity, e);
        }
    }

    private boolean isDifferentHolderChangeCard( String oldHolder,String newHolder,
                                                Integer originalType, Integer newType,
                                                String originalIdentity, String newIdentity ) {
        if (!Objects.equals(newType, originalType)) {
            return true;
        }
        // 对私看身份证
        if (Objects.equals(newType, BankAccountTypeEnum.PERSONAL.getValue())) {
            return !StringUtils.equals(newIdentity, originalIdentity);
        }
        // 对公看holder
        return !StringUtils.equals(newHolder, oldHolder);
    }

    public void deleteDifferentHolderAccount(String merchantId, BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner) {
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId));
        String newHolder = MapUtils.getString(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.HOLDER);
        Integer newType = MapUtils.getInteger(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.TYPE);
        String newIdentity = MapUtils.getString(mainTaskContextBOInner.getBankAccount(), MerchantBankAccount.IDENTITY);
        if (Objects.nonNull(listResult) && CollectionUtils.isNotEmpty(listResult.getRecords())) {
            List<Map> records = listResult.getRecords();
            for (Map record : records) {
                Integer recordType = MapUtils.getInteger(record, MerchantBankAccountPre.TYPE);
                String recordHolder = MapUtils.getString(record, MerchantBankAccountPre.HOLDER);
                String recordIdentity = MapUtils.getString(record, MerchantBankAccountPre.IDENTITY);
                if (isDifferentHolderChangeCard(recordHolder, newHolder, recordType, newType, recordIdentity, newIdentity)) {
                    Map bankAccount = new HashMap();
                    bankAccount.put(DaoConstants.ID, MapUtils.getString(record, DaoConstants.ID));
                    bankAccount.put(MerchantBankAccountPre.MERCHANT_ID, merchantId);
                    bankAccount.put(DaoConstants.DELETED, DeleteStatusEnum.DELETED.getValue());
                    try {
                        merchantBankService.updateMerchantBankAccountPre(bankAccount);
                        log.info("营业执照更新换卡，删除异名卡, merchantId:{}, bankAccountId:{}", merchantId, MapUtils.getString(record, DaoConstants.ID));
                    } catch (Exception e) {
                        log.error("deleteDifferentHolderAccount 删除异名卡失败, merchantId:{}, bankAccountId:{}", merchantId, MapUtils.getString(record, DaoConstants.ID), e);
                    }
                }
            }
        }
    }

    public void updateAccountWhenContractFail(BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner, String merchantId, BusinessLicenseAuditApplyDTO auditApplyDTO) {
        Integer originalStatus = mainTaskContextBOInner.getOriginalDefaultAccountVerifyStatus();
        Integer preOriginalStatus = mainTaskContextBOInner.getOriginalDefaultPreAccountVerifyStatus();
        updateDefaultAccountVerifyStatus(merchantId, originalStatus, auditApplyDTO.getSubmitUserId(), auditApplyDTO.getSubmitUserName(), auditApplyDTO.getPlatform());
        updateAccountPreVerifyStatus(merchantId, mainTaskContextBOInner.getOriginalDefaultAccountNumber(),preOriginalStatus);
    }

}
