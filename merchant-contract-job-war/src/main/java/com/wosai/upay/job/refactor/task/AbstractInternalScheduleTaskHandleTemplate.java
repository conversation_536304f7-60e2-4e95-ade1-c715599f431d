package com.wosai.upay.job.refactor.task;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.contract.ScheduleStatusEnum;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.shouqianba.cua.utils.thread.CollectionWorker;
import com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO;
import com.wosai.upay.job.refactor.dao.InternalScheduleSubTaskDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.dto.InternalScheduleTaskResultRspDTO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.*;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.service.impl.InternalKeyValueServiceImpl;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抽象的内部任务调度处理模板
 *
 * <AUTHOR>
 * @date 2024/6/14 15:15
 */
@Slf4j
public abstract class AbstractInternalScheduleTaskHandleTemplate {

    @Resource
    protected InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Resource
    protected InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    @Resource
    protected InternalKeyValueServiceImpl internalKeyValueService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 获取任务类型
     *
     * @return 任务类型
     */
    public abstract InternalScheduleTaskTypeEnum getTaskType();

    /**
     * 获取任务执行属性taskExecuteProperty
     */
    protected abstract ScheduleTaskExecutePropertyBO getTaskExecuteProperty();

    /**
     * 处理单个子任务逻辑
     * 子类需要完善不同状态的处理逻辑，如：0-待处理 2-等待外部结果 3-重试
     * 状态：{@link InternalScheduleSubTaskStatusEnum}
     * 子类需要根据业务场景，决定是否需要重试
     * 同时该方法执行出现异常也会默认重试 配置：{@link ScheduleTaskExecutePropertyBO} retryIfException
     * 返回 status = InternalScheduleSubTaskStatusEnum.RETRY 会尝试重试
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     * @return 子任务执行结果
     */
    protected abstract InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO);


    /**
     * 获取任务便宜量记录的内部key，internal_key_value表的key
     * 默认: merchant-contract_job:internal_schedule_task: + GetScheduleTaskPatternTypeEnum.getValue()
     *
     * @return 任务偏移量记录的内部key
     */
    protected String getTaskOffsetRecordInternalKey() {
        return "merchant-contract_job:internal_schedule_task:" + getTaskType().getValue();
    }

    /**
     * 按照顺序批量处理任务
     *
     * @param taskNum 需要处理的每个任务状态对应的主任务数量（并不是所有的任务数量）
     * @return 处理结果
     */
    public InternalScheduleTaskResultRspDTO batchHandleTasksInSequence(Integer taskNum) {
        ScheduleTaskExecutePropertyBO taskExecuteProperty = getTaskExecuteProperty();
        List<InternalScheduleMainTaskDO> mainTaskDOList = listNeedToHandleMainTasks(taskNum);
        if (CollectionUtils.isEmpty(mainTaskDOList)) {
            return getEmptyTaskListRsp();
        }
        if (taskExecuteProperty.isSupportAsyncExecute()) {
            ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> doHandleTasks(mainTaskDOList, true));
            return new InternalScheduleTaskResultRspDTO("任务:" + getTaskType().getValue() + " 调度成功");
        }
        return doHandleTasks(mainTaskDOList, true);
    }

    /**
     * 根据主任务id列表处理任务
     *
     * @param mainTaskIds 主任务id列表
     * @return 处理结果
     */
    public InternalScheduleTaskResultRspDTO batchHandleTasksByMainTaskIds(List<Long> mainTaskIds) {
        List<InternalScheduleMainTaskDO> mainTaskDOList = internalScheduleMainTaskDAO.listByIds(mainTaskIds);
        if (CollectionUtils.isEmpty(mainTaskDOList)) {
            return getEmptyTaskListRsp();
        }
        return doHandleTasks(mainTaskDOList, false);
    }

    /**
     * 批量处理主任务
     *
     * @param mainTaskDOList 主任务列表
     * @return 处理结果
     */
    public InternalScheduleTaskResultRspDTO batchHandleMainTasks(List<InternalScheduleMainTaskDO> mainTaskDOList) {
        if (CollectionUtils.isEmpty(mainTaskDOList)) {
            return getEmptyTaskListRsp();
        }
        return doHandleTasks(mainTaskDOList, false);
    }

    private @NotNull InternalScheduleTaskResultRspDTO getEmptyTaskListRsp() {
        return new InternalScheduleTaskResultRspDTO(0, 0, "任务:"
                + getTaskType().getText() + " 没有需要处理的任务");
    }

    private InternalScheduleTaskResultRspDTO doHandleTasks(List<InternalScheduleMainTaskDO> mainTaskDOList, boolean recordConsumerOffset) {
        Map<Long/*mainId*/, List<InternalScheduleSubTaskDO>> mainTaskIdToSortedSubTaskMap = getMainTaskIdToSortedSubTaskMap(mainTaskDOList.stream().map(InternalScheduleMainTaskDO::getId).collect(Collectors.toList()));
        ScheduleTaskExecutePropertyBO taskExecuteProperty = getTaskExecuteProperty();
        boolean supportParallel = taskExecuteProperty.isSupportParallel();
        if (supportParallel) {
            CollectionWorker.of(mainTaskDOList).parallel(taskExecuteProperty.getThreadPool())
                    .forEach(mainTaskDO -> scheduleSingleMainTaskWithLock(mainTaskDO, mainTaskIdToSortedSubTaskMap.get(mainTaskDO.getId())));
        } else {
            for (InternalScheduleMainTaskDO mainTaskDO : mainTaskDOList) {
                scheduleSingleMainTaskWithLock(mainTaskDO, mainTaskIdToSortedSubTaskMap.get(mainTaskDO.getId()));
            }
        }
        // recordOffsetIfNecessary(mainTaskDOList, recordConsumerOffset);
        return getResultRspDTOWhenFinished(mainTaskDOList);
    }

    private void recordOffsetIfNecessary(List<InternalScheduleMainTaskDO> mainTaskDOList, boolean recordConsumerOffset) {
        if (recordConsumerOffset) {
            recordTaskConsumerOffset(mainTaskDOList);
        }
    }


    private InternalScheduleTaskResultRspDTO getResultRspDTOWhenFinished(List<InternalScheduleMainTaskDO> mainTaskDOList) {
        Map<Integer, Long> statusToCountMap = mainTaskDOList.stream().collect(Collectors.groupingBy(InternalScheduleMainTaskDO::getStatus, Collectors.counting()));
        return new InternalScheduleTaskResultRspDTO(
                statusToCountMap.getOrDefault(InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue(), 0L).intValue(),
                statusToCountMap.getOrDefault(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue(), 0L).intValue(),
                "任务:" + getTaskType().getValue() + " 该批次处理成功");
    }

    private void recordTaskConsumerOffset(List<InternalScheduleMainTaskDO> mainTaskDOList) {
        // 存在这样的场景：任务无法调度，状态是待处理，或者子类处理结果仍然返回待处理
        // 对于没有待处理的任务，直接记录最大的id
        Optional<Long> minNotScheduleTaskId = mainTaskDOList.stream().filter(t -> Objects.equals(t.getStatus(), InternalScheduleMainTaskStatusEnum.WAIT_PROCESS.getValue())).map(InternalScheduleMainTaskDO::getId)
                .min(Long::compareTo);
        if (minNotScheduleTaskId.isPresent()) {
            internalKeyValueService.insertOrUpdateKeyValue(getTaskOffsetRecordInternalKey(), String.valueOf(minNotScheduleTaskId.get()));
            return;
        }
        Long maxTaskId = mainTaskDOList.stream().map(InternalScheduleMainTaskDO::getId).max(Long::compareTo).orElse(0L);
        internalKeyValueService.insertOrUpdateKeyValue(getTaskOffsetRecordInternalKey(), String.valueOf(maxTaskId));
    }

    // todo 这里进入锁，需要再查一下数据库
    private void scheduleSingleMainTaskWithLock(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
        RLock lock = null;
        try {
            lock = redissonClient.getLock("merchant-contract-job:internal_schedule_task:" + mainTaskDO.getId());
            if (lock.tryLock()) {
                processSingleMainTaskTemplate(mainTaskDO, sortedSubTasks);
            } else {
                log.warn("internalScheduleTask任务:{} 获取锁失败，任务正在执行中", mainTaskDO.getId());
            }
        } catch (Exception e) {
            log.error("internalScheduleTask任务调度执行异常, 主任务id: {}", mainTaskDO.getId(), e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void processSingleMainTaskTemplate(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
        mainTaskDO.setLastScheduledTime(new Timestamp(System.currentTimeMillis()));
        if (CollectionUtils.isEmpty(sortedSubTasks)
                || Objects.equals(mainTaskDO.getEnableScheduledStatus(), ScheduleStatusEnum.NOT_CAN.getValue())
                || mainTaskDO.taskCanScheduledTimeNotArrive()) {
            // 只更新调度时间和更新时间 防止任务数据被覆盖
            updateMainTaskScheduleTime(mainTaskDO.getId());
            return;
        }
        if (mainTaskDO.taskCanNotHandled()) {
            return;
        }
        mainTaskPreProcessor(mainTaskDO, sortedSubTasks);
        loopHandleSubTask(mainTaskDO, sortedSubTasks);
        calculateMainTaskStatus(mainTaskDO, sortedSubTasks);
        updateMainTaskAndSubTask(mainTaskDO, sortedSubTasks);
        mainTaskPostProcessor(mainTaskDO, sortedSubTasks);
    }

    private void updateMainTaskScheduleTime(Long id) {
        InternalScheduleMainTaskDO internalScheduleMainTaskDO = new InternalScheduleMainTaskDO();
        internalScheduleMainTaskDO.setId(id);
        internalScheduleMainTaskDO.setLastScheduledTime(new Timestamp(System.currentTimeMillis()));
        internalScheduleMainTaskDAO.updateByPrimaryKeySelective(internalScheduleMainTaskDO);
    }

    /**
     * 主任务执行完后的后置操作
     */
    protected void mainTaskPostProcessor(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {

    }

    /**
     * 主任务开始前的前置操作
     */
    protected void mainTaskPreProcessor(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {

    }

    private void updateMainTask(InternalScheduleMainTaskDO mainTaskDO) {
        internalScheduleMainTaskDAO.updateByPrimaryKeySelective(mainTaskDO);
    }


    private void updateMainTaskAndSubTask(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
        internalScheduleMainTaskDAO.batchUpdateTasks(Lists.newArrayList(mainTaskDO), sortedSubTasks);

    }

    private void loopHandleSubTask(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
        if (mainTaskDO.taskExpired()) {
            mainTaskDO.updateStatusFailed("任务已过期");
            sortedSubTasks.forEach(internalScheduleSubTaskDO -> {
                internalScheduleSubTaskDO.updateStatusFailed("任务已过期");
            });
            return;
        }
        Map<Long, InternalScheduleSubTaskDO> subTaskMap = sortedSubTasks.stream()
                .collect(Collectors.toMap(InternalScheduleSubTaskDO::getId, t -> t));
        for (InternalScheduleSubTaskDO subTaskDO : sortedSubTasks) {
            try {
                if (subTaskDO.isTaskCanNotBeHandle() || !Objects.equals(ScheduleStatusEnum.CAN.getValue(), subTaskDO.getEnableScheduledStatus())) {
                    continue;
                }
                if (Objects.nonNull(subTaskDO.getDependOnSubTaskId())) {
                    InternalScheduleSubTaskDO internalScheduleSubTaskDO = subTaskMap.get(subTaskDO.getDependOnSubTaskId());
                    if (Objects.isNull(internalScheduleSubTaskDO)) {
                        subTaskDO.updateStatusFailed("依赖的任务为空");
                        continue;
                    }
                    if (internalScheduleSubTaskDO.isProcessFail()) {
                        subTaskDO.updateStatusFailed("依赖的任务执行失败");
                        continue;
                    }
                    if (!internalScheduleSubTaskDO.isProcessSuccess() ) {
                        continue;
                    }
                }
                InternalScheduleSubTaskProcessResultBO processResultBO = handleSingleSubTask(mainTaskDO, subTaskDO);
                subTaskDO.setRequestMessage(processResultBO.getRequestMsg());
                subTaskDO.setResponseMessage(processResultBO.getResponseMsg());
                subTaskDO.setStatus(processResultBO.getStatus().getValue());
                subTaskDO.setResult(processResultBO.getResult());
                if (Objects.equals(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, processResultBO.getStatus())) {
                    // 依赖的任务应该归属于同一个主任务
                    sortedSubTasks.stream()
                            .filter(t -> Objects.equals(t.getDependOnSubTaskId(), subTaskDO.getId()))
                            .forEach(t -> t.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue()));
                } else if (Objects.equals(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, processResultBO.getStatus())) {
                    sortedSubTasks.stream()
                            .filter(t -> Objects.equals(t.getDependOnSubTaskId(), subTaskDO.getId()))
                            .forEach(internalScheduleSubTaskDO -> {
                                internalScheduleSubTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.NOT_CAN.getValue());
                                internalScheduleSubTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL.getValue());
                                internalScheduleSubTaskDO.setResult("依赖的任务执行失败");
                            });
                    if (Objects.equals(subTaskDO.getAffectMainTaskStatus(), AffectPrimaryTaskStatusEnum.YES.getValue())) {
                        mainTaskDO.setResult(processResultBO.getResult());
                        break;
                    }
                } else if (Objects.equals(InternalScheduleSubTaskStatusEnum.RETRY, processResultBO.getStatus())) {
                    retrySubTaskRspNeedToRetry(subTaskDO);
                }
            } catch (Exception e) {
                log.error("任务调度执行异常, 子任务id: {}", subTaskDO.getId(), e);
                retrySubTaskException(subTaskDO, e);
            }
        }
    }


    protected void calculateMainTaskStatus(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
        // 等待下次继续调度
        long affectMainTaskAlreadySuccessSubNum = sortedSubTasks.stream()
                .filter(t -> Objects.equals(t.getAffectMainTaskStatus(), AffectPrimaryTaskStatusEnum.YES.getValue())
                        && Objects.equals(t.getStatus(), InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS.getValue()))
                .count();
        mainTaskDO.setAlreadySuccessSubTaskNum((int) affectMainTaskAlreadySuccessSubNum);
        Set<Integer> subTaskStatusSet = sortedSubTasks.stream().map(InternalScheduleSubTaskDO::getStatus).collect(Collectors.toSet());
        HashSet<Integer> needToScheduleStatusSet = Sets.newHashSet(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT.getValue(), InternalScheduleSubTaskStatusEnum.RETRY.getValue(), InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue());
        if (CollectionUtils.containsAny(needToScheduleStatusSet, subTaskStatusSet)) {
            mainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.WAIT_NEXT_SCHEDULED.getValue());
            mainTaskDO.setResult(InternalScheduleMainTaskStatusEnum.WAIT_NEXT_SCHEDULED.getText());
            return;
        }
        // 所有影响主任务状态的子任务都成功
        Set<Integer> affectMainTaskSubTaskStatusSet = sortedSubTasks.stream()
                .filter(t -> Objects.equals(t.getAffectMainTaskStatus(), AffectPrimaryTaskStatusEnum.YES.getValue()))
                .map(InternalScheduleSubTaskDO::getStatus)
                .collect(Collectors.toSet());
        if (affectMainTaskSubTaskStatusSet.contains( InternalScheduleSubTaskStatusEnum.PROCESS_FAIL.getValue())) {
            mainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue());
            log.warn("任务调度失败，存在影响主任务状态的子任务调度失败，主任务id: {}", mainTaskDO.getId());
            if (StringUtils.isBlank(mainTaskDO.getResult())) {
                mainTaskDO.setResult(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getText());
            }
            return;
        }
        // 防止子任务都是不影响主任务状态的
        if ((affectMainTaskSubTaskStatusSet.size() == 1 && affectMainTaskSubTaskStatusSet.contains(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS.getValue()))
                || (subTaskStatusSet.size() == 1 && subTaskStatusSet.contains(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS.getValue()))) {
            mainTaskDO.setResult(InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getText());
            mainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue());
            return;
        }
        if (StringUtils.isBlank(mainTaskDO.getResult())) {
            mainTaskDO.setResult(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getText());
        }
        mainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue());
    }

    private void retrySubTaskRspNeedToRetry(InternalScheduleSubTaskDO subTaskDO) {
        if (Objects.equals(subTaskDO.getEnableRetryStatus(), EnableTypeEnum.ENABLED.getValue())) {
            doRetrySubTask(subTaskDO, subTaskDO.getResult());
            return;
        }
        subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.RETRY.getValue());
    }

    private void doRetrySubTask(InternalScheduleSubTaskDO subTaskDO, String failRetryMessage) {
        int alreadyRetryNum = Objects.isNull(subTaskDO.getAlreadyRetryNum()) ? 0 : subTaskDO.getAlreadyRetryNum();
        int maxRetryNum = Objects.isNull(subTaskDO.getMaxRetryNum()) ? 0 : subTaskDO.getMaxRetryNum();
        if (alreadyRetryNum < maxRetryNum) {
            subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.RETRY.getValue());
        } else {
            subTaskDO.setResult(failRetryMessage);
            subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL.getValue());
        }
    }

    private void retrySubTaskException(InternalScheduleSubTaskDO subTaskDO, Exception exception) {
        ScheduleTaskExecutePropertyBO taskExecuteProperty = getTaskExecuteProperty();
        if (taskExecuteProperty.isRetryIfException()) {
            if (Objects.equals(subTaskDO.getEnableRetryStatus(), EnableTypeEnum.ENABLED.getValue())) {
                doRetrySubTask(subTaskDO, exception.getMessage());
                return;
            }
        }
        subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL.getValue());
        subTaskDO.setResult(exception.getMessage());
    }


    private Map<Long/*mainId*/, List<InternalScheduleSubTaskDO>> getMainTaskIdToSortedSubTaskMap(List<Long> mainIds) {
        return internalScheduleSubTaskDAO.listByMainIds(mainIds).stream()
                .sorted(Comparator.comparingInt(o -> o.getPriority() != null ? o.getPriority() : Integer.MAX_VALUE))
                .collect(Collectors.groupingBy(InternalScheduleSubTaskDO::getMainTaskId));
    }

    private void updateMainTasksStatusToProcessing(List<Long> mainTaskIds) {
        internalScheduleMainTaskDAO.batchUpdateStatusByIds(mainTaskIds, InternalScheduleMainTaskStatusEnum.BEING_PROCESSING.getValue());
    }


    /**
     * 获取需要处理的主任务列表
     * 如果调度类型是OFFSET，则需要根据internal_key_value表记录偏移量获取需要处理的主任务列表
     * 如果调度类型是OFFSET_AND_TIMESTAMP，则需要根据时间戳获取需要处理的主任务列表
     *
     * @param taskNum 需要处理的主任务数量
     * @return 需要处理的主任务列表
     */
    protected List<InternalScheduleMainTaskDO> listNeedToHandleMainTasks(Integer taskNum) {
        BatchGetScheduleTaskPatternTypeEnum scheduleTaskType = getTaskExecuteProperty().getBatchGetPatternType();
        List<InternalScheduleMainTaskDO> tasks = Lists.newArrayList();
        List<InternalScheduleMainTaskDO> waitProcessMainTask = listWaitProcessTasksByOffset(taskNum);
        if (CollectionUtils.isNotEmpty(waitProcessMainTask)) {
            tasks.addAll(waitProcessMainTask);
        }
        if (Objects.equals(scheduleTaskType, BatchGetScheduleTaskPatternTypeEnum.PENDING_TASKS)) {
            return tasks;
        } else if (Objects.equals(scheduleTaskType, BatchGetScheduleTaskPatternTypeEnum.PENDING_AND_WAITING_EXT_TASKS)) {
            List<InternalScheduleMainTaskDO> waitExternalResultTasks = listWaitExternalResultMainTasksByTimestamp(taskNum);
            if (CollectionUtils.isNotEmpty(waitExternalResultTasks)) {
                tasks.addAll(waitExternalResultTasks);
            }
            return tasks;
        } else {
            throw new ContractBizException("不支持的调度任务类型");
        }
    }

    /**
     * 获取等待外部结果的主任务列表,status=2 type+status order by last_scheduled_time asc
     *
     * @param taskNum 需要处理的主任务数量
     * @return 需要处理的主任务列表
     */
    private List<InternalScheduleMainTaskDO> listWaitExternalResultMainTasksByTimestamp(Integer taskNum) {
        return internalScheduleMainTaskDAO.listByTypeAndStatusOrderByLastScheduledTimeWithLimit(
                getTaskType().getValue(), InternalScheduleMainTaskStatusEnum.WAIT_NEXT_SCHEDULED.getValue(), taskNum);
    }

    /**
     * 获取需要处理的主任务列表,status=0 type+status order by last_scheduled_time asc
     *
     * @param taskNum 需要处理的主任务数量
     * @return 需要处理的主任务列表
     */
    private List<InternalScheduleMainTaskDO> listWaitProcessTasksByOffset(Integer taskNum) {
        return internalScheduleMainTaskDAO.listByTypeAndStatusOrderByLastScheduledTimeWithLimit(
                getTaskType().getValue(),
                InternalScheduleMainTaskStatusEnum.WAIT_PROCESS.getValue(),
                taskNum);
    }


}
