package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 内部key value表实体对象
 *
 * <AUTHOR>
 */
@TableName("internal_key_value")
@Data
public class InternalKeyValueDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * key, 命名 服务名：key名
     */
    @TableField(value = "internal_key")
    private String internalKey;
    /**
     * value
     */
    @TableField(value = "internal_value")
    private String internalValue;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Timestamp updateTime;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Timestamp createTime;


}

