package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.model.provider.UnionOpenUnionPayParam;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import com.wosai.upay.merchant.contract.service.UnionOpenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: jerry
 * @date: 2019/4/26 15:56
 * @Description:银联开放平台
 */
@Component(ProviderUtil.UNION_OPEN_CHANNEL)
public class OpenUnionProvider extends AbstractProvider {

    @Autowired
    private UnionOpenService unionOpenService;

    @Autowired
    LklV3Service lklV3Service;

    @Autowired
    ProviderTradeParamsService providerTradeParamsService;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        return null;
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (PaywayEnum.UNIONPAY.getValue().equals(payWay)) {
            UnionOpenUnionPayParam unionOpenUnionPayParam = buildParam(contractChannel, sub, UnionOpenUnionPayParam.class);
            return unionOpenService.contractMerchantWithParams(contextParam, unionOpenUnionPayParam);
        }
        return null;
    }

    /**
     * not support
     *
     * @param:
     * @return:
     * @date: 16:28
     */
    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        return null;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        throw new ContractBizException(getProviderBeanName() + "暂不支持配置微信appid");
    }

    @Override
    public ContractResponse queryTermContractResult(String merCupNo, String termNo) {
        LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        return lklV3Service.queryTermContractResultV2(merCupNo, termNo, lklV3Param);
    }

}
