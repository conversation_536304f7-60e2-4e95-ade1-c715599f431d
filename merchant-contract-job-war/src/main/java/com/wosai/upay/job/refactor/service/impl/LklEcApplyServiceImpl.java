package com.wosai.upay.job.refactor.service.impl;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;

import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.TodoDetailBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.dto.EcApplyPushDTO;
import com.wosai.upay.job.model.dto.acquirePos.LklEcApplyDTO;
import com.wosai.upay.job.model.lklV3Pos.LKlEcApplyStatusResp;
import com.wosai.upay.job.refactor.dao.LklEcApplyDAO;
import com.wosai.upay.job.refactor.model.entity.LklEcApplyDO;
import com.wosai.upay.job.refactor.model.enums.LklEcApplyStatusRespEnum;
import com.wosai.upay.job.service.LklEcApplyService;
import com.wosai.upay.job.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.wosai.upay.job.refactor.model.enums.LklEcApplyStatusRespEnum.AUDITING;
import static com.wosai.upay.job.refactor.model.enums.LklEcApplyStatusRespEnum.AUDIT_PASSED;


/**
 * 签约申请记录表Service层 {@link LklEcApplyDO}
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class LklEcApplyServiceImpl implements LklEcApplyService {

    @Resource
    private LklEcApplyDAO lklEcApplyDAO;

    @Resource
    private DirectStatusBiz directStatusBiz;

    @Value("${lklForeignCard.devCode}")
    private String lklForeignCardDevCode;

    @Value("${lkl_pso_dev_code}")
    public String lklPsoDevCode;

    @Resource
    private TodoDetailBiz todoDetailBiz;

    public static DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public LKlEcApplyStatusResp getLklEcApplyStatus(String merchantSn, String devCode) throws CommonPubBizException{
        final LKlEcApplyStatusResp resp = new LKlEcApplyStatusResp();
        resp.setStatus(String.valueOf(LklEcApplyStatusRespEnum.NOT_EXISTS.getValue()));
        //先判断不需要走三四要素验证就开通成功了
        final DirectStatus directStatus = Optional.ofNullable(directStatusBiz.getDirectStatusByMerchantSnAndDevCode(merchantSn, devCode))
                .orElse(new DirectStatus());
        if(Objects.equals(directStatus.getStatus(),DirectStatus.STATUS_SUCCESS)) {
            throw new CommonPubBizException("当前业务已开通成功");
        }
        final Optional<LklEcApplyDO> apply = lklEcApplyDAO.getApply(merchantSn, devCode);
        //不存在
        if(!apply.isPresent()) {
            return resp;
        }
        final LklEcApplyDO ecApplyDO = apply.get();
        //签约链接会过期,所以当发现detailStatus=2 审核通过的时候,需要判断链接有没有过期
        //链接过期时间
        final Map<String, Object> extraMap = ecApplyDO.getExtraMap();
        final String signH5UrlExpTm = BeanUtil.getPropString(extraMap, LklEcApplyDO.ExtraMapConst.SIGN_H5_URL_EXP_TM);
        if(Objects.equals(ecApplyDO.getDetailStatus(), AUDIT_PASSED.getValue()) && isTimeInPast(signH5UrlExpTm)) {
            //修改本地记录
            updateEcApplyStatusByPrimaryId(ecApplyDO.getId(),LklEcApplyDO.Status.FAIL,LklEcApplyStatusRespEnum.SIGN_ADDRESS_EXPIRED.getValue(),LklEcApplyStatusRespEnum.SIGN_ADDRESS_EXPIRED.getText());
            //取消BD的待办
            final String toDOid = BeanUtil.getPropString(extraMap, LklEcApplyDO.ExtraMapConst.TO_DO_ID);
            todoDetailBiz.cancel(toDOid);
            //返回
            resp.setStatus(String.valueOf(LklEcApplyStatusRespEnum.SIGN_ADDRESS_EXPIRED.getValue()));
            resp.setMsg(LklEcApplyStatusRespEnum.SIGN_ADDRESS_EXPIRED.getText());
            return resp;
        }
        final LklEcApplyStatusRespEnum respEnum = LklEcApplyStatusRespEnum.getEnumByValue(ecApplyDO.getDetailStatus());
        resp.setStatus(String.valueOf(respEnum.getValue()));
        resp.setMsg(ecApplyDO.getResult());
        resp.setUrl(ecApplyDO.getContractUrl());
        resp.setEcApplyId(Lists.newArrayList(AUDIT_PASSED.getValue(), AUDITING.getValue()).contains(respEnum.getValue())? ecApplyDO.getEcApplyId() : null);
        resp.setFeeMap(MapUtils.getMap(extraMap, LklEcApplyDO.ExtraMapConst.FEE_MAP));
        resp.setTradeComboId(MapUtils.getLong(extraMap, LklEcApplyDO.ExtraMapConst.TRADE_COMBO_ID));
        return resp;
    }

    @Override
    public void saveManualAuditingEcApply(LklEcApplyDTO dto) {
        final String merchantSn = dto.getMerchantSn();
        final String devCode = dto.getDevCode();
        //先查询当前商户有没有处于审核中的数据
        final Optional<LklEcApplyDO> processApply = lklEcApplyDAO.getProcessApply(merchantSn, devCode);
        if(processApply.isPresent()) {
            return;
        }
        final LklEcApplyDO lklEcApplyDO = new LklEcApplyDO();
        lklEcApplyDO.setMerchantSn(merchantSn);
        lklEcApplyDO.setEcApplyId(dto.getEcApplyId());
        lklEcApplyDO.setDevCode(devCode);
        lklEcApplyDO.setResult(dto.getResult());
        lklEcApplyDO.setStatus(LklEcApplyDO.Status.PROCESSING);
        lklEcApplyDO.setDetailStatus(LklEcApplyStatusRespEnum.AUDITING.getValue());
        lklEcApplyDO.setExtra(CommonUtil.map2String(CollectionUtil.hashMap(LklEcApplyDO.ExtraMapConst.FEE_MAP,dto.getFeeMap(),
                LklEcApplyDO.ExtraMapConst.TRADE_COMBO_ID,dto.getTradeComboId()
                )));
        lklEcApplyDAO.insertOne(lklEcApplyDO);

    }

    @Override
    public Map<String, String> updateManualAuditingEcApply(EcApplyPushDTO  dto) {
        Map<String,String> resultMap = CollectionUtil.hashMap("code","SUCCESS","message","执行成功");
        //待审核不做处理
        final String auditStatus = dto.getAuditStatus();
        if(Objects.equals(auditStatus,"WAIT")) {
            log.info("updateManualAuditingEcApply auditStatus WAIT");
            return resultMap;
        }
        final long ecApplyId = dto.getEcApplyId();
        final Optional<LklEcApplyDO> apply = lklEcApplyDAO.getApplyByEcApplyId(String.valueOf(ecApplyId));
        //不存在
        if(!apply.isPresent()) {
            log.info("updateManualAuditingEcApply apply not exist");
            return resultMap;
        }
        final LklEcApplyDO lklEcApplyDO = apply.get();
        final Integer detailStatus = lklEcApplyDO.getDetailStatus();
        //防止重复调用
        if(!Objects.equals(detailStatus,LklEcApplyStatusRespEnum.AUDITING.getValue())) {
            log.info("updateManualAuditingEcApply detailStatus:{}",detailStatus);
            return resultMap;
        }
        //REFUSE 审核拒绝
        if(Objects.equals(auditStatus,"REFUSE")) {
            updateEcApplyStatusByPrimaryId(lklEcApplyDO.getId(), LklEcApplyDO.Status.FAIL,LklEcApplyStatusRespEnum.AUDIT_FAILED.getValue(),dto.getAuditDesc());
            return resultMap;
        }
        //PASS 审核通过
        updateEcApplyStatusByPrimaryId(lklEcApplyDO.getId(), LklEcApplyDO.Status.PROCESSING, AUDIT_PASSED.getValue(), AUDIT_PASSED.getText());
        final LklEcApplyDO newEcApplyDO = new LklEcApplyDO();
        newEcApplyDO.setId(lklEcApplyDO.getId());
        //签约链接
        newEcApplyDO.setContractUrl(dto.getSignH5Url());
        final Map<String, Object> extraMap = Optional.ofNullable(lklEcApplyDO.getExtraMap())
                .orElseGet(HashMap::new);
        //记录链接过期时间
        final String signH5UrlExpTm = dto.getSignH5UrlExpTm();
        extraMap.put(LklEcApplyDO.ExtraMapConst.SIGN_H5_URL_EXP_TM, signH5UrlExpTm);
        //区分外卡审核还是刷卡审核
        final String merchantSn = lklEcApplyDO.getMerchantSn();
        String todoId = null;
        if(Objects.equals(lklEcApplyDO.getDevCode(),lklPsoDevCode)) {
            todoId = todoDetailBiz.addLKlBankPos(merchantSn, signH5UrlExpTm);
        }else if(Objects.equals(lklEcApplyDO.getDevCode(),lklForeignCardDevCode)) {
            todoId = todoDetailBiz.addLKlForeignCard(merchantSn, signH5UrlExpTm);
        }
        if(StringUtils.isNotBlank(todoId)) {
            extraMap.put(LklEcApplyDO.ExtraMapConst.TO_DO_ID,todoId);
        }
        //更新信息
        newEcApplyDO.setExtra(JSONObject.toJSONString(extraMap));
        lklEcApplyDAO.updateByPrimaryKeySelective(newEcApplyDO);
        return resultMap;
    }

    /**
     * 判断给定的时间字符串是否小于当前时间
     *
     * @param timeStr 给定的时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     * @return 如果给定时间在过去返回true，否则返回false。
     */
    public static boolean isTimeInPast(String timeStr) {
        try {
            // 解析给定的时间字符串为LocalDateTime对象
            LocalDateTime givenTime = LocalDateTime.parse(timeStr, FORMATTER);

            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();

            // 比较两个时间
            return givenTime.isBefore(now);
        } catch (DateTimeParseException e) {
            // 处理解析异常
           log.error("无法解析时间字符串: " + timeStr);
            throw new IllegalArgumentException("无效的时间格式", e);
        }
    }

    /**
     * 更新EC申请单的状态
     * @param id           主键id
     * @param status       新的状态
     * @param detailStatus 新的详细状态
     * @param result       新的结果描述
     */
    @Override
    public void updateEcApplyStatusByPrimaryId(Long id, Integer status, Integer detailStatus, String result) {
        // 创建一个新的LklEcApplyDO对象，用于更新EC申请单的状态
        final LklEcApplyDO newEcApplyDO = new LklEcApplyDO();
        newEcApplyDO.setId(id);
        // 设置新的状态
        newEcApplyDO.setStatus(status);
        // 设置新的详细状态
        newEcApplyDO.setDetailStatus(detailStatus);
        // 设置新的结果描述
        newEcApplyDO.setResult(result);
        // 使用DAO的updateByPrimaryKeySelective方法更新数据库中的EC申请单记录
        lklEcApplyDAO.updateByPrimaryKeySelective(newEcApplyDO);
    }


    @Override
    public void updateEcApplyStatusByApplyId(String applyId, Integer status, Integer detailStatus, String result) {
        // 创建一个新的LklEcApplyDO对象，用于更新EC申请单的状态
        final Optional<LklEcApplyDO> apply = lklEcApplyDAO.getApplyByEcApplyId(applyId);
        if(!apply.isPresent()){
            return;
        }
        final LklEcApplyDO newEcApplyDO = new LklEcApplyDO();
        newEcApplyDO.setId(apply.get().getId());
        // 设置新的状态
        newEcApplyDO.setStatus(status);
        // 设置新的详细状态
        newEcApplyDO.setDetailStatus(detailStatus);
        // 设置新的结果描述
        newEcApplyDO.setResult(result);
        // 使用DAO的updateByPrimaryKeySelective方法更新数据库中的EC申请单记录
        lklEcApplyDAO.updateByPrimaryKeySelective(newEcApplyDO);
    }

    @Override
    public void deleteLklEcApply(String merchantSn, String devCode) {
        final Optional<LklEcApplyDO> apply = lklEcApplyDAO.getApply(merchantSn, devCode);
        if(!apply.isPresent() || !Objects.equals(apply.get().getStatus(),LklEcApplyDO.Status.FAIL)) {
            return;
        }
        lklEcApplyDAO.deleteByPrimaryKey(apply.get().getId());
    }

    @Override
    public void cancelByMerchantSn(String merchantSn, String devCode) {
        todoDetailBiz.cancelByMerchantSn(merchantSn,devCode);
    }
}
