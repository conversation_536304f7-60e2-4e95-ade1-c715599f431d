package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.InternalKeyValueMapper;
import com.wosai.upay.job.refactor.model.entity.InternalKeyValueDO;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;


/**
 * 内部key value表数据库访问层 {@link InternalKeyValueDO}
 * 对InternalKeyValueMapper层做出简单封装 {@link InternalKeyValueMapper}
 *
 * <AUTHOR>
 */
@Repository
public class InternalKeyValueDAO extends AbstractBaseDAO<InternalKeyValueDO, InternalKeyValueMapper> {

    public InternalKeyValueDAO(SqlSessionFactory sqlSessionFactory, InternalKeyValueMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 新增一条键值对,存在则更新
     *
     * @param key   键
     * @param value 值
     * @return effect rows
     */
    public int insertOrUpdateKeyValue(String key, String value) {
        if (Objects.isNull(key)) {
            return 0;
        }
        InternalKeyValueDO internalKeyValueDO = new InternalKeyValueDO();
        internalKeyValueDO.setInternalKey(key);
        internalKeyValueDO.setInternalValue(value);
        LambdaUpdateWrapper<InternalKeyValueDO> wrapper = new LambdaUpdateWrapper<InternalKeyValueDO>()
                .eq(InternalKeyValueDO::getInternalKey, key)
                .set(InternalKeyValueDO::getInternalValue, value);
        int update = entityMapper.update(null, wrapper);
        return update > 0 ? update : entityMapper.insert(internalKeyValueDO);
    }

    /**
     * 根据键获取值
     *
     * @param key 键
     * @return 值
     */
    public Optional<String> getValueByKey(String key) {
        LambdaQueryWrapper<InternalKeyValueDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InternalKeyValueDO::getInternalKey, key);
        return super.selectOne(wrapper).map(InternalKeyValueDO::getInternalValue);
    }
}
