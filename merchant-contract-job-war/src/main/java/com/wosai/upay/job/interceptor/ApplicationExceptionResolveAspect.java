package com.wosai.upay.job.interceptor;

import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.job.exception.ApplicationException;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.util.ExceptionUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 只针对ContractApplicationServiceImpl 的切面
 **/
@Component
@Aspect
public class ApplicationExceptionResolveAspect {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationExceptionResolveAspect.class);

    public ApplicationExceptionResolveAspect() {
    }

    @Around("execution(public * com.wosai.upay.job.service.ContractApplicationServiceImpl.*(..))")
    public Object logAroundAllMethods(ProceedingJoinPoint pjp) throws Throwable {
        try {
            return pjp.proceed();
        } catch (Throwable e) {
            String message = "";
            int code = CommonResult.ERROR;
            logger.info("process application ex ", e);
            if (e instanceof CommonPubBizException) {
                code = CommonResult.BIZ_FAIL;
                message = e.getMessage();
            }
            if (e instanceof ApplicationException) {
                ApplicationException ex = (ApplicationException) e;
                code = ex.getCode();
                message = ex.getMessage();
            }
            if (e instanceof ConstraintViolationException) {
                ConstraintViolationException ex = (ConstraintViolationException) e;
                code = CommonResult.BIZ_FAIL;
                for (ConstraintViolation<?> constraintViolation : ex.getConstraintViolations()) {
                    message += constraintViolation.getMessage();
                }
            } else {
                message = ExceptionUtil.getThrowableMsg(e);
            }
            return new CommonResult(code, message);
        }

    }
}
