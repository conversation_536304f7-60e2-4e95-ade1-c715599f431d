package com.wosai.upay.job.refactor.task.license.micro.builder.processor.acquirer;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.task.license.micro.builder.context.TradeParamsBuilderContext;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.PaywayProcessor;

/**
 * 收单机构支付方式处理器（payway=0）
 * 
 * <AUTHOR>
 */
public class AcquirerPaywayProcessor implements PaywayProcessor {
    
    private final String acquirerType;
    
    public AcquirerPaywayProcessor(String acquirerType) {
        this.acquirerType = acquirerType;
    }
    
    @Override
    public void processParam(MerchantProviderParamsDO newParam,
                             MerchantProviderParamsDO oldParam,
                             TradeParamsBuilderContext context) {
        
        MerchantAcquireInfoBO merchantAcquireInfo =  context.getMerchantAcquireInfo();
        
        // 设置收单机构相关参数
        newParam.setPayMerchantId(merchantAcquireInfo.getAcquireMerchantId());
        
        // 根据不同收单机构设置特定参数
        switch (acquirerType) {
            case "LKLV3":
                newParam.setProviderMerchantId(merchantAcquireInfo.getUnionNo());
                break;
            case "FUYOU":
            case "HAIKE":
                newParam.setProviderMerchantId(merchantAcquireInfo.getAcquireMerchantId());
                break;
            default:
                // 默认处理
                break;
        }
    }
    
    @Override
    public Integer getSupportedPayway() {
        return PaywayEnum.ACQUIRER.getValue();
    }
    
    @Override
    public String getSupportedAcquirerType() {
        return acquirerType;
    }
}
