package com.wosai.upay.job.refactor.config.threadpool;

import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.validation.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Bean("agreementThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor agreementThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(1);
        threadPoolTaskExecutor.setMaxPoolSize(3);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        threadPoolTaskExecutor.setQueueCapacity(200);
        threadPoolTaskExecutor.setThreadNamePrefix("agreementThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("settlementHandleThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor settlementHandleThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(5);
        threadPoolTaskExecutor.setMaxPoolSize(5);
        threadPoolTaskExecutor.setKeepAliveSeconds(0);
        threadPoolTaskExecutor.setQueueCapacity(100);
        threadPoolTaskExecutor.setThreadNamePrefix("settlementHandleThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("appidConfigThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor appidConfigThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(1);
        threadPoolTaskExecutor.setMaxPoolSize(3);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        threadPoolTaskExecutor.setQueueCapacity(200);
        threadPoolTaskExecutor.setThreadNamePrefix("appidConfigThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("updateProviderTerminalThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor updateProviderTerminalThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(3);
        threadPoolTaskExecutor.setMaxPoolSize(3);
        threadPoolTaskExecutor.setKeepAliveSeconds(600);
        threadPoolTaskExecutor.setQueueCapacity(500);
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        threadPoolTaskExecutor.setThreadNamePrefix("updateProviderTerminalThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("lklMerchantRelationThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor lklMerchantRelationThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2);
        threadPoolTaskExecutor.setMaxPoolSize(2);
        threadPoolTaskExecutor.setKeepAliveSeconds(100);
        threadPoolTaskExecutor.setQueueCapacity(1000);
        threadPoolTaskExecutor.setThreadNamePrefix("lklMerchantRelationThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("backAcquirerSendMsgThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor backAcquirerSendMsgThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2);
        threadPoolTaskExecutor.setMaxPoolSize(2);
        threadPoolTaskExecutor.setKeepAliveSeconds(0);
        threadPoolTaskExecutor.setQueueCapacity(1000);
        threadPoolTaskExecutor.setRejectedExecutionHandler((r, executor) -> log.error("BackAcquirerBiz  reject{}", executor.toString()));
        threadPoolTaskExecutor.setThreadNamePrefix("backAcquirerSendMsgThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("sensorSendThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor sensorSendThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2);
        threadPoolTaskExecutor.setMaxPoolSize(5);
        threadPoolTaskExecutor.setKeepAliveSeconds(0);
        threadPoolTaskExecutor.setQueueCapacity(1000);
        threadPoolTaskExecutor.setRejectedExecutionHandler((r, executor) -> log.error("SensorSendBiz  reject{}", executor.toString()));
        threadPoolTaskExecutor.setThreadNamePrefix("sensorSendThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("updateTradeParamsThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor updateTradeParamsThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2);
        threadPoolTaskExecutor.setMaxPoolSize(2);
        threadPoolTaskExecutor.setKeepAliveSeconds(100);
        threadPoolTaskExecutor.setQueueCapacity(1000);
        threadPoolTaskExecutor.setThreadNamePrefix("updateTradeParamsThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("haikeBrandTerminalSyncThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor haikeBrandTerminalSyncThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2);
        threadPoolTaskExecutor.setMaxPoolSize(5);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        threadPoolTaskExecutor.setQueueCapacity(1000);
        threadPoolTaskExecutor.setThreadNamePrefix("haikeBrandTerminalSyncThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("merchantPayChangeThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor merchantPayChangeThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2);
        threadPoolTaskExecutor.setMaxPoolSize(5);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        threadPoolTaskExecutor.setQueueCapacity(1000);
        threadPoolTaskExecutor.setThreadNamePrefix("merchantPayChangeThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("fuyouUnionPayThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor fuyouUnionPayThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2);
        threadPoolTaskExecutor.setMaxPoolSize(5);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        threadPoolTaskExecutor.setQueueCapacity(1000);
        threadPoolTaskExecutor.setRejectedExecutionHandler((r, executor) -> log.error("FuyouProvider  setDefault  reject{}", executor.toString()));
        threadPoolTaskExecutor.setThreadNamePrefix("fuyouUnionPayThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("batchScheduleExecutorThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor batchScheduleExecutorThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2 * (Runtime.getRuntime().availableProcessors()));
        threadPoolTaskExecutor.setMaxPoolSize(200);
        threadPoolTaskExecutor.setKeepAliveSeconds(0);
        threadPoolTaskExecutor.setQueueCapacity(1024);
        threadPoolTaskExecutor.setRejectedExecutionHandler((r, executor) -> {
            log.error("handleBatchQueryAuthStatus  reject{}", executor.toString());
            throw new CommonPubBizException("超过最大线程数拒绝执行");
        });
        threadPoolTaskExecutor.setThreadNamePrefix("batchScheduleExecutorThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("batchProcessExecutorThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor batchProcessExecutorThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(6);
        threadPoolTaskExecutor.setMaxPoolSize(6);
        threadPoolTaskExecutor.setKeepAliveSeconds(0);
        threadPoolTaskExecutor.setQueueCapacity(6);
        threadPoolTaskExecutor.setThreadNamePrefix("batchProcessExecutorThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("businessLogThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor businessLogThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(1);
        threadPoolTaskExecutor.setMaxPoolSize(1);
        threadPoolTaskExecutor.setKeepAliveSeconds(0);
        threadPoolTaskExecutor.setQueueCapacity(2000);
        threadPoolTaskExecutor.setThreadNamePrefix("businessLogThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("acquirerChangeThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor acquirerChangeThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(0);
        threadPoolTaskExecutor.setMaxPoolSize(Integer.MAX_VALUE);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        threadPoolTaskExecutor.setQueueCapacity(0);
        threadPoolTaskExecutor.setThreadNamePrefix("acquirerChangeThreadPoolTaskExecutor");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }


    @Bean(name = "luzhouThreadPool")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(5);
        // 最大线程数
        executor.setMaxPoolSize(10);
        // 队列容量
        executor.setQueueCapacity(25);
        // 线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 线程名称前缀
        executor.setThreadNamePrefix("luzhou银行处理回调线程池-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        // 初始化线程池
        executor.initialize();

        return executor;
    }

    @Bean(name = "umbThreadPool")
    public ThreadPoolTaskExecutor umbThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(5);
        // 最大线程数
        executor.setMaxPoolSize(10);
        // 队列容量
        executor.setQueueCapacity(25);
        // 线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 线程名称前缀
        executor.setThreadNamePrefix("umb中投科信处理回调线程池-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        // 初始化线程池
        executor.initialize();

        return executor;
    }


    @Bean("submitAuthWhenMicroUpgradeThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor submitAuthWhenMicroUpgradeThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2);
        threadPoolTaskExecutor.setMaxPoolSize(6);
        threadPoolTaskExecutor.setKeepAliveSeconds(10);
        threadPoolTaskExecutor.setQueueCapacity(6);
        threadPoolTaskExecutor.setThreadNamePrefix("小微升级提交实名认证-");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskExecutor.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

}
