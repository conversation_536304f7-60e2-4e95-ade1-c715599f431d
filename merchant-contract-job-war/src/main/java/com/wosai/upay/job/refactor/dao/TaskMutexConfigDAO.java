package com.wosai.upay.job.refactor.dao;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.mapper.TaskMutexConfigDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.TaskMutexConfigDO;
import com.wosai.upay.job.refactor.model.enums.MutexTypeCategoryEnum;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/12/19
 */
@Repository
public class TaskMutexConfigDAO {

    @Resource
    private TaskMutexConfigDynamicMapper taskMutexConfigDynamicMapper;


    public Set<String> getMutexTypesByTaskType(String typeIdentifier, MutexTypeCategoryEnum category) {
        LambdaQueryWrapper<TaskMutexConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TaskMutexConfigDO::getTypeIdentifier, typeIdentifier)
                .eq(TaskMutexConfigDO::getTypeCategory, category.getCategory());
        TaskMutexConfigDO taskMutexConfigDO = taskMutexConfigDynamicMapper.selectOne(lambdaQueryWrapper);
        return Objects.isNull(taskMutexConfigDO) ? new HashSet<>() : JSON.parseObject(taskMutexConfigDO.getMutexTaskTypes(), Set.class);
    }
}
