package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.LklParam;
import com.wosai.upay.merchant.contract.service.LakalaService;
import com.wosai.upay.merchant.contract.service.NewLakalaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * Created by hzq on 19/4/2.
 */
@Component(ProviderUtil.LKL_PROVIDER_CHANNEL)
public class LklProvider extends AbstractProvider {
    private final static Logger log = LoggerFactory.getLogger(LklProvider.class);

    private static final String RESULT_CONTRACT_ID = "contractId";
    private static final int RESULT_SUCCESS = 200;


    @Autowired
    TradeConfigService tradeConfigService;
    @Autowired
    LakalaService lakalaService;
    @Autowired
    NewLakalaService newLakalaService;

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        // 非当前收单机构，更新不影响总状态
        int influPtask = AcquirerTypeEnum.LKL.getValue().equals(acquirer) || isSubBiz(merchantSn, AcquirerTypeEnum.LKL.getValue()) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.LKL_PROVIDER_CHANNEL)
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);

        Integer taskType = null;
        //银行账户变更
        if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
            Map requestParam = (Map) paramContext.get("cardRequestParam");
            if (!CollectionUtils.isEmpty(requestParam)) {
                //银行卡管理服务发起的变更(merchant_bank_account_pre)
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
            } else {
                //dts订阅直接变更(merchant_bank_account)
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
            }
        } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
            //更新基本信息
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
        } else if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()) {
            //更新费率
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE;
        } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {
            String crmUpdate = BeanUtil.getPropString(paramContext, "crmUpdate");
            if (!StringUtils.isEmpty(crmUpdate)) {
                if ("0".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
                } else if ("1".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                } else if ("2".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH;
                } else {
                    //do nothing
                }
            }

        } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
            //更新营业执照
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
        }

        if (taskType == null) {
            return null;
        }
        return subTask.setTask_type(taskType);
    }

    /**
     * @param:
     * @return:
     * @date: 16:14
     */
    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (needPayFor(contextParam, sub, contractTask)) {
            return null;
        }
        LklParam lklParam = buildParam(contractChannel, sub, LklParam.class);
        ContractResponse response = newLakalaService.contractMerchantWithParams(contextParam, lklParam);
        String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
        updateClearProvider(merchantId, sub.getMerchant_sn());
        return response;
    }

    /**
     * @param:
     * @return:
     * @date: 16:14
     */
    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(contractTask.getType())) {
            if (needPayFor(contextParam, sub, contractTask)) {
                return null;
            }
        }
        return callRemote(contextParam, sub);
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        throw new ContractBizException(getProviderBeanName() + "暂不支持配置微信appid");
    }

    private ContractResponse callRemote(Map contextParam, ContractSubTask contractSubTask) {
        String merchantSn = contractSubTask.getMerchant_sn();
        ContractResponse contractResponse = null;
        if (ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(contractSubTask.getTask_type())) {
            contractResponse = newLakalaService.updateMerchantBasic(merchantSn, contextParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(contractSubTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(contractSubTask.getTask_type())) {
            contractResponse = newLakalaService.updateMerchantBankAccount(merchantSn, contextParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(contractSubTask.getTask_type())) {
            contractResponse = newLakalaService.updateMerchantFeerate(merchantSn, contextParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE.equals(contractSubTask.getTask_type())) {
            contractResponse = newLakalaService.updateMerchantStatusToLkl(merchantSn, contextParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(contractSubTask.getTask_type())) {
            //do nothing
            contractResponse = newLakalaService.updateByCrm(merchantSn, contextParam, ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(contractSubTask.getTask_type())) {
            //更新营业执照
            contractResponse = newLakalaService.updateMerchantBusinessLicense(merchantSn, contextParam);
        }
        if (contractResponse == null) {
            log.error("lklProvider callRemote contractResponse null  contractSubTask {}", contractSubTask);
        }
        if (contractResponse != null && RESULT_SUCCESS == contractResponse.getCode()) {
            contractSubTask.setContract_id((String) contractResponse.getTradeParam().get(RESULT_CONTRACT_ID));
        }
        return contractResponse;
    }
}
