package com.wosai.upay.job.scheduler;


import com.wosai.upay.job.mapper.PendingTasksMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Description: 清理过期无用数据
 * <AUTHOR>
 * @Date 2022/7/11
 **/

@Component
@ConditionalOnProperty(name = "schedule.init", havingValue = "true")
@Slf4j
public class UselessDataCleanSchedule {


    @Autowired
    private PendingTasksMapper pendingTasksMapper;


    /**
     * 每天凌晨10分，清除PendingTask表，一个月前的数据
     * @throws Exception
     */
    @Scheduled(cron = "0 10 0 * * ?")
    public void submitMerchant() {
        log.info("清除PendingTask表一个月前历史数据开始");
        try {
            Date searchDate = DateUtils.addMonths(new Date(), -1);
            int result = pendingTasksMapper.deleteBeforeTargetDate(searchDate);
            log.info("清除PendingTask表一个月前历史数据结束，共清除：{} 条",result);
        }catch (Exception e){
            log.error("清除PendingTask表一个月前历史数据异常：{}",e.getMessage());
        }

    }


}
