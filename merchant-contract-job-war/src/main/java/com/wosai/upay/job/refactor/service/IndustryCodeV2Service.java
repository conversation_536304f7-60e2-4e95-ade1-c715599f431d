package com.wosai.upay.job.refactor.service;

import com.wosai.upay.job.refactor.model.dto.IndustryCodeV2DTO;
import com.wosai.upay.job.refactor.model.entity.IndustryCodeV2DO;

import java.util.List;
import java.util.Optional;


/**
 * industry_code_v2表Service层 {@link IndustryCodeV2DO}
 *
 * <AUTHOR>
 */
public interface IndustryCodeV2Service {

    /**
     * 根据行业id查询
     *
     * @param industryId {@link IndustryCodeV2DTO#getIndustryId()} ()}
     * @return Optional<IndustryCodeV2DTO> {@link IndustryCodeV2DTO}
     */
    Optional<IndustryCodeV2DTO> getIndustryCodeV2ByIndustryId(String industryId);

    /**
     * 获取所有的二级映射行业(数据量较少,直接获取所有)
     *
     * @return 所有的二级映射行业
     */
    List<IndustryCodeV2DTO> listAllIndustryCodeV2s();

}
