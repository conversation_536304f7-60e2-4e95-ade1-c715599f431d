package com.wosai.upay.job.providers;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.RetryException;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandDetailInfoQueryResp;
import com.wosai.upay.job.externalservice.brand.model.BrandMerchantInfoQueryResp;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.refactor.dao.ContractSubTaskExtDAO;
import com.wosai.upay.job.refactor.event.HaikeUnionPayContractSuccessEvent;
import com.wosai.upay.job.refactor.event.PayMchUpdateSuccessEvent;
import com.wosai.upay.job.refactor.event.UpdateMerchantBankAccountEvent;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskExtDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsExtDO;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.*;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.MerchantProviderTradeParams;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.haike.HaikeTerminalRequest;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.model.provider.UnionAlipayParam;
import com.wosai.upay.merchant.contract.model.provider.UnionWeixinParam;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.LogOutTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.UpdateTermInfoDTO;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;


/**
 * @Description: Haike处理类
 * <AUTHOR>
 * @Date 2023/7/10
 */
@Component(ProviderUtil.HAIKE_CHANNEL)
@Slf4j
@Transactional
public class HaikeProvider extends AbstractProvider {

    @Autowired
    private HaikeService haikeService;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private MerchantProviderParamsBiz merchantProviderParamsBiz;

    @Autowired
    private BrandBusinessClient brandBusinessClient;
    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private ContractStatusService contractStatusService;

    @Autowired
    private ContractSubTaskExtDAO contractSubTaskExtDAO;

    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;
    @Autowired
    @Qualifier("haikeBrandTerminalSyncThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor haikeBrandTerminalSyncThreadPoolTaskExecutor;


    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams param) {
        HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
        return haikeService.weixinSubdevConfig(weixinConfig, haikeParam);
    }


    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        // 非当前收单机构，更新不影响总状态
        int influPtask = AcquirerTypeEnum.HAI_KE.getValue().equals(acquirer) || isSubBiz(merchantSn, AcquirerTypeEnum.HAI_KE.getValue()) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.HAIKE_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);

        if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
            Integer taskType = null;
            //银行账户变更
            if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
                if (contractRule.getPayway() == null || PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
                    Map requestParam = (Map) paramContext.get("cardRequestParam");
                    if (!CollectionUtils.isEmpty(requestParam)) {
                        //银行卡管理服务发起的变更(merchant_bank_account_pre)
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
                    } else {
                        //dts订阅直接变更(merchant_bank_account)
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                    }
                }
            } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
                //更新基本信息
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
            } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {
                String crmUpdate = BeanUtil.getPropString(paramContext, "crmUpdate");
                if (!StringUtils.isEmpty(crmUpdate)) {
                    if ("0".equals(crmUpdate)) {
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
                    } else if ("1".equals(crmUpdate)) {
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                    } else if ("2".equals(crmUpdate)) {
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH;
                    } else {
                        //do nothing
                    }
                }

            } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
                if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
                    //更新营业执照
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
                }
            } else if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()) {
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE;
            }

            if (taskType == null) {
                return null;
            }
            return subTask.setTask_type(taskType);
        } else {
            Map eventMsg = JSON.parseObject(event.getEvent_msg(), Map.class);
            if (org.springframework.util.CollectionUtils.isEmpty(eventMsg)) {
                log.error("lkl_org channelNo {} 更新事件 {} eventMsg为空", contractRule.getChannelNo(), event.getId());
                return null;
            }
            if (WosaiMapUtils.getBooleanValue(eventMsg, ContractEvent.FORCE_UPDATE)) {
                subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE);
                return subTask;
            }
            List<String> changeFileds = (List) eventMsg.get("msg");
            if (!ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(event.getEvent_type()) && CollectionUtils.isEmpty(changeFileds)) {
                log.error("lkl_org channelNo {} 更新事件 {} msg为空", contractRule.getChannelNo(), event.getId());
                return null;
            }
            if (ProviderUtil.getPayWayUpdate(contractRule.getPayway(), event.getEvent_type(), changeFileds)) {
                subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE);
                return subTask;
            }

            return null;
        }
    }


    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        HaikeParam haikeParam = buildParam(contractChannel, sub, HaikeParam.class);
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        ContractRule contractRule = ruleContext.getContractRule(sub.getContract_rule());
        ContractResponse tarResponse = null;
        if (contractRule.getType() == 3) {
            List<MerchantProviderParams> paramsList = new ArrayList<>();
            if (sub.getSchedule_dep_task_id() != null) {
                ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(sub.getSchedule_dep_task_id());
                paramsList.add(merchantProviderParamsMapper.selectByPrimaryKey(BeanUtil.getPropString(subTask.getResponseBody(), "merchantProviderParamsId")));
            } else {
                paramsList = merchantProviderParamsMapper.getParamsByChannelAndPayWay(sub.getMerchant_sn(), contractChannel.getChannel_no(), payWay.toString());
            }
            if (CollectionUtils.isEmpty(paramsList)) {
                return null;
            }
            if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
                for (MerchantProviderParams params : paramsList) {
                    tarResponse = haikeService.syncMerchantToHaike(params.getPay_merchant_id());
                    if (!tarResponse.isSuccess()) {
                        return tarResponse;
                    }
                    merchantProviderParamsBiz.syncHaikeTerminal(params);
                }
                return tarResponse;
            } else if (payWay.equals(PaywayEnum.WEIXIN.getValue())) {
                for (MerchantProviderParams params : paramsList) {
                    tarResponse = haikeService.syncMerchantToHaike(params.getPay_merchant_id());
                    if (!tarResponse.isSuccess()) {
                        return tarResponse;
                    }
                    merchantProviderParamsBiz.syncHaikeTerminal(params);
                }
                return tarResponse;
            }
        } else {
            if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
                if (needPayFor(contextParam, sub, contractTask)) {
                    return null;
                }
                ContractResponse response = haikeService.contractMerchant(contextParam, haikeParam);
                String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
                if (response.isSuccess()) {
                    updateClearProvider(merchantId, sub.getMerchant_sn());
                }
                return response;
            } else if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
                UnionAlipayParam unionAlipayParam = buildParam(contractChannel, sub, UnionAlipayParam.class);
                return haikeService.contractAlipayWithParams(contextParam, unionAlipayParam);
            } else if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
                UnionWeixinParam unionWeixinParam = buildParam(contractChannel, sub, UnionWeixinParam.class);
                return haikeService.contractWeixinWithParams(contextParam, unionWeixinParam);
            } else if (PaywayEnum.UNIONPAY.getValue().equals(payWay)) {
                MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(contractTask.getMerchant_sn(), ProviderEnum.PROVIDER_HAIKE.getValue());
                ContractResponse contractResponse = haikeService.queryMerchantContractResult(acquirerParams.getProvider_merchant_id(), haikeParam);
                if (contractResponse.isSuccess()) {
                    applicationEventPublisher.publishEvent(new HaikeUnionPayContractSuccessEvent(this, contractTask.getMerchant_sn()));
                }
                return contractResponse;
            }
        }
        return null;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (needPayFor(contextParam, sub, contractTask)) {
            return null;
        }
        Optional<ContractSubTaskExtDO> subTaskExtOP = contractSubTaskExtDAO.getBySubTaskId(sub.getId());

        if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
            HaikeParam haikeParam = buildParam(contractChannel, sub, HaikeParam.class);
            if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(contractTask.getType())) {
                if (needPayFor(contextParam, sub, contractTask)) {
                    return null;
                }
                ContractResponse response = haikeService.updateBankAccountMerchant(contextParam, haikeParam);
                if (response.isSuccess()) {
                    applicationEventPublisher.publishEvent(new UpdateMerchantBankAccountEvent(this,
                            sub.getId(),
                            ProviderEnum.PROVIDER_HAIKE.getValue(),
                            ProviderUtil.HAIKE_CHANNEL));
                }
                return response;
            }
            Integer subTaskType = sub.getTask_type();
            if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(subTaskType)
                    || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(subTaskType)
                    || ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(subTaskType)) {
                ContractResponse response = haikeService.updateBankAccountMerchant(contextParam, haikeParam);
                if (response.isSuccess()) {
                    applicationEventPublisher.publishEvent(new UpdateMerchantBankAccountEvent(this,
                            sub.getId(),
                            ProviderEnum.PROVIDER_HAIKE.getValue(),
                            ProviderUtil.HAIKE_CHANNEL));
                }
                return response;
            }
            ContractResponse response = haikeService.updateBasicMerchant(contextParam, haikeParam);
            if (response.isSuccess()) {
                try {
                    String unMcc = BeanUtil.getPropString(response.getRequestParam(), "merchant_data.un_mcc");
                    String merchantBusinessName = BeanUtil.getPropString(response.getRequestParam(), "merchant_data.merch_short_name");
                    syncBusinessNameUnMcc2Union(contractTask.getMerchant_sn(), merchantBusinessName, unMcc);
                    if (isUpdateLicenseNeedUpdateAccount(contextParam)) {
                        applicationEventPublisher.publishEvent(new UpdateMerchantBankAccountEvent(this,
                                sub.getId(),
                                ProviderEnum.PROVIDER_HAIKE.getValue(),
                                ProviderUtil.HAIKE_CHANNEL));
                    }
                } catch (Exception e) {
                    log.error("更新云闪付商户名 {}", contractTask.getMerchant_sn(), e);
                }
            }
            return response;
        } else if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            UnionAlipayParam unionAlipayParam = buildParam(contractChannel, sub, UnionAlipayParam.class);
            ContractResponse response = subTaskExtOP.isPresent() ? haikeService.updateAlipayBySubMchId(subTaskExtOP.get().getPayMerchantId(), contextParam, unionAlipayParam) :
                    haikeService.updateAlipayWithParams(contextParam, unionAlipayParam);
            if (response.isSuccess() && !subTaskExtOP.isPresent()) {
                applicationEventPublisher.publishEvent(new PayMchUpdateSuccessEvent(sub,
                        contractTask.getMerchant_sn(),
                        ProviderEnum.PROVIDER_HAIKE.getValue(),
                        PaywayEnum.ALIPAY.getValue()));
            }
            return response;
        } else if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            UnionWeixinParam unionWeixinParam = buildParam(contractChannel, sub, UnionWeixinParam.class);
            ContractResponse response = subTaskExtOP.isPresent() ? haikeService.updateWechatBySubMchId(subTaskExtOP.get().getPayMerchantId(), contextParam, unionWeixinParam) :
                    haikeService.updateWeixinWithParams(contextParam, unionWeixinParam);
            if (response.isSuccess() && !subTaskExtOP.isPresent()) {
                applicationEventPublisher.publishEvent(new PayMchUpdateSuccessEvent(sub,
                        contractTask.getMerchant_sn(),
                        ProviderEnum.PROVIDER_HAIKE.getValue(),
                        PaywayEnum.WEIXIN.getValue()));
            }
            return response;
        }
        return null;
    }

    @Override
    public ContractResponse queryMerchantContractResult(String providerMerchantId) {
        HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
        ContractResponse contractResponse = haikeService.queryMerchantContractResult(providerMerchantId, haikeParam);
        if (contractResponse.isSuccess()) {
            contractResponse.setTradeParam(CollectionUtil.hashMap("status", MerchantProviderParamsExtDO.UNION_PAY_SUCCESS));
            // 没设默认的云闪付参数，尝试设置默认
            checkUnionSetDefault(contractResponse, providerMerchantId);
        }
        return contractResponse;

    }

    private void checkUnionSetDefault(ContractResponse contractResponse, String providerMerchantId) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andProvider_merchant_idEqualTo(providerMerchantId)
                .andPaywayEqualTo(PaywayEnum.UNIONPAY.getValue())
                .andProviderEqualTo(ProviderEnum.PROVIDER_HAIKE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        MerchantProviderParams unionParams = null;
        if (params != null && !params.isEmpty()) {
            unionParams = params.get(0);
        }
        if (unionParams != null) {
            ContractStatus contractStatus = contractStatusService.selectByMerchantSn(unionParams.getMerchant_sn());
            if (AcquirerTypeEnum.HAI_KE.getValue().equals(contractStatus.getAcquirer()) && unionParams.getStatus() == 0) {
                Map responseParam = contractResponse.getResponseParam();
                Map channelBiz = WosaiMapUtils.getMap(responseParam, "channel_biz_info");
                if (WosaiMapUtils.isEmpty(channelBiz)) {
                    return;
                }
                Map bankInfo = WosaiMapUtils.getMap(channelBiz, "bank_info");
                if ("3".equals(BeanUtil.getPropString(bankInfo, "apply_status")) && "3".equals(BeanUtil.getPropString(bankInfo, "report_status"))) {
                    merchantProviderParamsService.setDefaultMerchantProviderParams(unionParams.getId(), null, "查询时海科云闪付状态变更为成功，重新设置默认");
                }
            }
        }
    }

    @Override
    public void createProviderTerminal(String merchantSn, Integer provider) {
        doCreateProviderTerminal(merchantSn, provider);
    }


    /**
     * 终端/子商户号绑定
     *
     * @param termInfoDTO
     * @param payWay
     * @param terminalSn
     * @return
     */
    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO termInfoDTO, int payWay, String terminalSn) {
        ContractResponse response = null;
        HaikeTerminalRequest.TerminalInfo terminalInfo = new HaikeTerminalRequest.TerminalInfo();
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(termInfoDTO.getSubMchId(), UnionAlipayParam.class);
            response = haikeService.addAliTermInfo(termInfoDTO, alipayParam);
            terminalInfo.setAli_sub_merchant_no(termInfoDTO.getSubMchId());
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(termInfoDTO.getSubMchId(), UnionWeixinParam.class);
            response = haikeService.addWxTermInfo(termInfoDTO, weixinParam);
            terminalInfo.setWx_sub_merchant_no(termInfoDTO.getSubMchId());
        }
        // subMchId对应的商户号和绑定的商户号不一致，证明是品牌模式, 品牌模式用特殊方式来同步终端信息，其他继续调用原有方法
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(termInfoDTO.getSubMchId());
        if (Objects.nonNull(merchantProviderParams) && !Objects.equals(termInfoDTO.getMerchantSn(), merchantProviderParams.getMerchant_sn())) {
            syncBrandTerminal(termInfoDTO, payWay);
        } else {
            return haikeService.syncTerminalToHaike(termInfoDTO.getMerchantSn(), termInfoDTO.getDeviceId(), payWay);
        }
        return response;
    }

    private void syncBrandTerminal(AddTermInfoDTO termInfoDTO, int payWay) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(termInfoDTO.getMerchantSn(), ProviderEnum.PROVIDER_HAIKE.getValue());
        MerchantProviderParams unionPayParams = merchantProviderParamsMapper.getParamsByProviderMerchantIdAndProviderAndPayWay(acquirerParams.getProvider_merchant_id(), String.valueOf(ProviderEnum.PROVIDER_HAIKE.getValue()), String.valueOf(PaywayEnum.UNIONPAY.getValue()));
        HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
        ContractResponse contractResponse = haikeService.queryMerchantContractResult(acquirerParams.getProvider_merchant_id(), haikeParam);
        HaikeTerminalRequest request = new HaikeTerminalRequest();
        request.setMerch_no(acquirerParams.getPay_merchant_id());
        HaikeTerminalRequest.TerminalInfo syncTerminal = new HaikeTerminalRequest.TerminalInfo();
        syncTerminal.setTerminal_id(termInfoDTO.getDeviceId());
        syncTerminal.setSn(termInfoDTO.getDeviceId());
        // 如果云闪付成功再去同步云闪付子商户号
        if (contractResponse.isSuccess()) {
            syncTerminal.setUnion_merchant_no(unionPayParams.getPay_merchant_id());
        }
        if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            syncTerminal.setAli_sub_merchant_no(termInfoDTO.getSubMchId());
        } else if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            syncTerminal.setWx_sub_merchant_no(termInfoDTO.getSubMchId());
        }
        request.setTerminal_info_list(Collections.singletonList(syncTerminal));
        haikeBrandTerminalSyncThreadPoolTaskExecutor.submit(
                () -> {
                    try {
                        RetryerUtil.getHaikeBrandTerminalSyncRetryer().call(() -> haikeService.syncTerminalToHaike(request, haikeParam));
                    } catch (ExecutionException e) {
                        log.error("品牌模式同步终端到海科执行异常：{} {}", JSON.toJSONString(termInfoDTO), e.getMessage(), e);
                        chatBotUtil.sendMessageToPaymentModeChangeChatBot(String.format("品牌模式同步终端到海科执行异常, 商户号:%s, 终端号:%s, 原因: %s", termInfoDTO.getMerchantSn(), termInfoDTO.getDeviceId(), e.getMessage()));
                    } catch (RetryException e) {
                        ContractResponse result = (ContractResponse) e.getLastFailedAttempt().getResult();
                        log.error("品牌模式同步终端到海科重试异常：{} {}", JSON.toJSONString(termInfoDTO), result.getMessage(), e);
                        chatBotUtil.sendMessageToPaymentModeChangeChatBot(String.format("品牌模式同步终端到海科重试异常, 商户号:%s, 终端号:%s, 原因: %s", termInfoDTO.getMerchantSn(), termInfoDTO.getDeviceId(), result.getMessage()));
                    }
                }
        );
    }

    @Override
    public void handleSqbTerminalBind(MerchantInfo merchant, Map terminal, Integer provider) {
        if (!applicationApolloConfig.getHaikeStoreTerminalSwitch()) {
            return;
        }
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        //该通道下所有间连子商户号
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayIn(Arrays.asList(PaywayEnum.WEIXIN.getValue(), PaywayEnum.ALIPAY.getValue(), PaywayEnum.UNIONPAY.getValue()))
                .andProviderEqualTo(provider)
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_HAIKE.getValue());
        //provider_terminal 不存在该门店的终端记录
        final ProviderTerminal providerTerminal = providerTerminalBiz.existProviderTerminal(provider, storeSn, merchantSn, terminalSn, acquirerParams.getPay_merchant_id());
        //判断是否需要插入绑定任务
        if (Objects.isNull(providerTerminal)) {
            final String providerTerminalId = providerTerminalIdBiz.nextProviderTerminalId();
            if (Objects.isNull(providerTerminalId)) {
                return;
            }
            providerTerminalBiz.sqbTerminalConnectionProviderTerminal(merchantSn, providerTerminalId, acquirerParams.getPay_merchant_id(), ProviderEnum.PROVIDER_HAIKE.getValue(), vendorAppAppid, terminalSn, storeSn);
            log.info("终端绑定=>新创建终端Id:{},门店:{},交易参数:{}", providerTerminalId, storeSn, JSONObject.toJSONString(params));
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(), param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                            providerTerminalId,
                            storeSn,
                            terminalSn));
            return;
        }

        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("终端绑定=>已经存在收钱吧终端Id:{},门店:{},交易参数:{}", providerTerminal.getProvider_terminal_id(), storeSn, JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(), param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        terminalSn));
    }

    /**
     * 收钱吧门店绑定
     *
     * @param storeSn    门店号
     * @param merchantSn 商户号
     * @param provider   收单机构provider
     */
    @Override
    public void handleSqbStoreTerminal(String storeSn, String merchantSn, Integer provider) {
        if (!applicationApolloConfig.getHaikeStoreTerminalSwitch()) {
            return;
        }
        // 判断是不是品牌商户
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        BrandMerchantInfoQueryResp brandMerchantInfoQueryResp = brandBusinessClient.getBrandMerchantInfoByMerchantId(WosaiMapUtils.getString(merchant, DaoConstants.ID));
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_HAIKE.getValue());
        // 如果是品牌子商户 && 是品牌支付模式 =》走品牌的终端绑定逻辑，生成两个终端号，一个和自己的子商户号做绑定，一个和品牌主商户的子商户号做绑定
        if (brandMerchantInfoQueryResp.isSubBrandMerchant() && brandMerchantInfoQueryResp.isBrandPayMode()) {
            doHandleBrandStoreTerminal(merchantSn, storeSn, provider, brandMerchantInfoQueryResp.getBrandId(), acquirerParams);
            return;
        }
        final ProviderTerminal providerTerminal = providerTerminalBiz.getStoreProviderTerminal(provider, storeSn, merchantSn, acquirerParams.getPay_merchant_id());

        //该通道下所有间连子商户号
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayIn(Arrays.asList(PaywayEnum.WEIXIN.getValue(), PaywayEnum.ALIPAY.getValue(), PaywayEnum.UNIONPAY.getValue()))
                .andProviderEqualTo(provider)
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        if (Objects.isNull(providerTerminal)) {
            final String termNo = providerTerminalIdBiz.nextProviderTerminalId();
            providerTerminalBiz.sqbStoreTerminalConnectionProviderTerminal(merchantSn, termNo, acquirerParams.getPay_merchant_id(), provider, storeSn);
            log.info("海科门店绑定=>门店号:{},不存在门店级别,现在开始重新绑定,终端Id:{},交易参数:{}", merchantSn, termNo, JSONObject.toJSONString(params));
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(),
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                            termNo,
                            storeSn,
                            null));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("海科商户绑定=>商户号:{},已经存在终端新增子商户号,终端Id:{},交易参数:{}", merchantSn, providerTerminal.getProvider_terminal_id(), JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(),
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        null)
        );
    }

    private void doHandleBrandStoreTerminal(String merchantSn, String storeSn, Integer provider, String brandId, MerchantProviderParams acquirerParams) {
        List<MerchantProviderParams> selfParams = getMerchantProviderParamsByProvider(provider, acquirerParams.getMerchant_sn());
        BrandDetailInfoQueryResp brandDetailInfoQueryResp = brandBusinessClient.getBrandDetailInfoByBrandId(brandId);
        String brandMainMerchantSn = brandDetailInfoQueryResp.getMainMerchantSn();
        MerchantProviderParams brandAcquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(brandMainMerchantSn, ProviderEnum.PROVIDER_HAIKE.getValue());
        List<MerchantProviderParams> brandParams = getMerchantProviderParamsByProvider(provider, brandAcquirerParams.getMerchant_sn());
        // 生成绑定任务
        produceBoundTask(selfParams, acquirerParams.getPay_merchant_id(), merchantSn, provider, storeSn, false);
        produceBoundTask(brandParams, brandAcquirerParams.getPay_merchant_id(), merchantSn, provider, storeSn, true);
    }

    /**
     * 门店级别终端绑定 将targetParams中的子商户号和acquirerParams的收单机构商户号做绑定
     *
     * @param targetParams       待绑定的参数列表
     * @param acquirerMerchantId 要绑定到的收单机构商户号
     * @param merchantSn         商户号
     * @param provider           通道
     * @param storeSn            门店号
     */
    private void produceBoundTask(List<MerchantProviderParams> targetParams, String acquirerMerchantId, String merchantSn, Integer provider, String storeSn, boolean isBrand) {
        ProviderTerminal providerTerminal = providerTerminalBiz.getStoreProviderTerminal(provider, storeSn, merchantSn, acquirerMerchantId);
        if (Objects.isNull(providerTerminal)) {
            String termNo = providerTerminalIdBiz.nextProviderTerminalId();
            providerTerminalBiz.sqbStoreTerminalConnectionProviderTerminal(merchantSn, termNo, acquirerMerchantId, provider, storeSn);
            targetParams.forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(),
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                            termNo,
                            storeSn,
                            null,
                            isBrand ? param.getMerchant_sn() : null));
        } else {
            //判断是否需要插入绑定任务
            final List<MerchantProviderParams> notSyncParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, targetParams);
            //已经绑定了不用处理
            if (WosaiCollectionUtils.isNotEmpty(notSyncParams)) {
                notSyncParams.forEach(param ->
                        providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                                param.getPay_merchant_id(),
                                param.getProvider(),
                                param.getPayway(),
                                ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                                providerTerminal.getProvider_terminal_id(),
                                storeSn,
                                null,
                                isBrand ? param.getMerchant_sn() : null));
            }
        }
    }


    @Override
    public ContractResponse unbindTerminal(LogOutTermInfoDTO dto, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionAlipayParam.class);
            response = haikeService.LogOutAliTermInfo(dto, alipayParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionWeixinParam.class);
            response = haikeService.LogOutWxTermInfo(dto, weixinParam);
        }
        return response;
    }

    @Override
    public ContractResponse updateTerminal(UpdateTermInfoDTO dto, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionAlipayParam.class);
            response = haikeService.updateAliTermInfo(dto, alipayParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionWeixinParam.class);
            response = haikeService.updateWxTermInfo(dto, weixinParam);
        }
        return response;
    }

    public void syncBusinessNameUnMcc2Union(String merchantSn, String merchantBusinessName, String unMcc) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(ProviderEnum.PROVIDER_HAIKE.getValue())
                .andPaywayEqualTo(PaywayEnum.UNIONPAY.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        boolean needUpdate = false;
        MerchantProviderParams param = merchantProviderParams.get(0);
        MerchantProviderParams updateValue = new MerchantProviderParams();
        updateValue.setId(param.getId());
        if (WosaiStringUtils.isNotEmpty(merchantBusinessName)) {
            updateValue.setMerchant_name(merchantBusinessName);
            needUpdate = true;
        }
        if (WosaiStringUtils.isNotEmpty(unMcc)) {
            updateValue.setAli_mcc(unMcc);
            needUpdate = true;
        }
        if (!needUpdate) {
            return;
        }
        merchantProviderParamsMapper.updateByPrimaryKeySelective(updateValue);
        if (Objects.equals(param.getStatus(), UseStatusEnum.IN_USE.getValue())) {
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.UNIONPAY.getValue());
            // 再次检查防止交易侧数据不是海科
            if (Objects.equals(ProviderEnum.PROVIDER_HAIKE.getValue(), BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER))) {
                CompletableFuture.runAsync(() -> merchantProviderParamsService.setDefaultMerchantProviderParams(param.getId(), null, "更新云闪付经营名称或mcc"));
                log.info("更新云闪付 {} {} {}", merchantSn, merchantBusinessName, unMcc);
            }
        }
    }
    @Override
    public ContractResponse doProcessMicroUpgradeTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        log.info("contractTask id {},ContractSubTask id {}",contractTask.getId(),sub.getId());
        final String merchantSn = contractTask.getMerchant_sn();
        HaikeParam haikeParam = buildParam(contractChannel, sub, HaikeParam.class);
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        ContractRule contractRule = ruleContext.getContractRule(sub.getContract_rule());
        ContractResponse tarResponse = null;
        if (contractRule.getType() == 3) {
            final Long taskId = contractTask.getId();
            final List<ContractSubTaskDO> subTaskDOS = contractSubTaskDAO.listByPTaskId(taskId);
            //查询当前支付方式下需要同步哪些任务
            final List<Long> scheduleDepTaskIdList = subTaskDOS.stream()
                    .filter(r -> Objects.equals(r.getPayway(), payWay)
                    && StrUtil.contains(r.getContractRule(), "sync")
                    && Objects.equals(r.getChangeConfig(), 0))
                    .map(ContractSubTaskDO::getScheduleDepTaskId)
                    .collect(Collectors.toList());
            //需要同步的任务对应的支付源子商户号
            final List<String> mchIdList = subTaskDOS.stream()
                    .filter(r -> scheduleDepTaskIdList.contains(r.getId()))
                    .map(r -> {
                        if(Objects.equals(PaywayEnum.WEIXIN.getValue(), r.getPayway())) {
                            return BeanUtil.getNestedProperty(JSON.parseObject(r.getResponseBody(), Map.class), "responseParam.sub_mch_id").toString();
                        } else if (Objects.equals(PaywayEnum.ALIPAY.getValue(), r.getPayway())) {
                            return  BeanUtil.getNestedProperty(JSON.parseObject(r.getResponseBody(), Map.class), "responseParam.sub_merchant_id").toString();
                        }
                       return null;
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mchIdList)) {
                log.warn("海科同步支付源子商户号异常taskId:{},subTaskId:{},获取对应支付方式字商户号为空",taskId,sub.getId());
                return null;
            }
            final String haikeMchId = getHaikeMchIdByResponse(contractTask.getId());
            if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
                UnionAlipayParam unionAlipayParam = buildParam(contractChannel, sub, UnionAlipayParam.class);
                for (String mchId : mchIdList) {
                    tarResponse = haikeService.microUpgradeSyncMerchantToHaike(mchId,payWay,haikeMchId,contextParam,unionAlipayParam.getChannel_no());
                    if (!tarResponse.isSuccess()) {
                        return tarResponse;
                    }
                }
                return tarResponse;
            } else if (payWay.equals(PaywayEnum.WEIXIN.getValue())) {
                UnionWeixinParam unionWeixinParam = buildParam(contractChannel, sub, UnionWeixinParam.class);
                for (String mchId : mchIdList) {
                    tarResponse = haikeService.microUpgradeSyncMerchantToHaike(mchId,payWay,haikeMchId,contextParam,unionWeixinParam.getChannel_no());
                    if (!tarResponse.isSuccess()) {
                        return tarResponse;
                    }
                }
                return tarResponse;
            }
        } else {
            if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
                ContractResponse response = haikeService.contractToAcquirer(contextParam, haikeParam);
                return response;
            } else if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
                UnionAlipayParam unionAlipayParam = buildParam(contractChannel, sub, UnionAlipayParam.class);
                final String haikeMchId = getHaikeMchIdByResponse(contractTask.getId());
                return haikeService.microUpgradeContractAlipayWithParams(contextParam,unionAlipayParam,haikeMchId);
            } else if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
                final String haikeMchId = getHaikeMchIdByResponse(contractTask.getId());
                UnionWeixinParam unionWeixinParam = buildParam(contractChannel, sub, UnionWeixinParam.class);
                return haikeService.microUpgradeContractWeixinWithParams(contextParam, unionWeixinParam,haikeMchId);
            } else if (PaywayEnum.UNIONPAY.getValue().equals(payWay)) {
                final String haikeMchId = getHaikeMchIdByResponse(contractTask.getId());
                ContractResponse contractResponse = haikeService.microUpgradeQueryMerchantContractResult(haikeMchId, haikeParam,merchantSn);
//                if (contractResponse.isSuccess()) {
//                    applicationEventPublisher.publishEvent(new HaikeUnionPayContractSuccessEvent(this, merchantSn));
//                }
                return contractResponse;
            }
        }
        return null;
    }



    /**
     * 通过入网任务返回获取海科商户号
     * @param pTaskId
     * @return
     */
    public String getHaikeMchIdByResponse(Long pTaskId) {
        final List<ContractSubTaskDO> subTaskDOS = contractSubTaskDAO.listByPTaskId(pTaskId);
        final Optional<ContractSubTaskDO> first = subTaskDOS.stream().filter(r -> PaywayEnum.ACQUIRER.getValue().equals(r.getPayway())
                && Objects.equals(r.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                && Objects.equals(r.getTaskType(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                && Objects.equals(r.getContractRule(), "haike")
                && StrUtil.contains(r.getRuleGroupId(), ContractRuleConstants.RULE_GROUP_MICROUPGRADE)
        ).findFirst();
        return first.map(sub-> {
                    final String responseBody = sub.getResponseBody();
                    return BeanUtil.getNestedProperty(JSON.parseObject(responseBody, Map.class),"responseParam.merch_no").toString();
                })
                .orElseThrow(() -> new ContractBizException("没有找到对应的入网任务"));
    }
}