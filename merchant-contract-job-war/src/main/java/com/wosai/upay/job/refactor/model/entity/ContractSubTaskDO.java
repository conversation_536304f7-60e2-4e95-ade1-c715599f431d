package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 子任务表表实体对象
 *
 * <AUTHOR>
 */
@TableName("contract_sub_task")
@Data
public class ContractSubTaskDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 父任务编号
     */
    @TableField(value = "p_task_id")
    private Long pTaskId;
    /**
     * 商户sn 方便查询
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 进件通道 lkl,union_wanma,union_common 等等
     */
    @TableField(value = "channel")
    private String channel;
    /**
     * 是否是默认通道  0否 1是
     */
    @TableField(value = "default_channel")
    private Integer defaultChannel;
    /**
     * 成功后是否切通道 0否 1是
     */
    @TableField(value = "change_config")
    private Integer changeConfig;
    /**
     * 切通道时 必要参数
     */
    @TableField(value = "change_body")
    private Integer changeBody;
    /**
     * 0基本信息变更 1商户状态 2结算账户变更 3费率更新 4银行卡更新 5进件 6更新
     */
    @TableField(value = "task_type")
    private Integer taskType;
    /**
     * 进件通道 外部唯一编号 异步回调时可能需要 可以为空
     */
    @TableField(value = "contract_id")
    private String contractId;
    /**
     * 进件支付方式 可以为空(例 进件拉卡拉时为空)
     */
    @TableField(value = "payway")
    private Integer payway;
    /**
     * 此任务的请求body
     */
    @TableField(value = "request_body")
    private String requestBody;
    /**
     * 此任务的响应body
     */
    @TableField(value = "response_body")
    private String responseBody;
    /**
     * 是否可调度 0否 1可调度
     */
    @TableField(value = "schedule_status")
    private Integer scheduleStatus;
    /**
     * 依赖的任务id 不可调度时 必须
     */
    @TableField(value = "schedule_dep_task_id")
    private Long scheduleDepTaskId;
    /**
     * 处理状态 0待处理 1处理中 4系统异常失败 5处理成功  6处理失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 失败是否影响主流程 0否 1是
     */
    @TableField(value = "status_influ_p_task")
    private Integer statusInfluPTask;

    @TableField(value = "priority")
    private Timestamp priority;
    /**
     * 备注 用于页面上展示
     */
    @TableField(value = "result")
    private String result;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;

    @TableField(value = "version")
    private Long version;
    /**
     * 报备规则
     */
    @TableField(value = "contract_rule")
    private String contractRule;
    /**
     * 报备规则组
     */
    @TableField(value = "rule_group_id")
    private String ruleGroupId;
    /**
     * 已重试次数
     */
    @TableField(value = "retry")
    private Integer retry;

}

