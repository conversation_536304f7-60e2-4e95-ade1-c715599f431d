package com.wosai.upay.job.refactor.model.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 银行保障交易申请excelBO
 *
 * <AUTHOR>
 * @date 2024/8/15 11:48
 */
@Data
public class BankTradeProtectionApplyExcelBO {

    @Excel(name = "收钱吧商户号", isImportField = "true", width = 20)
    @NotEmpty(message = "商户号不能为空")
    private String merchantSn;
}
