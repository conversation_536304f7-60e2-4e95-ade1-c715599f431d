package com.wosai.upay.job.refactor.task.rotational;


import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;

/**
 * 轮询任务处理器
 */
public interface RotationalSubTaskProcessor {

    /**
     * 获取子任务类型
     *
     * @return 子任务类型
     */
    RotationalSubTaskTypeEnum getSubTaskType();

    /**
     * 处理具体轮询子任务
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     * @return 处理结果
     */
    InternalScheduleSubTaskProcessResultBO handleRotationalSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO);
}
