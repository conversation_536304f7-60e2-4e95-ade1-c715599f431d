package com.wosai.upay.job.providers;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.terminal.basic.TerminalBasicInsertEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;
import com.wosai.upay.core.bean.request.TradeExtConfigCreateRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigQueryRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigUpdateRequest;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.bankDirect.LzbDirectBiz;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.enume.ProviderTerminalBindLevel;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.ProviderTerminalMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.callback.req.LuzhouNetInCallBack;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalAcquirerMerchantInfo;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalAddRequest;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.job.service.ProviderTerminalSerivce;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.luzhou.LuZhouAddTerminalReq;
import com.wosai.upay.merchant.contract.model.luzhou.LuZhouAddTerminalRes;
import com.wosai.upay.merchant.contract.model.luzhou.callback.req.LuZhouQueryChannelMerchantReq;
import com.wosai.upay.merchant.contract.model.provider.LuZhouParam;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.service.LuZhouService;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wosai.upay.job.constant.CallBackConstants.CONTRACT_CALLBACK_MSG;

@Slf4j
@Component(ProviderUtil.LZB_CHANNEL)
public class LzbProvider extends AbstractProvider {

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Lazy
    @Autowired
    LzbDirectBiz lzbDirectBiz;

    @Autowired
    private LuZhouService luZhouService;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;

    @Autowired
    private ProviderTradeParamsService providerTradeParamsService;

    @Autowired
    private ProviderTerminalSerivce providerTerminalSerivce;
    @Autowired
    private StringRedisTemplate redisTemplate;

    private LuZhouParam luZhouParam;

    @PostConstruct
    public void init() {
        this.luZhouParam = contractParamsBiz.buildContractParams("lzb", LuZhouParam.class);
    }

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        int influPtask = AcquirerTypeEnum.LZB.getValue().equals(acquirer) || isSubBiz(merchantSn, AcquirerTypeEnum.LZB.getValue()) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.LZB_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);
        Integer taskType = null;
        if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
            //银行账户变更
            if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
                Map requestParam = (Map) paramContext.get("cardRequestParam");
                if (!CollectionUtils.isEmpty(requestParam)) {
                    //银行卡管理服务发起的变更(merchant_bank_account_pre)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
                } else {
                    //dts订阅直接变更(merchant_bank_account)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                }
            } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
                //更新基本信息
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
            } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {
                String crmUpdate = BeanUtil.getPropString(paramContext, "crmUpdate");
                if (!org.springframework.util.StringUtils.isEmpty(crmUpdate)) {
                    switch (crmUpdate) {
                        case "0":
                            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
                            break;
                        case "1":
                            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                            break;
                        case "2":
                            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH;
                            break;
                    }
                }
            } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
                //更新营业执照
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
            } else if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()) {
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE;
            }
            if (taskType == null) {
                return null;
            }
        }
        return subTask.setTask_type(taskType);
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask contractSubTask) {
        ContractResponse contractResponse;
        LuZhouParam luZhouParam = super.buildParam(contractChannel, contractSubTask, LuZhouParam.class);
        Integer payWay = contractSubTask.getPayway();
        String merchantSn = contractTask.getMerchant_sn();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
            if (Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue())) {
                contractResponse = luZhouService.contractMerchant(luZhouParam.getChannelNo(), contextParam);
                if (contractResponse.isSuccess()) {
                    final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
                    // 已提交待银行审核
                    lzbDirectBiz.recordViewProcess(apply, BankDirectApplyViewStatusEnum.DISTRIBUTING.getValue(), new Date());
                    //对于reOpen的情况.避免同一个收钱吧商户在泸州银行创建了多个商户号.这里保存下取得的商户号以备重新进件时使用
                    String acquirerMerchantId = BeanUtil.getPropString(contractResponse, "tradeParam.contractId");
                    MerchantAcquirerInfoDO merchantAcquirerInfoDO = new MerchantAcquirerInfoDO();
                    merchantAcquirerInfoDO.setMerchantSn(merchantSn);
                    merchantAcquirerInfoDO.setAcquirer(AcquirerTypeEnum.LZB.getValue());
                    merchantAcquirerInfoDO.setAcquirerMerchantId(acquirerMerchantId);
                    merchantAcquirerInfoDAO.insertOrUpdateOne(merchantAcquirerInfoDO);
                }
            } else {
                // 其他支付类型：微信，支付宝、云闪付
                contractResponse = luZhouService.contractMerchantOtherPayWay(merchantSn, payWay, luZhouParam.getChannel_no());
            }
            return contractResponse;
        }
        return null;
    }

    @Override
    public ContractResponse addTerminal(ContractTask task, ContractSubTask subTask) {
        String merchantSn = subTask.getMerchant_sn();
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LZB.getValue());
        if (Objects.isNull(acquirerParams)) {
            log.error("商户未进件成功,终端绑定失败 {}", merchantSn);
            return ContractResponse.builder().code(460).message("商户未进件成功,终端绑定失败").build();
        }
        //不存在则绑定
        ContractResponse response = this.addTerminalToLuzhou(merchantSn, acquirerParams.getOut_merchant_sn());
        if (response.isSuccess()) {
            Map responseParam = response.getResponseParam();
            LuZhouAddTerminalRes luZhouAddTerminalRes = JSON.parseObject(JSON.toJSONString(responseParam), LuZhouAddTerminalRes.class);
            if (luZhouAddTerminalRes.isSuccess()) {
                String providerTerminalId = luZhouAddTerminalRes.getData().getDeviceNo();
                providerTerminalSerivce.addProviderTerminal(new ProviderTerminalAddRequest()
                        .setMerchantSn(merchantSn)
                        .setBindLevel(ProviderTerminalBindLevel.MERCHANT)
                        .setProviderTerminalId(providerTerminalId)
                        .setAcquirerMerchantInfo(new ProviderTerminalAcquirerMerchantInfo()
                                .setMerchantSn(acquirerParams.getMerchant_sn())
                                .setAcquirerMerchantId(acquirerParams.getPay_merchant_id())
                                .setProvider(ProviderEnum.PROVIDER_LZB)));
            }
        } else {
            log.error("商户终端绑定失败,商户号:{},失败原因:{}", merchantSn, response.getMessage());
        }
        return response;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        Integer taskType = sub.getTask_type();
        if (ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(taskType)) {
            // 如果商户信息更新成功要更新 merchant_acquirer_info中的数据
            return luZhouService.updateMerchantAllInfo(contextParam, contractChannel.getChannel());
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(taskType)) {
            return luZhouService.updateMerchantBankAccount(contextParam, contractChannel.getChannel());
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(taskType)) {
            return luZhouService.updateMerchantFeeRate(contextParam, contractChannel.getChannel());
        }
        return null;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        LuZhouParam luzhouParam = contractParamsBiz.buildContractParamsByParams(merchantProviderParams, LuZhouParam.class);
        return luZhouService.wechatSubDevConfig(weixinConfig, luzhouParam);
    }

    @Override
    protected ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        LuZhouQueryChannelMerchantReq request = LuZhouQueryChannelMerchantReq.buildReq(contractSubTask.getContract_id());
        request.setChannelNo(luZhouParam.getChannel_no());
        return luZhouService.queryMerchantStatus(request);
    }

    @Override
    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        return this.doHandleContractStatusAndUpdateTaskResult(contractSubTask, response);
    }

    public HandleQueryStatusResp doHandleContractStatusAndUpdateTaskResult(ContractSubTask contractSubTask, ContractResponse response) {
        //失败 修改状态
        if (response.isBusinessFail()) {
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage(response.getMessage());
        } else if (response.isSystemFail()) {
            //可重试异常 不做任何处理
            return new HandleQueryStatusResp()
                    .setRetry(true)
                    .setMessage(response.getMessage());
        } else {
            Map<String, Object> callbackMsg = response.getResponseParam();
            String originResult = contractSubTask.getResult();
            this.updateSubAndParentTaskResult(contractSubTask.getId(), originResult, "泸州银行进件成功");

            return getHandleQueryStatusResp(contractSubTask, callbackMsg);
        }
    }

    private HandleQueryStatusResp getHandleQueryStatusResp(ContractSubTask contractSubTask, Map<String, Object> callbackMsg) {
        Map<String, Object> resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
        ArrayList<Map> callBack = Lists.newArrayList(callbackMsg);
        if (resp == null) {
            resp = new HashMap();
        }
        resp.put(CONTRACT_CALLBACK_MSG, callBack);
        contractSubTask.setResponse_body(JSONObject.toJSONString(resp));
        return new HandleQueryStatusResp().setSuccess(true)
                .setMessage("泸州银行进件成功");
    }

    public HandleQueryStatusResp doHandleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        //失败 修改状态
        if (response.isBusinessFail()) {
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage(response.getMessage());
        } else if (response.isSystemFail()) {
            //可重试异常 不做任何处理
            return new HandleQueryStatusResp()
                    .setRetry(true)
                    .setMessage(response.getMessage());
        } else {
            Map<String, Object> callbackMsg = response.getResponseParam();
            return getHandleQueryStatusResp(contractSubTask, callbackMsg);
        }
    }

    private void updateSubAndParentTaskResult(Long id, String originResult, String targetResult) {
        if (Objects.equals(originResult, targetResult)) {
            return;
        }
        ContractSubTask subTask = new ContractSubTask();
        subTask.setId(id);
        subTask.setResult(targetResult);
        contractSubTaskMapper.updateByPrimaryKey(subTask);
        //为了保持contractTask同步;
        final ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(id);
        final Long pTaskId = contractSubTask.getP_task_id();
        final ContractTask task = contractTaskMapper.selectByPrimaryKey(pTaskId);
        if (Objects.equals(task.getResult(), targetResult)) {
            return;
        }
        //只有影响主任务的时候才会去更新主任务的中的result
        if (Objects.equals(contractSubTask.getStatus_influ_p_task(), 1)) {
            final ContractTask contractTask = new ContractTask();
            contractTask.setId(pTaskId);
            contractTask.setResult(targetResult);
            contractTask.setPriority(task.getPriority());
            contractTaskMapper.updateByPrimaryKey(contractTask);
        }
    }

    private ContractResponse addTerminalToLuzhou(String merchantSn, String outMerchantSn) {
        LuZhouAddTerminalReq request = new LuZhouAddTerminalReq();
        Optional<MerchantAcquirerInfoDO> byMerchantSnAndAcquirer = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(merchantSn, AcquirerTypeEnum.LZB.getValue());
        if (!byMerchantSnAndAcquirer.isPresent()) {
            log.error("泸州银行商户信息不存在,商户号:{}", merchantSn);
        }
        MerchantAcquirerInfoDO merchantAcquirerInfoDO = byMerchantSnAndAcquirer.get();
        LuzhouNetInCallBack luzhouNetInCallBack = JSON.parseObject(merchantAcquirerInfoDO.getMerchantInfo(), LuzhouNetInCallBack.class);
        request.setMerchantNo(outMerchantSn);
        request.setChannelNo(luZhouParam.getChannelNo());
        request.setStoreNo(luzhouNetInCallBack.getStoreNo());
        request.setDeviceSn(merchantSn);
        return luZhouService.bindTerminal(merchantSn, request);
    }

    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO addTermInfoDTO, int payWay, String terminalSn) {
        ContractResponse response;
        String redisKey = addTermInfoDTO.getDeviceId();
        if (redisTemplate.hasKey(redisKey)) {
            ThreadUtil.sleep(applicationApolloConfig.getHxBindTaskDelay());
        }
        response = this.addTerminalToLuzhou(addTermInfoDTO.getMerchantSn(), addTermInfoDTO.getProviderMerNo());
        if (!redisTemplate.hasKey(redisKey)) {
            //添加key
            redisTemplate.opsForValue().set(redisKey, redisKey, 5L, TimeUnit.SECONDS);
        }
        return response;
    }
}
