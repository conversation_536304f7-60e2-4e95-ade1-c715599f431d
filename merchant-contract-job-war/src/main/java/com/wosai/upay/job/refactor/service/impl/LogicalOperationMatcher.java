package com.wosai.upay.job.refactor.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.utils.json.JSON;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeaturePropertyBO;
import com.wosai.upay.job.refactor.model.enums.LogicalOperationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.function.BiFunction;

/**
 * 逻辑操作匹配器工具类
 * 负责处理商户特征与规则条件的逻辑匹配操作
 *
 * <AUTHOR>
 */
@Slf4j
public class LogicalOperationMatcher {

    /**
     * 初始化逻辑操作函数映射
     *
     * @return 逻辑操作函数映射
     */
    public static EnumMap<LogicalOperationTypeEnum, BiFunction<MerchantFeaturePropertyBO, String, Boolean>> initLogicalOperationFunctionMap() {
        EnumMap<LogicalOperationTypeEnum, BiFunction<MerchantFeaturePropertyBO, String, Boolean>> logicalOperationFunctionMap = Maps.newEnumMap(LogicalOperationTypeEnum.class);

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.EQUAL, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> StringUtils.equals(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_EQUAL, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> !StringUtils.equals(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.CONTAIN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            if (StringUtils.isBlank(getMerchantFeatureProperty(merchantFeaturePropertyBO))) {
                return false;
            }
            return StringUtils.contains(getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue),
                    getMerchantFeatureProperty(merchantFeaturePropertyBO));
        });

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_CONTAIN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            if (StringUtils.isBlank(getMerchantFeatureProperty(merchantFeaturePropertyBO))) {
                return true;
            }
            return !StringUtils.contains(getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue),
                    getMerchantFeatureProperty(merchantFeaturePropertyBO));
        });

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.BE_CONTAINED, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            if (StringUtils.isBlank(getMerchantFeatureProperty(merchantFeaturePropertyBO))) {
                return false;
            }
            return StringUtils.contains(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                    getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue));
        });

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_BE_CONTAINED, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            if (StringUtils.isBlank(getMerchantFeatureProperty(merchantFeaturePropertyBO))) {
                return true;
            }
            return !StringUtils.contains(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                    getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue));
        });

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.IN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> parseInputToList(ruleRequiredPropertyValue).contains(getMerchantFeatureProperty(merchantFeaturePropertyBO)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_IN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> !parseInputToList(ruleRequiredPropertyValue).contains(getMerchantFeatureProperty(merchantFeaturePropertyBO)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.START_WITH, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> StringUtils.startsWith(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_START_WITH, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> !StringUtils.startsWith(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue)));
                
        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.GREATER_THAN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            String merchantValue = getMerchantFeatureProperty(merchantFeaturePropertyBO);
            String ruleValue = getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue);
            
            if (StringUtils.isBlank(merchantValue) || StringUtils.isBlank(ruleValue)) {
                return false;
            }
            
            if (NumberUtils.isCreatable(merchantValue) && NumberUtils.isCreatable(ruleValue)) {
                try {
                    int merchantIntValue = Integer.parseInt(merchantValue);
                    int ruleIntValue = Integer.parseInt(ruleValue);
                    return merchantIntValue > ruleIntValue;
                } catch (NumberFormatException e) {
                    try {
                        double merchantDoubleValue = Double.parseDouble(merchantValue);
                        double ruleDoubleValue = Double.parseDouble(ruleValue);
                        return merchantDoubleValue > ruleDoubleValue;
                    } catch (NumberFormatException ex) {
                        log.warn("无法将值转换为数字进行大于比较: merchantValue={}, ruleValue={}", merchantValue, ruleValue);
                        return false;
                    }
                }
            }
            
            return false;
        });
        
        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.LESS_THAN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            String merchantValue = getMerchantFeatureProperty(merchantFeaturePropertyBO);
            String ruleValue = getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue);
            
            if (StringUtils.isBlank(merchantValue) || StringUtils.isBlank(ruleValue)) {
                return false;
            }
            
            if (NumberUtils.isCreatable(merchantValue) && NumberUtils.isCreatable(ruleValue)) {
                try {
                    int merchantIntValue = Integer.parseInt(merchantValue);
                    int ruleIntValue = Integer.parseInt(ruleValue);
                    return merchantIntValue < ruleIntValue;
                } catch (NumberFormatException e) {
                    try {
                        double merchantDoubleValue = Double.parseDouble(merchantValue);
                        double ruleDoubleValue = Double.parseDouble(ruleValue);
                        return merchantDoubleValue < ruleDoubleValue;
                    } catch (NumberFormatException ex) {
                        log.warn("无法将值转换为数字进行小于比较: merchantValue={}, ruleValue={}", merchantValue, ruleValue);
                        return false;
                    }
                }
            }
            
            return false;
        });

        return logicalOperationFunctionMap;
    }

    /**
     * 获取商户对象具体属性值
     *
     * @param merchantFeaturePropertyBO 商户特性及类型
     * @return 商户对象具体属性
     */
    public static String getMerchantFeatureProperty(MerchantFeaturePropertyBO merchantFeaturePropertyBO) {
        return getMerchantFeatureProperty(merchantFeaturePropertyBO.getMerchantFeatureBO(), merchantFeaturePropertyBO.getObjectPropertyType());
    }

    /**
     * 获取商户对象具体属性值
     *
     * @param merchantFeatureBO  商户特征
     * @param objectPropertyType 对象属性
     * @return 商户对象具体属性值
     */
    public static String getMerchantFeatureProperty(MerchantFeatureBO merchantFeatureBO, String objectPropertyType) {
        try {
            return BeanUtils.getProperty(merchantFeatureBO, objectPropertyType);
        } catch (IllegalAccessException e) {
            log.error("获取商户属性异常,访问、调用或修改未经允许的内容.商户属性:{}", objectPropertyType);
        } catch (InvocationTargetException e) {
            log.error("调用商户属性目标方法(getXxx)时出现异常,商户属性:{}", objectPropertyType);
        } catch (NoSuchMethodException e) {
            log.error("未找到对应的商户属性调用方法,商户属性:{}", objectPropertyType);
        }
        return StringUtils.EMPTY;
    }

    private static @Nullable String getRealRuleRequiredPropertyValue(String ruleRequiredPropertyValue) {
        return parseInputToList(ruleRequiredPropertyValue).stream().filter(Objects::nonNull).findFirst().orElse(null);
    }

    private static List<String> parseInputToList(String input) {
        if (StringUtils.isBlank(input)) {
            return Collections.emptyList();
        }
        if (isJsonStringArray(input)) {
            return JSON.parseArray(input, String.class);
        }
        return Lists.newArrayList(input);
    }

    private static boolean isJsonStringArray(String input) {
        return input.startsWith("[") && input.endsWith("]");
    }
}
