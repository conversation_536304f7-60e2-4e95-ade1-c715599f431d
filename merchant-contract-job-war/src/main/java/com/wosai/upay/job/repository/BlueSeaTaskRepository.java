package com.wosai.upay.job.repository;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;


/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/8/31 5:58 下午
 */
@Component
public class BlueSeaTaskRepository {

    @Autowired
    private BlueSeaTaskMapper blueSeaTaskMapper;
    @Autowired
    private BlueSeaBiz blueSeaBiz;

    /**
     * 新增task
     * @param task
     */
    public void insertSelectiveAndStoreSn(BlueSeaTask task) {
        if(StringUtils.isEmpty(task.getStore_sn())) {
            String storeSn = BeanUtil.getPropString(blueSeaBiz.getStoreList(task.getMerchant_id()).get(0), Store.SN);
            task.setStore_sn(storeSn);
        }
        blueSeaTaskMapper.insertSelective(task);
    }


}
