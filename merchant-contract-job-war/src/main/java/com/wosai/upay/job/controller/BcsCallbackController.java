package com.wosai.upay.job.controller;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.validation.Valid;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.wosai.upay.job.model.callback.req.BcsCallbackReq;
import com.wosai.upay.job.model.callback.res.BcsCallBackResponse;
import com.wosai.upay.job.service.BcsCallbackService;
import com.wosai.upay.job.util.bcs.Hex;
import com.wosai.upay.job.util.bcs.Sm2Util;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/contract/bcs")
public class BcsCallbackController {

    @Value("${bcs_sm2_public_key}")
    private String sm2PublicKey;

    @Autowired
    private BcsCallbackService bcsCallbackService;

    /**
     * 进件回调接口
     * 
     * @param content 回调报文
     * @return BcsCallBackResponse
     */
    @PostMapping(value = "/callback", produces = "application/json")
    public BcsCallBackResponse contractCallback(@Valid @RequestBody String content,
        @RequestHeader Map<String, String> headers) {
        log.info("BcsCallbackController.contractCallback: content={}, headers={}", content, headers);
        String signature = headers.get("x-aob-signature");
        Map<String, String> contentMap = JSONObject.parseObject(content, new TypeReference<Map<String, String>>() {});
        String bizContent = contentMap.get("bizContent");
        // 生成加签body体
        String signatureContent = genSignatureContent(bizContent, headers);
        // 进件回调接口，将对应的进件子任务设置成完成状态，进件子任务的下游任务设置为可调度
        if (!Sm2Util.verifySign(Hex.hexStringToBytes(sm2PublicKey), signatureContent.getBytes(StandardCharsets.UTF_8),
            Hex.hexStringToBytes(signature))) {
            // 签名验证失败
            log.error("BcsCallbackController.contractCallback: verifySignSm2 error, content={}, signature={}", content,
                signature);
            return BcsCallBackResponse.fail(400, "Signature verification failed");
        }
        BcsCallbackReq bcsCallbackReq = JSONObject.parseObject(bizContent, BcsCallbackReq.class);
        return bcsCallbackService.contractCallback(bcsCallbackReq);
    }

    private String genSignatureContent(String content, Map<String, String> headers) {
        if (MapUtils.isEmpty(headers)) {
            log.error("genSignatureContent: headers is empty");
            return content;
        }
        String appId = headers.get("x-aob-appID");
        String bankId = headers.get("x-aob-bankID");
        Map<String, String> signature = new LinkedHashMap<>();
        signature.put("x-aob-appID", appId);
        signature.put("x-aob-bankID", bankId);
        signature.put("bizContent", content);
        return JSONObject.toJSONString(signature);
    }
}
