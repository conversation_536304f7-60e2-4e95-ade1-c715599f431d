package com.wosai.upay.job.refactor.service.rpc.coreb.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * core-b交易扩展配置删除请求
 *
 * <AUTHOR>
 * @date 2024/10/8 14:00
 */
public class CoreBTradeExtConfigRemoveRequest{

    public static final Integer SN_TYPE_MERCHANT = 0;
    public static final Integer SN_TYPE_STORE = 1;
    public static final Integer SN_TYPE_TERMINAL = 2;
    public static final Integer SN_TYPE_PROVIDER_MCH = 3;
    public static final Integer SN_TYPE_STORE_SUB_MCH = 4;

    @Setter
    @Getter
    @JsonProperty("merchant_sn")
    private String merchantSn;

    @JsonProperty("sn_types")
    private List<Integer> snTypes;

    @Setter
    @Getter
    private Integer provider;

    @Setter
    @Getter
    @JsonProperty("provider_mch_id")
    private String providerMchId;

    public List<Integer> getSnTypes() {
        return Objects.isNull(snTypes) ? new ArrayList<>() : new ArrayList<>(snTypes);
    }

    public void setSnTypes(List<Integer> snTypes) {
        this.snTypes = Objects.isNull(snTypes) ? new ArrayList<>() : new ArrayList<>(snTypes);
    }

}
