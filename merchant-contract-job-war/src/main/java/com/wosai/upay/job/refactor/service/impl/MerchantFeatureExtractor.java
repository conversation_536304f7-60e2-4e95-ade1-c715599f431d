package com.wosai.upay.job.refactor.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.IdentificationTypeEnum;
import com.shouqianba.cua.enums.core.MerchantTypeEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.utils.json.JSON;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.shouqianba.cua.utils.thread.ThreadPoolWorker;
import com.wosai.assistant.response.UserBean;
import com.wosai.assistant.service.UserRpcService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.job.enume.LegalPersonTypeEnum;
import com.wosai.upay.job.enume.MerchantSettlementAccountTypeEnum;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandDetailInfoQueryResp;
import com.wosai.upay.job.externalservice.brand.model.BrandPaymentModeQueryResp;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.enums.NetInSceneEnum;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 商户特征提取器
 * 负责从各种数据源提取和构建商户特征信息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MerchantFeatureExtractor {

    private final MerchantService merchantService;
    private final IMerchantService iMerchantService;
    private final OrganizationService organizationService;
    private final MerchantBusinessLicenseService mcMerchantBusinessLicenseService;
    private final MerchantBankService merchantBankService;
    private final DistrictsServiceV2 districtsServiceV2;
    private final UserRpcService userRpcService;
    private final CommonAppInfoService commonAppInfoService;
    private final BrandBusinessClient brandBusinessClient;
    private final ContractStatusMapper contractStatusMapper;
    private final MerchantBasicInfoBiz merchantBasicInfoBiz;
    private StoreService storeService;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    public MerchantFeatureExtractor(
            MerchantService merchantService,
            IMerchantService iMerchantService,
            OrganizationService organizationService,
            MerchantBusinessLicenseService mcMerchantBusinessLicenseService,
            MerchantBankService merchantBankService,
            DistrictsServiceV2 districtsServiceV2,
            UserRpcService userRpcService,
            CommonAppInfoService commonAppInfoService,
            BrandBusinessClient brandBusinessClient,
            ContractStatusMapper contractStatusMapper,
            StoreService storeService,
            MerchantBasicInfoBiz merchantBasicInfoBiz) {
        this.storeService = storeService;
        this.merchantBasicInfoBiz = merchantBasicInfoBiz;
        this.merchantService = merchantService;
        this.iMerchantService = iMerchantService;
        this.organizationService = organizationService;
        this.mcMerchantBusinessLicenseService = mcMerchantBusinessLicenseService;
        this.merchantBankService = merchantBankService;
        this.districtsServiceV2 = districtsServiceV2;
        this.userRpcService = userRpcService;
        this.commonAppInfoService = commonAppInfoService;
        this.brandBusinessClient = brandBusinessClient;
        this.contractStatusMapper = contractStatusMapper;
    }

    public MerchantFeatureBO getMerchantFeatureByStoreId(String merchantSn, String storeId, NetInSceneEnum netInScene) {
        MerchantFeatureBO merchantFeature = getMerchantFeature(merchantSn, null, netInScene);
        merchantFeature.setStoreProvinces(null);
        merchantFeature.setStoreCities(null);
        try {
            StoreInfo storeInfo = storeService.getStoreById(storeId, null);
            if (Objects.isNull(storeInfo)) {
                return merchantFeature;
            }
            String provinceName = storeInfo.getProvince();
            String cityName = storeInfo.getCity();
            if (StringUtils.isNotBlank(provinceName)) {
                District provinceDistrict = districtsServiceV2.getCodeByName(provinceName.trim());
                if (provinceDistrict != null && StringUtils.isNotBlank(provinceDistrict.getProvince_code())) {
                    merchantFeature.setStoreProvinces(provinceDistrict.getProvince_code());
                }
            }
            if (StringUtils.isNotBlank(provinceName) && StringUtils.isNotBlank(cityName)) {
                String fullCityName = provinceName.trim() + " " + cityName.trim();
                District cityDistrict = districtsServiceV2.getCodeByName(fullCityName);
                if (cityDistrict != null && StringUtils.isNotBlank(cityDistrict.getCity_code())) {
                    merchantFeature.setStoreCities(cityDistrict.getCity_code());
                }
            }
            return merchantFeature;
        } catch (Exception e) {
            log.error("getMerchantFeatureByStoreId error, merchantSn:{}, StoreId:{}", merchantSn, storeId, e);
            return merchantFeature;
        }
    }

    /**
     * 根据商户号和组织ID获取商户特征
     *
     * @param merchantSn     商户号
     * @param organizationId 组织ID
     * @param netInScene     入网场景
     * @return 商户特征信息
     */
    public MerchantFeatureBO getMerchantFeature(String merchantSn, String organizationId, NetInSceneEnum netInScene) {
        MerchantInfo merchantBasicInfo = getMerchantBasicInfo(merchantSn);
        if (Objects.isNull(merchantBasicInfo)) {
            throw new CommonPubBizException("商户不存在, merchantSn = {}" + merchantSn);
        }
        HashMap<String, String> distinctRequestMap = Maps.newHashMap();
        AtomicReference<String> promotionOrganizationPathReference = new AtomicReference<>();
        AtomicReference<String> organizationPathReference = new AtomicReference<>();
        AtomicReference<MerchantBusinessLicenseInfo> licenseInfoAtomicReference = new AtomicReference<>();
        AtomicReference<Map<String, ?>> accountInfoReference = new AtomicReference<>();
        AtomicReference<District> districtAtomicReference = new AtomicReference<>();
        ThreadPoolWorker<Object> poolWorker = ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance());
        poolWorker
                .addWork(() ->  promotionOrganizationPathReference.set(getPromotionOrganizationPath(merchantBasicInfo, organizationId)))
                .addWork(() -> organizationPathReference.set(getMerchantOrganizationPath(merchantBasicInfo)))
                .addWork(() -> licenseInfoAtomicReference.set(mcMerchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantBasicInfo.getId(), devCode)))
                .addWork(() -> accountInfoReference.set(getBankAccount(merchantBasicInfo.getId(), merchantSn)));
        if (StringUtils.isNotBlank(merchantBasicInfo.getDistrict_code())) {
            distinctRequestMap.put("code", merchantBasicInfo.getDistrict_code());
            poolWorker.addWork(() -> districtAtomicReference.set(districtsServiceV2.getDistrict(distinctRequestMap)));
        }
        poolWorker.doWorks();
        return buildMerchantFeature(merchantBasicInfo, licenseInfoAtomicReference, organizationPathReference,
                promotionOrganizationPathReference, accountInfoReference, districtAtomicReference, netInScene);
    }

    private MerchantInfo getMerchantBasicInfo(String merchantSn) {
        return merchantService.getMerchantBySn(merchantSn, devCode);
    }

    private String getMerchantOrganizationPath(MerchantInfo merchantBasicInfo) {
        String organizationId = BeanUtil.getPropString(iMerchantService.getMerchantBySn(merchantBasicInfo.getSn()), "organization_id");
        if (StringUtils.isBlank(organizationId)) {
            return StringUtils.EMPTY;
        }
        return MapUtils.getString(organizationService.getOrganization(organizationId), "path");
    }

    private String getPromotionOrganizationPath(MerchantInfo merchantBasicInfo, String organizationId) {
        if (StringUtils.isNotBlank(organizationId)) {
            return MapUtils.getString(organizationService.getOrganization(organizationId), "path");
        }
        Map<String, UserBean> userMap = userRpcService.getUserByMerchantIds(Lists.newArrayList(merchantBasicInfo.getId()));
        if (MapUtils.isEmpty(userMap) || Objects.isNull(userMap.get(merchantBasicInfo.getId()))) {
            return StringUtils.EMPTY;
        }
        String organizationCodes = userMap.get(merchantBasicInfo.getId()).getOrganizationCodes();
        if (StringUtils.isBlank(organizationCodes)) {
            return StringUtils.EMPTY;
        }
        return organizationCodes.replaceAll("/", ",");
    }

    private Map<String, ?> getBankAccount(String merchantId, String merchantSn) {
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", merchantId, "default_status", ValidStatusEnum.VALID.getValue())
        );
        if (CollectionUtils.isEmpty(listResult.getRecords())) {
            throw new CommonPubBizException("获取商户卡信息为空merchantSn:" + merchantSn + "merchantId:" + merchantId);
        }
        return listResult.getRecords().get(0);
    }

    private MerchantFeatureBO buildMerchantFeature(MerchantInfo merchantBasicInfo,
                                                   AtomicReference<MerchantBusinessLicenseInfo> licenseInfoAtomicReference,
                                                   AtomicReference<String> organizationPathReference,
                                                   AtomicReference<String> promotionOrganizationReference,
                                                   AtomicReference<Map<String, ?>> accountInfoReference,
                                                   AtomicReference<District> districtAtomicReference, NetInSceneEnum netInScene) {
        MerchantFeatureBO merchantFeatureBO = new MerchantFeatureBO();
        MerchantBusinessLicenseInfo licenseInfo = licenseInfoAtomicReference.get();
        Map<String, ?> accountInfoMap = accountInfoReference.get();
        District district = districtAtomicReference.get();
        
        populateBasicMerchantInfo(merchantFeatureBO, merchantBasicInfo, district);
        populateOrganizationInfo(merchantFeatureBO, organizationPathReference.get(), promotionOrganizationReference.get());
        populateAccountInfo(merchantFeatureBO, accountInfoMap, licenseInfo);
        populateLicenseInfo(merchantFeatureBO, licenseInfo, accountInfoMap);
        populateAgeCalculations(merchantFeatureBO, licenseInfo, accountInfoMap);
        populateBusinessInfo(merchantFeatureBO, merchantBasicInfo);
        populateAdditionalInfo(merchantFeatureBO, netInScene);
        populateStoreInfo(merchantFeatureBO, merchantBasicInfo);
        populateBrandInfo(merchantFeatureBO);
        
        return merchantFeatureBO;
    }

    private void populateBasicMerchantInfo(MerchantFeatureBO merchantFeatureBO, MerchantInfo merchantBasicInfo, District district) {
        merchantFeatureBO.setMerchantSn(merchantBasicInfo.getSn());
        merchantFeatureBO.setName(merchantBasicInfo.getName());
        merchantFeatureBO.setDistrictCode(merchantBasicInfo.getDistrict_code());
        merchantFeatureBO.setProvinceName(merchantBasicInfo.getProvince());
        merchantFeatureBO.setCityName(merchantBasicInfo.getCity());
        merchantFeatureBO.setIndustry(merchantBasicInfo.getIndustry());
        merchantFeatureBO.setType(getMerchantType(null));
        
        if (Objects.nonNull(district)) {
            merchantFeatureBO.setProvinceCode(district.getProvince_code());
            merchantFeatureBO.setCityCode(district.getCity_code());
        }
    }

    private void populateOrganizationInfo(MerchantFeatureBO merchantFeatureBO, String organizationPath, String promotionOrganizationPath) {
        merchantFeatureBO.setOrganizationPath(organizationPath);
        merchantFeatureBO.setPromotionOrganizationPath(promotionOrganizationPath);
    }

    private void populateAccountInfo(MerchantFeatureBO merchantFeatureBO, Map<String, ?> accountInfoMap, MerchantBusinessLicenseInfo licenseInfo) {
        MerchantFeatureBO.ExtraFeature extraFeature = new MerchantFeatureBO.ExtraFeature();
        MerchantFeatureBO.ExtraFeature.AccountInfo accountInfo = new MerchantFeatureBO.ExtraFeature.AccountInfo();
        merchantFeatureBO.setPersonalCertificateType(MapUtils.isEmpty(accountInfoMap) ? null : BeanUtil.getPropString(accountInfoMap, MerchantBankAccount.ID_TYPE));
        accountInfo.setAccountType(MapUtils.isEmpty(accountInfoMap) ? null : MapUtils.getInteger(accountInfoMap, MerchantBankAccount.TYPE));
        accountInfo.setIdentityId(MapUtils.isEmpty(accountInfoMap) ? null : BeanUtil.getPropString(accountInfoMap, MerchantBankAccount.IDENTITY));
        accountInfo.setHolderName(MapUtils.isEmpty(accountInfoMap) ? null : BeanUtil.getPropString(accountInfoMap, MerchantBankAccount.HOLDER));
        extraFeature.setAccountInfo(accountInfo);
        extraFeature.setMerchantBusinessLicenseInfo(licenseInfo);
        merchantFeatureBO.setExtraFeature(extraFeature);
        
        EnumUtils.ofNullable(BankAccountTypeEnum.class, accountInfo.getAccountType())
                .ifPresent(t -> merchantFeatureBO.setBankAccountType(t.getValue().toString()));
        populateLegalPersonAndSettlementAccountType(merchantFeatureBO);
    }

    private void populateLicenseInfo(MerchantFeatureBO merchantFeatureBO, MerchantBusinessLicenseInfo licenseInfo, Map<String, ?> accountInfoMap) {
        if (Objects.nonNull(licenseInfo) && Objects.nonNull(licenseInfo.getLegal_person_id_type())) {
            merchantFeatureBO.setLegalPersonCertificateType(licenseInfo.getLegal_person_id_type());
        }
    }

    private void populateAgeCalculations(MerchantFeatureBO merchantFeatureBO, MerchantBusinessLicenseInfo licenseInfo, Map<String, ?> accountInfoMap) {
        if (Objects.nonNull(licenseInfo) && Objects.equals(licenseInfo.getLegal_person_id_type(), IdentificationTypeEnum.PRC_ID_CARD.getValue()) && StringUtils.isNotBlank(licenseInfo.getLegal_person_id_number())) {
            merchantFeatureBO.setLegalIdCardAge(calculateAgeFromIdCard(licenseInfo.getLegal_person_id_number()));
        }

        String settlementIdNumber = MapUtils.getString(accountInfoMap, MerchantBankAccount.IDENTITY);
        if (Objects.nonNull(accountInfoMap) && Objects.equals(MapUtils.getInteger(accountInfoMap, MerchantBankAccount.ID_TYPE), IdentificationTypeEnum.PRC_ID_CARD.getValue()) && StringUtils.isNotBlank(settlementIdNumber)) {
            merchantFeatureBO.setSettlementIdCardAge(calculateAgeFromIdCard(settlementIdNumber));
        }
    }

    private void populateBusinessInfo(MerchantFeatureBO merchantFeatureBO, MerchantInfo merchantBasicInfo) {
        merchantFeatureBO.setBusinessName(merchantBasicInfo.getBusiness_name());
        merchantFeatureBO.setOpenedBusinessAppIdListJson(getOpenedBusinessAppIdListJson(merchantBasicInfo.getId()));
        merchantFeatureBO.setType(getMerchantType(merchantFeatureBO.getExtraFeature().getMerchantBusinessLicenseInfo()));
    }

    private void populateStoreInfo(MerchantFeatureBO merchantFeatureBO, MerchantInfo merchantBasicInfo) {
        populateStoreProvinceAndCity(merchantFeatureBO, merchantBasicInfo);
    }

    private void populateAdditionalInfo(MerchantFeatureBO merchantFeatureBO, NetInSceneEnum netInScene) {
        merchantFeatureBO.setNetInScene(NetInSceneEnum.BUSINESS_OPENING.getValue());
        if (Objects.nonNull(netInScene)) {
            merchantFeatureBO.setNetInScene(netInScene.getValue());
        }
    }


    private void populateStoreProvinceAndCity(MerchantFeatureBO merchantFeatureBO, MerchantInfo merchantBasicInfo) {
        try {
            List<Map> stores = merchantBasicInfoBiz.listAllSqbStores(merchantBasicInfo.getSn());
            if (CollectionUtils.isEmpty(stores)) {
                log.warn("商户门店信息为空, merchantSn: {}", merchantBasicInfo.getSn());
                return;
            }

            Set<String> storeProvinceNames = new HashSet<>();
            Set<String> storeCityNames = new HashSet<>();
            Set<String> storeProvinceCodes = new HashSet<>();
            Set<String> storeCityCodes = new HashSet<>();

            for (Map store : stores) {
                try {
                    String province = MapUtils.getString(store, "province");
                    String city = MapUtils.getString(store, "city");
                    
                    if (StringUtils.isBlank(province) || StringUtils.isBlank(city)) {
                        log.warn("门店省市信息不完整, merchantSn: {}, province: {}, city: {}", 
                                merchantBasicInfo.getSn(), province, city);
                        continue;
                    }

                    storeProvinceNames.add(province.trim());
                    storeCityNames.add(province.trim() + " " + city.trim());
                } catch (Exception e) {
                    log.warn("处理单个门店信息异常, merchantSn: {}, store: {}", 
                            merchantBasicInfo.getSn(), store, e);
                }
            }

            // 处理省份编码
            for (String provinceName : storeProvinceNames) {
                try {
                    District district = districtsServiceV2.getCodeByName(provinceName);
                    if (district != null && StringUtils.isNotBlank(district.getProvince_code())) {
                        storeProvinceCodes.add(district.getProvince_code());
                    } else {
                        log.warn("省份编码查询失败, provinceName: {}", provinceName);
                    }
                } catch (Exception e) {
                    log.warn("查询省份编码异常, provinceName: {}", provinceName, e);
                }
            }

            // 处理城市编码
            for (String cityName : storeCityNames) {
                try {
                    District district = districtsServiceV2.getCodeByName(cityName);
                    if (district != null && StringUtils.isNotBlank(district.getCity_code())) {
                        storeCityCodes.add(district.getCity_code());
                    } else {
                        log.warn("城市编码查询失败, cityName: {}", cityName);
                    }
                } catch (Exception e) {
                    log.warn("查询城市编码异常, cityName: {}", cityName, e);
                }
            }

            if (!storeProvinceCodes.isEmpty()) {
                merchantFeatureBO.setStoreProvinces(String.join(",", storeProvinceCodes));
            }
            if (!storeCityCodes.isEmpty()) {
                merchantFeatureBO.setStoreCities(String.join(",", storeCityCodes));
            }

        } catch (Exception e) {
            log.error("填充门店省市信息失败, merchantSn: {}", merchantBasicInfo.getSn(), e);
        }
    }

    /**
     * 根据身份证计算年龄
     */
    private Integer calculateAgeFromIdCard(String idCardNumber) {
        if (StringUtils.isBlank(idCardNumber) || idCardNumber.length() < 14) {
            return null;
        }

        try {
            String birthDateStr;
            if (idCardNumber.length() == 15) {
                // 15位身份证
                birthDateStr = "19" + idCardNumber.substring(6, 12);
            } else if (idCardNumber.length() == 18) {
                // 18位身份证
                birthDateStr = idCardNumber.substring(6, 14);
            } else {
                return null;
            }

            int birthYear = Integer.parseInt(birthDateStr.substring(0, 4));
            int birthMonth = Integer.parseInt(birthDateStr.substring(4, 6));
            int birthDay = Integer.parseInt(birthDateStr.substring(6, 8));

            Calendar birth = Calendar.getInstance();
            birth.set(birthYear, birthMonth - 1, birthDay);

            Calendar now = Calendar.getInstance();
            int age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);

            // 如果还没过生日，年龄减1
            if (now.get(Calendar.DAY_OF_YEAR) < birth.get(Calendar.DAY_OF_YEAR)) {
                age--;
            }

            return age > 0 ? age : null;
        } catch (Exception e) {
            log.warn("计算身份证年龄失败, idCardNumber: {}", idCardNumber, e);
            return null;
        }
    }

    private void populateBrandInfo(MerchantFeatureBO merchantFeatureBO) {
        BrandPaymentModeQueryResp brandMerchantInfo = brandBusinessClient.getBrandPaymentModeForContract(merchantFeatureBO.getExtraFeature().getMerchantBusinessLicenseInfo().getMerchant_id());
        if (Objects.nonNull(brandMerchantInfo)) {
            BrandDetailInfoQueryResp brandDetailInfo = brandBusinessClient.getBrandDetailInfoByBrandId(brandMerchantInfo.getBrandId());
            // 如果入网商户和品牌主商户是同一个，则不需要设置品牌信息，要走商户模式入网流程
            if (Objects.equals(merchantFeatureBO.getMerchantSn(), brandDetailInfo.getMainMerchantSn())) {
                return;
            }
            String acquirer = contractStatusMapper.selectByMerchantSn(brandDetailInfo.getMainMerchantSn()).getAcquirer();
            merchantFeatureBO.setAcquirer(acquirer);
            merchantFeatureBO.setPaymentMode(brandMerchantInfo.getPaymentMode().getCode().toString());
        }
    }

    private String getOpenedBusinessAppIdListJson(String merchantId) {
        HashMap<Object, Object> requestMap = Maps.newHashMap();
        requestMap.put("merchant_id", merchantId);
        try {
            ListResult appInfos = commonAppInfoService.findAppInfos(new PageInfo(1, 10000), requestMap);
            if (Objects.isNull(appInfos)) {
                return StringUtils.EMPTY;
            }
            List<Map> records = appInfos.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return StringUtils.EMPTY;
            }
            return JSON.toJSONString(records.stream().map(t -> MapUtils.getString(t, "app_id")).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("获取商户开通的业务应用失败, merchantId:{}", merchantId, e);
            return StringUtils.EMPTY;
        }
    }

    private void populateLegalPersonAndSettlementAccountType(MerchantFeatureBO merchantFeatureBO) {
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.getExtraFeature();
        MerchantBusinessLicenseInfo licenseInfo = extraFeature.getMerchantBusinessLicenseInfo();
        MerchantFeatureBO.ExtraFeature.AccountInfo accountInfo = extraFeature.getAccountInfo();
        MerchantSettlementAccountTypeEnum settlementAccountType = MerchantSettlementAccountTypeEnum.OTHER;
        Integer accountType = accountInfo.getAccountType();
        if (Objects.isNull(licenseInfo) || Objects.isNull(accountType)) {
            log.warn("商户营业执照或者账户类型为空,商户号: {}", merchantFeatureBO.getMerchantSn());
            return;
        }
        boolean legalPersonAccount = StringUtils.isNotBlank(accountInfo.getIdentityId())
                && StringUtils.isNotBlank(licenseInfo.getLegal_person_id_number())
                && StringUtils.equals(accountInfo.getIdentityId(), licenseInfo.getLegal_person_id_number());
        merchantFeatureBO.setLegalPersonType(legalPersonAccount ? LegalPersonTypeEnum.LEGAL_PERSON.getValue().toString()
                : LegalPersonTypeEnum.NOT_LEGAL_PERSON.getValue().toString());
        if (BankAccountTypeEnum.isPersonal(accountType)) {
            settlementAccountType = legalPersonAccount ? MerchantSettlementAccountTypeEnum.LEGAL_PRIVATE
                    : MerchantSettlementAccountTypeEnum.NON_LEGAL_PRIVATE;
        } else if (BankAccountTypeEnum.isPublic(accountType)){
            settlementAccountType = StringUtils.equals(licenseInfo.getName(), accountInfo.getHolderName()) ? MerchantSettlementAccountTypeEnum.COMMON_PUBLIC
                    : MerchantSettlementAccountTypeEnum.OTHER_PUBLIC;
        }
        merchantFeatureBO.setSettlementAccountType(settlementAccountType.getValue().toString());
    }

    private String getMerchantType(MerchantBusinessLicenseInfo licenseInfo) {
        if (Objects.isNull(licenseInfo)) {
            return StringUtils.EMPTY;
        }
        Integer type = licenseInfo.getType();
        if (BusinessLicenseTypeEnum.isMicro(type)) {
            return MerchantTypeEnum.SMALL_MICRO_MERCHANT.getValue().toString();
        }
        if (BusinessLicenseTypeEnum.isIndividual(type)) {
            return MerchantTypeEnum.INDIVIDUAL_MERCHANT.getValue().toString();
        }
        if (BusinessLicenseTypeEnum.isEnterprise(type)) {
            return MerchantTypeEnum.COMPANY_MERCHANT.getValue().toString();
        }
        if (BusinessLicenseTypeEnum.isOrganization(type)) {
            return MerchantTypeEnum.ORGANIZATION_MERCHANT.getValue().toString();
        }
        return StringUtils.EMPTY;
    }
}
