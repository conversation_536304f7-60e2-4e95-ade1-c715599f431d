package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 结算通道表表实体对象
 *
 * <AUTHOR>
 */
@TableName("mc_provider")
@Data
public class McProviderDO {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 唯一标识
     */
    @TableField(value = "provider")
    private String provider;
    /**
     * 收单机构标识
     */
    @TableField(value = "acquirer")
    private String acquirer;

    @TableField(value = "name")
    private String name;

    /**
     * 处理对应业务的BeanName
     */
    @TableField(value = "bean_name")
    private String beanName;
    /**
     * 支持的支付源以及支付方式
     */
    @TableField(value = "metadata")
    private String metadata;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;


}

