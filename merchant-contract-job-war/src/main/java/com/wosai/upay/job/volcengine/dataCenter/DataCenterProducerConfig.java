package com.wosai.upay.job.volcengine.dataCenter;

import com.wosai.pantheon.util.MapUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.Map;

/**
 * 向数据中心写入消息配置
 */
@Slf4j
@Getter
@Configuration(value = "dataCenterProducerConfig")
public class DataCenterProducerConfig {
    @Value("${spring.kafka.bootstrap-servers-data-center}")
    private String brokerAddress;

    @Value("${topic.kafka.data-center}")
    private String topic;

    @Bean(name = "dataCenterKafkaTemplate")
    public KafkaTemplate<String, String> dataCenterProducer() {
        ProducerFactory<String, String> factory = dataCenterProducerFactory();
        log.info("producer topic:{} config:{}", topic, factory.getConfigurationProperties());
        return new KafkaTemplate<>(dataCenterProducerFactory());
    }

    @Bean(name = "dataCenterProducerFactory")
    public ProducerFactory<String, String> dataCenterProducerFactory() {
        Map<String, Object> props = MapUtil.hashMap(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, brokerAddress,
                ProducerConfig.ACKS_CONFIG, "1",
                ProducerConfig.BATCH_SIZE_CONFIG, "16384",
                ProducerConfig.LINGER_MS_CONFIG, "1000",
                ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, "30000",
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class,
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class
        );
        return new DefaultKafkaProducerFactory<>(props);
    }
}
