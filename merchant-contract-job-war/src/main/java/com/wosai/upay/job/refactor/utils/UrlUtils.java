package com.wosai.upay.job.refactor.utils;

import com.wosai.common.utils.WosaiStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.StringJoiner;

/**
 * url工具类
 *
 * <AUTHOR>
 * @date 2024/9/24 15:33
 */
public final class UrlUtils {


    public static String baseUrl(String urls) {
        if (StringUtils.isBlank(urls)) {
            return "";
        }
        String baseUrl = doBaseUrl(urls, ",");
        if (WosaiStringUtils.isEmpty(baseUrl)) {
            return baseUrl;
        }
        StringJoiner sj = new StringJoiner(",");
        for (String s : baseUrl.split(",")) {
            if (WosaiStringUtils.isNotEmpty(s)) {
                sj.add(s);
            }
        }
        return sj.toString();
    }

    private static String doBaseUrl(String sourceUrl, String split) {
        if (StringUtils.isEmpty(split)) {
            return sourceUrl;
        } else if (StringUtils.isNotEmpty(sourceUrl) && sourceUrl.contains("private-")) {
            String[] sourceUrls = sourceUrl.split("(?=((http|https)+?://[\\s\\S]+?)(,(?=http)|\"|}|])*?)".replace(",", split));

            for(int i = 0; i < sourceUrls.length; ++i) {
                sourceUrls[i] = baseEncryptUrl(sourceUrls[i]);
            }

            return StringUtils.join(sourceUrls, split);
        } else {
            return sourceUrl;
        }
    }

    private static String baseEncryptUrl(String encryptUrl) {
        if (StringUtils.isNotEmpty(encryptUrl) && encryptUrl.contains("private-")) {
            encryptUrl = encryptUrl.replaceAll("([&|?]((?i)expires|(?i)ossaccesskeyid|(?i)signature|(?i)security-token)=[^&|^?]*)", "");
            if (encryptUrl.contains("&") && !encryptUrl.contains("?")) {
                encryptUrl = encryptUrl.replaceFirst("&", "?");
            }
        }
        return encryptUrl;
    }
}
