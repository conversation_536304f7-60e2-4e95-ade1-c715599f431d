package com.wosai.upay.job.refactor.service.localcache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.wosai.upay.job.refactor.service.IndustryCodeV2Service;
import com.wosai.upay.job.refactor.model.dto.IndustryCodeV2DTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 行业映射本地缓存服务
 *
 * <AUTHOR>
 * @date 2023/11/10 15:46
 */
@Service
public class IndustryMappingLocalCacheService {

    private final Cache<String, IndustryCodeV2DTO> industryCache =
            Caffeine.newBuilder()
                    .initialCapacity(10)
                    .maximumSize(50000)
                    .expireAfterWrite(30, TimeUnit.MINUTES).build();

    @Resource
    private IndustryCodeV2Service industryCodeV2Service;


    /**
     * 根据行业id从缓存中获取行业映射信息
     *
     * @param industryId 内部行业id
     * @return 行业映射信息
     */
    public Optional<IndustryCodeV2DTO> getIndustryMappingByIndustryId(String industryId) {
        if (StringUtils.isBlank(industryId)) {
            return Optional.empty();
        }
        return Optional.ofNullable(industryCache.get(industryId, s -> industryCodeV2Service.getIndustryCodeV2ByIndustryId(s).orElse(null)));
    }

    /**
     * 清理行业映射缓存
     */
    public void cleanUpIndustryMappingCache() {
        industryCache.cleanUp();
    }
}
