package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 逻辑操作类型枚举
 *
 * <AUTHOR>
 */
public enum LogicalOperationTypeEnum implements ITextValueEnum<String> {
    /**
     * 等于
     */
    EQUAL("EQUAL", "等于"),
    /**
     * 不等于
     */
    NOT_EQUAL("NOT_EQUAL", "不等于"),
    /**
     * 包含
     */
    CONTAIN("CONTAIN", "包含"),
    /**
     * 不包含
     */
    NOT_CONTAIN("NOT_CONTAIN", "不包含"),
    /**
     * 被包含
     */
    BE_CONTAINED("BE_CONTAINED", "被包含"),
    /**
     * 被不包含
     */
    NOT_BE_CONTAINED("NOT_BE_CONTAINED", "被不包含"),
    /**
     * in
     */
    IN("IN", "in"),
    /**
     * not in
     */
    NOT_IN("NOT_IN", "not in"),
    /**
     * start with
     */
    START_WITH("START_WITH", "以..起始"),
    /**
     * not start with
     */
    NOT_START_WITH("NOT_START_WITH", "不以..起始"),
    /**
     * 大于
     */
    GREATER_THAN("GREATER_THAN", "大于"),
    /**
     * 小于
     */
    LESS_THAN("LESS_THAN", "小于");

    private final String value;
    private final String text;

    LogicalOperationTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
