package com.wosai.upay.job.providers;

import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component(ProviderUtil.PSBC_SX_CHANNEL)
public class PsbcsxProvider extends AbstractProvider {
    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        return null;
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        return new ContractResponse().setCode(460).setMessage("不支持此业务");
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        return new ContractResponse().setCode(460).setMessage("不支持此业务");
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        throw new ContractBizException("不支持此业务");
    }
}
