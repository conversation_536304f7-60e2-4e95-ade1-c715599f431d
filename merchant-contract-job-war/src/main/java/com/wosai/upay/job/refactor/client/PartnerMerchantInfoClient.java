package com.wosai.upay.job.refactor.client;

import com.wosai.upay.job.service.rpc.QldMerchantInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * partner-merchant-info-client
 *
 * <AUTHOR>
 * @date 2025/7/7 11:47
 */
@Component
@Slf4j
public class PartnerMerchantInfoClient {

    @Resource
    private QldMerchantInfoService qldMerchantInfoService;

    /**
     * 是否全来店商户
     *
     * @param merchantSn 商户sn
     * @return true 如果是全来店商户信息
     */
    public boolean isQldMerchant(String merchantSn) {
        try {
            return qldMerchantInfoService.isQldMerchant(merchantSn);
        } catch (Exception e) {
            log.error("Error checking if merchant is QLD: {}", merchantSn, e);
            return false;
        }
    }


}
