package com.wosai.upay.job.scheduler;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.bank.model.enume.AccountApplyStatus;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.QueryLimit;
import com.wosai.upay.job.model.QueryTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: jerry
 * @date: 2019/4/23 16:44
 * @Description:定时任务公共配置(阿波罗配置相关的操作)
 */
@Component
@Slf4j
public class ScheduleUtil {

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    /**
     * 报备路由
     */
    public static final String ROUTE_CONF_APPLO_KEY = "route_conf";
    /**
     * 查询数量
     */
    public static final String LIMIT_APPLO_KEY = "query_limit";
    /**
     * 查询时间
     */
    public static final String QUERY_APPLO_KEY = "query_time";

    public static Long DEFAULT_THREE_MINUTES_MILLIS_QUERY = 3 * 60 * 1000L;
    public static Long DEFAULT_FIVE_MINUTES_MILLIS_QUERY = 5 * 60 * 1000L;

    public static Long DEFAULT_THREE_HOURS_MILLIS_QUERY = 3 * 60 * 60 * 1000L;

    public static Long DEFAULT_TWO_DAYS_MILLIS_QUERY = 2 * 24 * 60 * 60 * 1000L;

    public static Long DEFAULT_FIVE_DAYS_MILLIS_QUERY = 5 * 24 * 60 * 60 * 1000L;

    public static Long DEFAULT_THREE_HOURS_MINUT_QUERY = 3 * 60L;

    public static Long DEFAULT_ONE_DAY_MINUTE_QUERY = 24 * 60L;

    public static Long DEFAULT_WEIXINAUTH_DELAY_MINUTE = 5L;

    /**
     * multi_provider_contract_event的阻塞时间 2013-07-18 12:00:00
     */
    public static Date PAUSE_DATE = new Date(1374120000000L);

    /**
     * 默认值
     */
    public static Integer DEFAULT_QUERY_LIMIT = 50;
    /**
     * 阿波罗前端文案配置时间 产品需求配置
     */
    public static final String TIPS_FOR_APPLYING_MINUTES_KEY = "tips_for_applying_add_minutes";
    public static final String TIPS_FOR_AUTH_MINUTES_KEY = "tips_for_applying_add_minutes";
    public static final String TIPS_FOR_WEB_FAIL_ERROR_KEY = "fail_contract_error_message";
    public static final String TIPS_FOR_WEB_PROCESSING_KEY = "processing_contract_message";
    public static final String TIPS_FOR_DIRECT_PROCESSING_KEY = "processing_direct_message";
    public static final String TIPS_FOR_WEB_APPLO_WEIXIN_ERROR_KEY = "wechat";
    public static final String TIPS_FOR_WEB_APPLO_ALY_ERROR_KEY = "aly";
    public static final String TIPS_FOR_WEB_APPLO_WECHAT_AUTH_ERROR_KEY = "wechatAuth";

    public static final String TIPS_PAY_FOR_PROCESS_PRIVATE_FOREIGN = "对私+非身份证，收钱吧代付通路返回验证中";
    public static final String TIPS_PAY_FOR_PROCESS_PUBLIC = "对公，收钱吧代付通路验证中";
    public static final String TIPS_PAY_FOR_VERIFY_PUBLIC = "对公，收钱吧代付打款成功";
    public static final String TIPS_PAY_FOR_BANK_CHARGEBACK_PRIVATE_FOREIGN = "对私+非身份证，收钱吧代付通路返回银行退单";
    public static final String TIPS_PAY_FOR_PAYMENT_FAIL_PRIVATE_FOREIGN = "对私+非身份证，收钱吧代付通路返回银行打款失败";
    public static final String TIPS_PAY_FOR_VERIFY_FAIL_PUBLIC = "对公，收钱吧代付通路返回金额验证不通过";
    public static final String TIPS_PAY_FOR_PAYMENT_FAIL_PUBLIC = "对公，收钱吧代付通路返回银行打款失败";
    public static final String TIPS_PAY_FOR_BANK_CHARGEBACK_PUBLIC = "对公，收钱吧代付通路返回银行退单";
    /**
     * 文案默认值
     */
    public static final Integer DEFAULT_TIPS_FOR_APP_MINUTES = 120;
    public static final Integer DEFAULT_TIPS_FOR_CRM_MINUTES = 15;
    public static final Integer DEFAULT_TIPS_FOR_SP_MINUTES = 15;

    /**
     * agentName，appid配置
     */
    public static final String AGENT_APPID_CONFIG = "agentAppidConfig";
    public static final String AGENT_NAME_KEY = "agent_name";

    /**
     * 报备服务是否修改商户额度
     **/
    public static final String UPDATE_MAX_TRADE = "update_max_trade";
    public static final String CHANNEL_AUTH_URL = "channel_auth_url";
    public static final String LZ_CHANNELS = "lz_channels";


    public QueryTime getQueryTime() {
        QueryTime queryTime = JSON.parseObject(applicationApolloConfig.getQueryTime(), QueryTime.class);
        if (queryTime == null) {
            return new QueryTime();
        }
        return queryTime;
    }

    public QueryLimit getQueryLimit() {
        QueryLimit queryLimit = JSON.parseObject(applicationApolloConfig.getQueryLimit(), QueryLimit.class);
        if (queryLimit == null) {
            return new QueryLimit();
        }
        return queryLimit;
    }

    public static Map getPayForTip(ContractTask contractTask, Integer status) {
        Map result = JSON.parseObject(contractTask.getResult(), Map.class);
        if (CollectionUtils.isEmpty(result)) {
            result = new HashMap();
        }
        String payMessage = (String) result.get("message");
        String payFail = "代付校验失败";
        if (ScheduleUtil.TIPS_PAY_FOR_PROCESS_PRIVATE_FOREIGN.equals(payMessage)) {
            if (AccountApplyStatus.PAYMENT_FAIL.getStatus().equals(status)) {
                payFail = ScheduleUtil.TIPS_PAY_FOR_PAYMENT_FAIL_PRIVATE_FOREIGN;
            } else {
                payFail = ScheduleUtil.TIPS_PAY_FOR_BANK_CHARGEBACK_PRIVATE_FOREIGN;
            }
        } else if (ScheduleUtil.TIPS_PAY_FOR_PROCESS_PUBLIC.equals(payMessage) || ScheduleUtil.TIPS_PAY_FOR_VERIFY_PUBLIC.equals(payMessage)) {
            if (AccountApplyStatus.FAIL.getStatus().equals(status)) {
                payFail = ScheduleUtil.TIPS_PAY_FOR_VERIFY_FAIL_PUBLIC;
            } else if (AccountApplyStatus.PAYMENT_FAIL.getStatus().equals(status)) {
                payFail = ScheduleUtil.TIPS_PAY_FOR_PAYMENT_FAIL_PUBLIC;
            } else if (AccountApplyStatus.BANK_CHARGEBACK_FAIL.getStatus().equals(status)) {
                payFail = ScheduleUtil.TIPS_PAY_FOR_BANK_CHARGEBACK_PUBLIC;
            }
        }
        result.put("message", payFail);
        result.put("result", payFail);
        return result;
    }
}
