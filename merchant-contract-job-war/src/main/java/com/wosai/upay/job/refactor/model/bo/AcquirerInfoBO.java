package com.wosai.upay.job.refactor.model.bo;

import com.shouqianba.cua.enums.contract.FeeEffectiveTypeEnum;
import com.shouqianba.cua.enums.contract.SupportSpecialIndustryTypeEnum;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.shouqianba.cua.enums.core.AcquirerTradeTypeEnum;
import com.shouqianba.cua.enums.core.ClearTypeEnum;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 收单机构信息BO
 *
 * <AUTHOR>
 * @date 2024/7/26 11:45
 */
@Data
public class AcquirerInfoBO {

    /**
     * 唯一标识
     */
    private String acquirer;

    /**
     * 名称
     */
    private String name;

    /**
     * 收单机构本身对应的provider
     * 兼容旧的代码
     */
    private String provider;

    /**
     * 交易类型  1间连交易  2直连交易
     */
    private AcquirerTradeTypeEnum tradeType;

    /**
     * 清算类型  1间接清算   2直接清算
     */
    private ClearTypeEnum clearType;

    /**
     * 机构类型  1三方机构  2银行机构  3支付源直连机构
     */
    private AcquirerOrgTypeEnum orgType;

    /**
     * 费率生效类型  1 以收钱吧为准  2 以收单机构为准
     */
    private FeeEffectiveTypeEnum feeEffectiveType;

    /**
     * 是否支持特殊行业
     */
    private SupportSpecialIndustryTypeEnum supportSpecialIndustry;

    /**
     * 支持的结算通道列表
     */
    private List<String> supportedProviders;

    @Data
    public static class ProviderBO {
        private String provider;
        private String name;
    }

    /**
     * 是否是三方机构
     *
     * @return 是否是三方机构 true 是 false 不是
     */
    public boolean isThirdPartyOrg() {
        return Objects.equals(orgType, AcquirerOrgTypeEnum.THIRD_PARTY);
    }

    /**
     * 是否是银行机构
     *
     * @return 是否是银行机构 true 是 false 不是
     */
    public boolean isBankOrg() {
        return Objects.equals(orgType, AcquirerOrgTypeEnum.BANK);
    }

    /**
     * 是否是支付源直连机构
     *
     * @return 是否是支付源直连机构 true 是 false 不是
     */
    public boolean isPaySourceDirectOrg() {
        return Objects.equals(orgType, AcquirerOrgTypeEnum.PAY_SOURCE_DIRECT);
    }

    /**
     * 是否是直连交易
     *
     * @return 是否是直连交易 true 是 false 不是
     */
    public boolean isDirectTrade() {
        return Objects.equals(tradeType, AcquirerTradeTypeEnum.DIRECT);
    }

    /**
     * 是否是间连交易
     *
     * @return 是否是间连交易 true 是 false 不是
     */
    public boolean isIndirectTrade() {
        return Objects.equals(tradeType, AcquirerTradeTypeEnum.INDIRECT);
    }

    /**
     * 是否是直接清算
     *
     * @return 是否是直接清算 true 是 false 不是
     */
    public boolean isDirectClear() {
        return Objects.equals(clearType, ClearTypeEnum.DIRECT);
    }

    /**
     * 是否是间接清算
     *
     * @return 是否是间接清算 true 是 false 不是
     */
    public boolean isIndirectClear() {
        return Objects.equals(clearType, ClearTypeEnum.INDIRECT);
    }

    /**
     * 费率是否以收钱吧为准
     *
     * @return
     */
    public boolean isFeeBasedOnSQB() {
        return Objects.equals(feeEffectiveType, FeeEffectiveTypeEnum.SQB);
    }

    /**
     * 是否支持特殊行业
     *
     * @return 是否支持特殊行业 true 是 false 否
     */
    public boolean isSupportSpecialIndustry() {
        return Objects.equals(supportSpecialIndustry, SupportSpecialIndustryTypeEnum.YES);
    }

}
