package com.wosai.upay.job.refactor.service.strategy.concrete.group;

import com.google.common.collect.Lists;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRuleDetailDO;
import com.wosai.upay.job.refactor.model.enums.GroupCombinedTypeEnum;
import com.wosai.upay.job.refactor.model.enums.GroupTypeEnum;
import com.wosai.upay.job.refactor.service.strategy.GroupCombinedProcessStrategy;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 一主多备-报备规则组策略组合处理策略
 *
 * <AUTHOR>
 * @date 2023/12/13 17:42
 */
@Component
public class SingleMMultipleBProcessStrategy implements GroupCombinedProcessStrategy {

    /**
     * 获取策略类型
     *
     * @return 报备规则组策略组合类型枚举
     */
    @Override
    public GroupCombinedTypeEnum getType() {
        return GroupCombinedTypeEnum.SINGLE_MASTER_MULTIPLE_BACKUP;
    }

    /**
     * 获取符合的收单机构进件规则detail
     *
     * @param groupCombinedStrategies 规则组对应的策略组合detail
     * @return 符合条件的策略组合detail
     */
    @Override
    public List<GroupCombinedStrategyDetailDO> listSatisfactionDetails(List<GroupCombinedStrategyDetailDO> groupCombinedStrategies) {
        if (CollectionUtils.isEmpty(groupCombinedStrategies)) {
            return Collections.emptyList();
        }
        List<GroupCombinedStrategyDetailDO> resDetails = Lists.newArrayList();
        Map<Integer, List<GroupCombinedStrategyDetailDO>> detailMap = groupCombinedStrategies.stream().collect(Collectors.groupingBy(GroupCombinedStrategyDetailDO::getGroupType));
        if (detailMap.containsKey(GroupTypeEnum.PRIMARY.getValue())) {
            detailMap.get(GroupTypeEnum.PRIMARY.getValue()).stream().findFirst().ifPresent(resDetails::add);
        }
        if (detailMap.containsKey(GroupTypeEnum.STANDBY.getValue())) {
            resDetails.addAll(detailMap.get(GroupTypeEnum.STANDBY.getValue()));
        }
        return resDetails;
    }

    /**
     * 新增并且返回收单机构进件规则,不存在新增后返回,存在直接返回
     *
     * @param groupCombinedStrategyDetailDOS 进件报备规则组策略组合detail
     * @return 进件报备规则组策略
     */
    @Override
    public Optional<GroupCombinedStrategyDO> getGroupCombineStrategy(List<GroupCombinedStrategyDetailDO> groupCombinedStrategyDetailDOS) {
        throw new ContractBizException("暂不支持一主多备-报备规则组策略组合处理策略");
    }
}
