package com.wosai.upay.job.controller;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.model.LklV3BankTerm;
import com.wosai.upay.job.model.dto.EcApplyPushDTO;
import com.wosai.upay.job.service.LklEcApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/7/27 09:42
 */
@RestController
@RequestMapping("/lkl_pos")
@Slf4j
public class LKLPosController {
    @Autowired
    private LklV3ShopTermBiz lklV3ShopTermBiz;

    @Autowired
    private LklEcApplyService lklEcApplyService;

    @GetMapping("/getActiveNo")
    public LklV3BankTerm posActiveNo(@RequestParam @NotBlank(message = "终端号sn不为空") String sn) {
        log.info("getActiveNo param:{}",sn);
        final LklV3BankTerm bankCardTermData = lklV3ShopTermBiz.getBankCardTermData(sn);
        log.info("getActiveNo result:{}", JSONObject.toJSONString(bankCardTermData));
        return bankCardTermData;
    }


    /**
     * 人工复核异步通知报文
     * https://o.lakala.com/#/home/<USER>/detail?id=984
     * @param dto
     * @return Map {
     *     "code":"SUCCESS",
     *     "message":"执行成功"
     * }
     */
    @PostMapping(path = "/ecApplyPush")
    @ResponseBody
    public Map ecApplyPush(@RequestBody EcApplyPushDTO dto) {
        log.info("ecApplyPush param:{}",JSONObject.toJSONString(dto));
        // TODO 逻辑处理
        return lklEcApplyService.updateManualAuditingEcApply(dto);

    }



}
