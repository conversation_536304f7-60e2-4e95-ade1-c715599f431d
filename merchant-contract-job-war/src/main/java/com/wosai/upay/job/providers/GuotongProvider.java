package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.model.guotong.BaseGuotongResponseModel;
import com.wosai.upay.job.model.guotong.GuotongAuditStatusResultModel;
import com.wosai.upay.job.model.guotong.GuotongContractRequestSimpleModel;
import com.wosai.upay.job.model.guotong.GuotongReportInfoModel;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.model.bo.GuotongMerchantInfoBO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsExtDO;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.MerchantProviderTradeParams;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.guotong.enums.GuotongAuditEnum;
import com.wosai.upay.merchant.contract.model.guotong.enums.GuotongPaywayEnum;
import com.wosai.upay.merchant.contract.model.guotong.request.GuotongAuditStatusQueryRequest;
import com.wosai.upay.merchant.contract.model.guotong.request.GuotongMerchantStatusUpdateRequest;
import com.wosai.upay.merchant.contract.model.guotong.request.GuotongReportInfoQueryRequest;
import com.wosai.upay.merchant.contract.model.provider.GuotongParam;
import com.wosai.upay.merchant.contract.service.GuotongService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.wosai.upay.job.biz.paramContext.ParamContextBiz.KEY_MERCHANT;

/**
 * <AUTHOR>
 * @date 2025/1/22
 */
@Component(ProviderUtil.GUOTONG_CHANNEL)
@Slf4j
@Transactional
public class GuotongProvider extends AbstractProvider {

    @Autowired
    private GuotongService guotongService;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private WechatAuthBiz wechatAuthBiz;
    @Autowired
    private MerchantProviderParamsBiz merchantProviderParamsBiz;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    private static final String CUST_ID = "custId";
    private static final String TIMESTAMP = "timestamp";
    private static final String CUST_LOGIN = "custLogin";

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        int influPtask = AcquirerTypeEnum.GUOTONG.getValue().equals(acquirer) || isSubBiz(merchantSn, AcquirerTypeEnum.GUOTONG.getValue()) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.GUOTONG_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);
        Integer taskType = null;
        if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
            //银行账户变更
            if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
                Map requestParam = (Map) paramContext.get("cardRequestParam");
                if (!CollectionUtils.isEmpty(requestParam)) {
                    //银行卡管理服务发起的变更(merchant_bank_account_pre)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
                } else {
                    //dts订阅直接变更(merchant_bank_account)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                }
            } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
                //更新基本信息
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
            } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {
                String crmUpdate = BeanUtil.getPropString(paramContext, "crmUpdate");
                if (!org.springframework.util.StringUtils.isEmpty(crmUpdate)) {
                    if ("0".equals(crmUpdate)) {
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
                    } else if ("1".equals(crmUpdate)) {
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                    } else if ("2".equals(crmUpdate)) {
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH;
                    }  //do nothing
                }
            } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
                //更新营业执照
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
            } else if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()) {
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE;
            }
            if (taskType == null) {
                return null;
            }
        }
        return subTask.setTask_type(taskType);
    }


    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        GuotongParam guotongParam = buildParam(contractChannel, sub, GuotongParam.class);
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        fillCustomInfo(contextParam);
        if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
            if (needPayFor(contextParam, sub, contractTask)) {
                return null;
            }
            ContractResponse contractResponse = guotongService.contractMerchant(contextParam, guotongParam);
            // 成功的话要保存或者是更新 merchant_acquirer_info中的数据 custId、login_info
            if (contractResponse.isSuccess()) {
                String custId = BeanUtil.getPropString(contractResponse.getResponseParam(), "data.custId");
                GuotongContractRequestSimpleModel request = JSON.parseObject(JSON.toJSONString(contractResponse.getRequestParam()), GuotongContractRequestSimpleModel.class);
                MerchantAcquirerInfoDO merchantAcquirerInfo = new MerchantAcquirerInfoDO();
                merchantAcquirerInfo.setMerchantSn(contractTask.getMerchant_sn());
                merchantAcquirerInfo.setAcquirer(AcquirerTypeEnum.GUOTONG.getValue());
                merchantAcquirerInfo.setAcquirerMerchantId(custId);
                merchantAcquirerInfo.setMerchantInfo(JSON.toJSONString(
                        new GuotongMerchantInfoBO().setCustId(custId).setTimestamp(request.getTimestamp()).setCustLogin(request.getCustLogin()).setBusName(request.getBusName()).setBusType(request.getBusType())
                ));
                merchantAcquirerInfoDAO.insertOrUpdateOne(merchantAcquirerInfo);
            }
            return contractResponse;
        } else if (PaywayEnum.ALIPAY.getValue().equals(payWay) || PaywayEnum.UNIONPAY.getValue().equals(payWay) || PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            return saveMerchantParams(contextParam, contractTask, contractChannel, sub, guotongParam);
        }
        return null;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        GuotongParam guotongParam = buildParam(contractChannel, sub, GuotongParam.class);
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        fillCustomInfo(contextParam);
        if (needPayFor(contextParam, sub, contractTask)) {
            return null;
        }
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(contractTask.getMerchant_sn(), ProviderEnum.PROVIDER_GUOTONG.getValue());
        Integer taskType = sub.getTask_type();
        if (ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(taskType)) {
            // 如果商户信息更新成功要更新 merchant_acquirer_info中的数据
            return guotongService.updateMerchant(contextParam, guotongParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(taskType)) {
            return guotongService.updateMerchantBankAccount(contextParam, guotongParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(taskType)) {
            return guotongService.updateMerchantFeeRate(contextParam, guotongParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE.equals(taskType)) {
            GuotongMerchantStatusUpdateRequest request = new GuotongMerchantStatusUpdateRequest();
            request.setAcquirerMerchantId(acquirerParams.getPay_merchant_id());
            Integer status = MapUtils.getInteger(MapUtils.getMap(contextParam, "merchant"), Merchant.STATUS, Merchant.STATUS_ENABLED);
            if (Merchant.STATUS_ENABLED == status) {
                request.setStatus(GuotongMerchantStatusUpdateRequest.OPEN);
            } else {
                request.setStatus(GuotongMerchantStatusUpdateRequest.CLOSE);
                request.setReason("3"); //先传3 商户注销
            }
            return guotongService.updateMerchantStatus(request, guotongParam);
        }
        return null;
    }

    @Override
    protected ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        GuotongAuditStatusQueryRequest request = new GuotongAuditStatusQueryRequest();
        if (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type())) {
            request.setAuditType(GuotongAuditEnum.NET_IN);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(contractSubTask.getTask_type()) || ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(contractSubTask.getTask_type())) {
            request.setAuditType(GuotongAuditEnum.BANK_ACCOUNT_UPDATE);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE.equals(contractSubTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(contractSubTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(contractSubTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(contractSubTask.getTask_type())) {
            request.setAuditType(GuotongAuditEnum.INFORMATION_UPDATE);
        } else {
            return new ContractResponse()
                    .setCode(400)
                    .setMessage("国通商户当前任务不支持查询");
        }
        Optional<MerchantAcquirerInfoDO> guotongMerchantAcquirerInfo = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(contractSubTask.getMerchant_sn(), AcquirerTypeEnum.GUOTONG.getValue());
        if (!guotongMerchantAcquirerInfo.isPresent()) {
            return new ContractResponse()
                    .setCode(400)
                    .setMessage("国通商户信息为空");
        }
        request.setAcquirerMerchantId(guotongMerchantAcquirerInfo.get().getAcquirerMerchantId());
        GuotongParam guotongParam = contractParamsBiz.buildContractParams(ChannelEnum.GUOTONG.getValue(), GuotongParam.class);
        return guotongService.queryAuditStatus(request, guotongParam);
    }

    @Override
    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        return doHandleContractStatus(contractSubTask, response);
    }

    private HandleQueryStatusResp doHandleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        //失败 修改状态
        if (response.isBusinessFail()) {
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage(response.getMessage());
        } else if (response.isSystemFail()) {
            //可重试异常 不做任何处理
            return new HandleQueryStatusResp()
                    .setRetry(true)
                    .setMessage(response.getMessage());
        } else {
            String custId = WosaiMapUtils.getString(response.getRequestParam(), "custId");
            String resposneParamString = JSON.toJSONString(response.getResponseParam());
            contractSubTask.setResponse_body(resposneParamString);
            BaseGuotongResponseModel<GuotongAuditStatusResultModel> guotongAuditStatusQueryResult = JSON.parseObject(resposneParamString, new TypeReference<BaseGuotongResponseModel<GuotongAuditStatusResultModel>>() {
            });
            if (!guotongAuditStatusQueryResult.isSuccess()) {
                return new HandleQueryStatusResp()
                        .setFail(true)
                        .setMessage(guotongAuditStatusQueryResult.getCode());
            }
            GuotongAuditStatusResultModel guotongAuditStatus = guotongAuditStatusQueryResult.getData();
            if (guotongAuditStatus.auditFail()) {
                return new HandleQueryStatusResp()
                        .setFail(true)
                        .setMessage(Joiner.on(",").join(guotongAuditStatus.getAuthResult(), guotongAuditStatus.getNotThrowReason()));
            }
            if (guotongAuditStatus.auditInProcess() || guotongAuditStatus.auditPending()) {
                return new HandleQueryStatusResp()
                        .setRetry(true)
                        .setMessage("国通审核中");
            }
            // 如果是入网成功就保存参数
            if (guotongAuditStatusQueryResult.getData().getAuthType().equals(GuotongAuditEnum.NET_IN.getValue())) {
                GuotongParam guotongParam = contractParamsBiz.buildContractParamsByContractSubTask(contractSubTask, GuotongParam.class);
                merchantProviderParamsBiz.saveAcquirerParams(contractSubTask.getMerchant_sn(),
                        guotongParam.getChannel_no(), ProviderEnum.PROVIDER_GUOTONG.getValue(), custId, custId, contractSubTask.getContract_rule(), contractSubTask.getRule_group_id());
                Map merchant = merchantService.getMerchantBySn(contractSubTask.getMerchant_sn());
                tradeConfigService.updateGuotongTradeParams(WosaiMapUtils.getString(merchant, DaoConstants.ID), CollectionUtil.hashMap(TransactionParam.GUOTONG_MCH_ID, custId));
            } else {
                // 其他的要更新 merchantAcquirerInfo中的信息
                GuotongContractRequestSimpleModel request = JSON.parseObject(JSON.toJSONString(response.getRequestParam()), GuotongContractRequestSimpleModel.class);
                MerchantAcquirerInfoDO merchantAcquirerInfo = new MerchantAcquirerInfoDO();
                merchantAcquirerInfo.setMerchantSn(contractSubTask.getMerchant_sn());
                merchantAcquirerInfo.setAcquirer(AcquirerTypeEnum.GUOTONG.getValue());
                merchantAcquirerInfo.setAcquirerMerchantId(custId);
                merchantAcquirerInfo.setMerchantInfo(JSON.toJSONString(
                        new GuotongMerchantInfoBO().setCustId(custId).setTimestamp(request.getTimestamp()).setCustLogin(request.getCustLogin()).setBusName(request.getBusName()).setBusType(request.getBusType())
                ));
                merchantAcquirerInfoDAO.insertOrUpdateOne(merchantAcquirerInfo);
            }
            return new HandleQueryStatusResp()
                    .setSuccess(true)
                    .setMessage("国通审核成功");
        }
    }


    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        GuotongParam guotongParam = contractParamsBiz.buildContractParamsByParams(merchantProviderParams, GuotongParam.class);
        return guotongService.wechatSubDevConfig(weixinConfig, guotongParam);
    }

    @Override
    public void createProviderTerminal(String merchantSn, Integer provider) {
        // CUA-11589 国通不报备终端，删除相关逻辑
    }

    @Override
    public ContractResponse queryMerchantContractResult(String providerMerchantId) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getParamsByProviderMerchantIdAndProviderAndPayWay(providerMerchantId, String.valueOf(ProviderEnum.PROVIDER_GUOTONG.getValue()), String.valueOf(PaywayEnum.ACQUIRER.getValue()));
        if (Objects.isNull(acquirerParams)) {
            return ContractResponse.builder().code(460).message("商户还未入网").build();
        }
        GuotongParam guotongParam = contractParamsBiz.buildContractParamsByParams(acquirerParams, GuotongParam.class);
        GuotongReportInfoQueryRequest request = new GuotongReportInfoQueryRequest();
        request.setAcquirerMerchantId(acquirerParams.getPay_merchant_id());
        request.setPayway(GuotongPaywayEnum.UNIONPAY_SCAN);
        ContractResponse contractResponse = guotongService.queryReportInfo(request, guotongParam);
        if (contractResponse.isSuccess()) {
            Map queryResult = WosaiMapUtils.getMap(contractResponse.getResponseParam(), "data");
            GuotongReportInfoModel guotongReportInfoModel = JSON.parseObject(JSON.toJSONString(queryResult), GuotongReportInfoModel.class);
            if (CollectionUtils.isEmpty(guotongReportInfoModel.getList())) {
                contractResponse.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage("未查询到报备信息");
            } else {
                Optional<GuotongReportInfoModel.ReportDetail> reportDetailOptional = guotongReportInfoModel.getList().stream().filter(r -> request.getPayway().getCode().equals(r.getPayWay())).findFirst();
                if (!reportDetailOptional.isPresent()) {
                    contractResponse.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage("未查询到报备信息");
                } else {
                    GuotongReportInfoModel.ReportDetail reportDetail = reportDetailOptional.get();
                    if (reportDetail.reportSuccess()) {
                        changeUnionPayParams(acquirerParams, reportDetail.getThirdMercid());
                        contractResponse.setTradeParam(CollectionUtil.hashMap("status", MerchantProviderParamsExtDO.UNION_PAY_SUCCESS, "sub_mch_id", reportDetail.getThirdMercid()));
                    } else if (reportDetail.reportFail()) {
                        contractResponse.setTradeParam(CollectionUtil.hashMap("status", MerchantProviderParamsExtDO.UNION_PAY_FAIL)).setMessage(reportDetail.getResult());
                    } else {
                        contractResponse.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage("未查询到报备信息");
                    }
                }
            }
        }
        return contractResponse;
    }

    private void changeUnionPayParams(MerchantProviderParams acquirerParams, String payMerchantId) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantIdAndMerchantSn(payMerchantId, acquirerParams.getMerchant_sn());
        if (Objects.nonNull(merchantProviderParams)) {
            ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(acquirerParams.getMerchant_sn());
            if (AcquirerTypeEnum.GUOTONG.getValue().equals(contractStatus.getAcquirer()) && merchantProviderParams.getStatus() == 0) {
                merchantProviderParamsService.setDefaultMerchantProviderParams(merchantProviderParams.getId(), null, "查询时国通云闪付状态变更为成功，重新设置默认");
            }
        }
    }


    private ContractResponse saveMerchantParams(Map contextParam, ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub, GuotongParam guotongParam) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(contractTask.getMerchant_sn(), ProviderEnum.PROVIDER_GUOTONG.getValue());
        GuotongReportInfoQueryRequest guotongReportInfoQueryRequest = new GuotongReportInfoQueryRequest();
        guotongReportInfoQueryRequest.setAcquirerMerchantId(acquirerParams.getPay_merchant_id());
        if (PaywayEnum.WEIXIN.getValue().equals(sub.getPayway())) {
            guotongReportInfoQueryRequest.setPayway(GuotongPaywayEnum.UNIONPAY_WECHAT);
        } else if (PaywayEnum.ALIPAY.getValue().equals(sub.getPayway())) {
            guotongReportInfoQueryRequest.setPayway(GuotongPaywayEnum.UNIONPAY_ALIPAY);
        } else {
            guotongReportInfoQueryRequest.setPayway(GuotongPaywayEnum.UNIONPAY_SCAN);
        }
        ContractResponse contractResponse = guotongService.queryReportInfo(guotongReportInfoQueryRequest, guotongParam);
        if (contractResponse.isSuccess()) {
            Map queryResult = WosaiMapUtils.getMap(contractResponse.getResponseParam(), "data");
            GuotongReportInfoModel guotongReportInfoModel = JSON.parseObject(JSON.toJSONString(queryResult), GuotongReportInfoModel.class);
            if (CollectionUtils.isEmpty(guotongReportInfoModel.getList())) {
                contractResponse.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage("未查询到报备信息");
            } else {
                Optional<GuotongReportInfoModel.ReportDetail> reportDetailOptional = guotongReportInfoModel.getList().stream().filter(r -> guotongReportInfoQueryRequest.getPayway().getCode().equals(r.getPayWay())).findFirst();
                if (!reportDetailOptional.isPresent()) {
                    contractResponse.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage("未查询到报备信息");
                } else {
                    GuotongReportInfoModel.ReportDetail reportDetail = reportDetailOptional.get();
                    if (reportDetail.reportSuccess()) {
                        MerchantProviderParams merchantProviderParams = getAndSaveMerchantProviderParams(contextParam, contractTask, sub, contractChannel, acquirerParams, reportDetail.getThirdMercid());
                        contractResponse.setMerchantProviderParamsId(merchantProviderParams.getId());
                        Map<String, Object> tradeParam = new HashMap<>();
                        tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, merchantProviderParams.getProvider_merchant_id());
                        if (PaywayEnum.WEIXIN.getValue().equals(sub.getPayway())) {
                            tradeParam.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, reportDetail.getThirdMercid());
                        }
                        if (PaywayEnum.ALIPAY.getValue().equals(sub.getPayway())) {
                            tradeParam.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, reportDetail.getThirdMercid());
                        }
                        if (PaywayEnum.UNIONPAY.getValue().equals(sub.getPayway())) {
                            tradeParam.put(MerchantProviderTradeParams.UNIONOPEN_MERCHANT_ID, reportDetail.getThirdMercid());
                        }
                        contractResponse.setTradeParam(tradeParam);
                    } else if (reportDetail.reportFail()) {
                        contractResponse.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage(reportDetail.getResult());
                    } else {
                        contractResponse.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage("未报备");
                    }
                }
            }
        }
        return contractResponse;
    }

    @NotNull
    private MerchantProviderParams getAndSaveMerchantProviderParams(Map contextParam, ContractTask contractTask, ContractSubTask subTask, ContractChannel contractChannel, MerchantProviderParams acquirerParams, String payMerchantId) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantIdAndMerchantSn(payMerchantId, contractTask.getMerchant_sn());
        if (Objects.isNull(merchantProviderParams)) {
            long timeMillis = System.currentTimeMillis();
            MerchantAcquirerInfoDO acquirerInfoDO = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(contractTask.getMerchant_sn(), AcquirerTypeEnum.GUOTONG.getValue()).orElse(new MerchantAcquirerInfoDO());
            GuotongMerchantInfoBO guotongMerchantInfoBO = JSON.parseObject(acquirerInfoDO.getMerchantInfo(), GuotongMerchantInfoBO.class);
            String merchantName = Optional.ofNullable(guotongMerchantInfoBO).orElse(new GuotongMerchantInfoBO()).getBusName();

            MerchantProviderParams params = new MerchantProviderParams();
            params.setId(UUID.randomUUID().toString());
            params.setCtime(timeMillis);
            params.setMtime(timeMillis);
            params.setDeleted(false);
            params.setMerchant_sn(contractTask.getMerchant_sn());
            params.setOut_merchant_sn(contractTask.getMerchant_sn());
            params.setChannel_no(contractChannel.getChannel_no());
            params.setParent_merchant_id(contractChannel.getChannel_no());
            params.setContract_rule(contractChannel.getChannel());
            params.setRule_group_id(contractTask.getRule_group_id());
            params.setProvider(ProviderEnum.PROVIDER_GUOTONG.getValue());
            params.setProvider_merchant_id(acquirerParams.getProvider_merchant_id());
            params.setPay_merchant_id(payMerchantId);
            params.setPayway(subTask.getPayway());
            if (PaywayEnum.WEIXIN.getValue().equals(subTask.getPayway())) {
                params.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE);
                String settlementId = wechatAuthBiz.getSettlementId(BeanUtil.getPropString(contextParam, "merchant.industry"), BeanUtil.getPropInt(contextParam, "merchantBusinessLicense.type"), merchantName);
                params.setWx_settlement_id(settlementId);
            } else {
                params.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL);
            }
            params.setMerchant_name(merchantName);
            merchantProviderParamsMapper.insertSelective(params);
            return params;
        } else {
            merchantProviderParams.setId(merchantProviderParams.getId());
            merchantProviderParams.setCtime(null);
            merchantProviderParamsMapper.updateByPrimaryKeySelective(merchantProviderParams);
            return merchantProviderParams;
        }

    }

    private void fillCustomInfo(Map<String, Object> paramContext) {
        Map merchant = MapUtils.getMap(paramContext, KEY_MERCHANT);
        String merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        Optional<MerchantAcquirerInfoDO> merchantAcquirerInfoDO = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(merchantSn, AcquirerTypeEnum.GUOTONG.getValue());
        if (merchantAcquirerInfoDO.isPresent()) {
            MerchantAcquirerInfoDO acquirerInfoDO = merchantAcquirerInfoDO.get();
            GuotongMerchantInfoBO guotongMerchantInfoBO = JSON.parseObject(acquirerInfoDO.getMerchantInfo(), GuotongMerchantInfoBO.class);
            paramContext.put(CUST_ID, acquirerInfoDO.getAcquirerMerchantId());
            paramContext.put(TIMESTAMP, guotongMerchantInfoBO.getTimestamp());
            paramContext.put(CUST_LOGIN, guotongMerchantInfoBO.getCustLogin());
        }
    }
}
