package com.wosai.upay.job.refactor.task.license.entity;


import com.wosai.upay.job.enume.PlatformEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 营业执照认证申请单req
 *
 * <AUTHOR>
 * @date 2025/1/15 11:23
 */
@Data
public class BusinessLicenseAuditApplyDTO {

    /**
     * 提交平台
     */
    private String platform;

    /**
     * 审批单id
     */
    private Integer fieldAppInfoId;

    /**
     * 提交人
     */
    private String submitUserId;

    /**
     * 提交人姓名
     */
    private String submitUserName;

    /**
     * 营业执照
     */
    private BusinessLicenseDTO businessLicense;

    /**
     * 结算账户
     */
    private BankAccountDTO bankAccount;

    public void basePhotos() {
        if (businessLicense != null) {
            businessLicense.basePhotos();
        }
        if (bankAccount != null) {
            bankAccount.basePhotos();
        }
    }

    public boolean fromCrm() {
        return StringUtils.containsIgnoreCase(platform, PlatformEnum.CRM.getValue());
    }

}
