package com.wosai.upay.job.refactor.model.enums;


/**
 * 进件子任务类型枚举
 *
 * <AUTHOR>
 */
public enum ContractSubTaskTypeEnum implements ITextValueEnum {

    /**
     * 基本信息变更
     */
    BASIC_INFO_CHANGE(0, "基本信息变更"),

    /**
     * 商户状态
     */
    MERCHANT_STATUS_CHANGE(1, "商户状态"),

    /**
     * 结算账户变更
     */
    SETTLEMENT_ACCOUNT_CHANGE(2, "结算账户变更"),

    /**
     * 费率更新
     */
    FEE_UPDATE(3, "费率更新"),

    /**
     * 银行卡更新
     */
    BANK_CARD_UPDATE(4, "银行卡更新"),

    /**
     * 进件
     */
    MERCHANT_OPENING(5, "进件"),

    /**
     * 更新
     */
    UPDATE(6, "更新");

    ContractSubTaskTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }
    private final int value;
    private final String text;


    public String getText() {
        return this.text;
    }

    public int getValue() {
        return this.value;
    }
}
