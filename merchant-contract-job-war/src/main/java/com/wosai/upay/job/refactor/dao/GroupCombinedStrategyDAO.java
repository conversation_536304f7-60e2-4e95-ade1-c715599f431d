package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.GroupCombinedStrategyMapper;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDO;

import java.util.*;


/**
 * 进件报备规则组策略组合表表数据库访问层 {@link GroupCombinedStrategyDO}
 * 对GroupCombinedStrategyMapper层做出简单封装 {@link GroupCombinedStrategyMapper}
 *
 * <AUTHOR>
 */
@Repository
public class GroupCombinedStrategyDAO extends AbstractBaseDAO<GroupCombinedStrategyDO, GroupCombinedStrategyMapper> {

    public GroupCombinedStrategyDAO(SqlSessionFactory sqlSessionFactory, GroupCombinedStrategyMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 插入并返回主键
     *
     * @param entity 实体对象
     * @return 主键
     */
    public Long insertAndGetPrimary(GroupCombinedStrategyDO entity) {
        entityMapper.insert(entity);
        return entity.getId();
    }

    /**
     * 根据组合类型获取报备规则组策略组合
     *
     * @param groupCombinedType 组合类型
     * @return 报备规则组策略组合
     */
    public List<GroupCombinedStrategyDO> listByGroupType(Integer groupCombinedType) {
        if (Objects.isNull(groupCombinedType)) {
            return Collections.emptyList();
        }
        return entityMapper.selectList(new LambdaQueryWrapper<GroupCombinedStrategyDO>().eq(GroupCombinedStrategyDO::getStrategyType, groupCombinedType));
    }

    /**
     * 根据组合类型获取有效的报备规则组策略组合
     *
     * @param groupCombinedType 组合类型
     * @return 报备规则组策略组合
     */
    public List<GroupCombinedStrategyDO> listValidByGroupType(Integer groupCombinedType) {
        if (Objects.isNull(groupCombinedType)) {
            return Collections.emptyList();
        }
        return entityMapper.selectList(new LambdaQueryWrapper<GroupCombinedStrategyDO>()
                .eq(GroupCombinedStrategyDO::getStrategyType, groupCombinedType).eq(GroupCombinedStrategyDO::getValidStatus, ValidStatusEnum.VALID.getValue()));
    }

    /**
     * 根据主键集合获取报备规则组策略组合
     *
     * @param ids 主键集合
     * @return 报备规则组策略组合
     */
    public List<GroupCombinedStrategyDO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return entityMapper.selectList(new LambdaQueryWrapper<GroupCombinedStrategyDO>().in(GroupCombinedStrategyDO::getId, ids));
    }
}
