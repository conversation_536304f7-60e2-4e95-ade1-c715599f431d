package com.wosai.upay.job.refactor.utils;

import com.wosai.upay.job.refactor.model.annotation.FieldConvertMapping;
import com.wosai.upay.job.service.WithKeyValue;

import java.lang.reflect.*;
import java.time.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class DtoConverter {

    /**
     * 缓存类字段/方法元数据（类 -> 字段映射关系）
     */
    private static final Map<Class<?>, Map<String, FieldAccessor>> CLASS_FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * 类型转换器注册表（目标类型 -> 转换器）
     */
    private static final Map<Class<?>, Converter<?>> CONVERTERS = new ConcurrentHashMap<>();

    static {
        // 注册基础类型转换器
        registerConverter(Integer.class, value -> Integer.parseInt(value.toString()));
        registerConverter(int.class, value -> Integer.parseInt(value.toString()));
        registerConverter(Long.class, value -> Long.parseLong(value.toString()));
        registerConverter(long.class, value -> Long.parseLong(value.toString()));
        registerConverter(Boolean.class, value -> Boolean.parseBoolean(value.toString()));
        registerConverter(boolean.class, value -> Boolean.parseBoolean(value.toString()));
        registerConverter(LocalDate.class, value -> LocalDate.parse(value.toString()));
        registerConverter(Enum.class, value -> {
            throw new IllegalArgumentException("请为具体枚举类型注册自定义转换器");
        });
    }

    /**
     * 根据源数据源列表，填充FieldConvertMapping注解到目标DTO对象中
     *
     * @param originalSourceKeyValues 源数据列表
     * @param targetClass             目标DTO类型
     * @return 填充后的DTO对象
     */
    public static <T, U extends WithKeyValue> T convertToDTOBySourceKeyValueList(List<U> originalSourceKeyValues, Class<T> targetClass) {
        try {
            T instance = targetClass.getDeclaredConstructor().newInstance();
            Map<String, FieldAccessor> fieldMapping = getFieldMappings(targetClass);
            boolean nullValue = true;
            for (U param : originalSourceKeyValues) {
                String fieldKey = param.getKey();
                Object fieldValue = param.getValue();

                FieldAccessor accessor = fieldMapping.get(fieldKey);
                if (accessor != null && fieldValue != null) {
                    nullValue = false;
                    accessor.setValue(instance, convertType(fieldValue, accessor.getTargetType()));
                }
            }
            if (nullValue) {
                return null;
            }
            return instance;
        } catch (Exception e) {
            throw new RuntimeException("DTO转换失败: " + targetClass.getName(), e);
        }
    }

    private static Map<String, FieldAccessor> getFieldMappings(Class<?> targetClass) {
        return CLASS_FIELD_CACHE.computeIfAbsent(targetClass, clazz -> {
            Map<String, FieldAccessor> mapping = new HashMap<>();
            for (Field field : clazz.getDeclaredFields()) {
                FieldConvertMapping annotation = field.getAnnotation(FieldConvertMapping.class);
                if (annotation != null) {
                    String sourceKey = annotation.source().isEmpty() ? field.getName() : annotation.source();
                    mapping.put(sourceKey, new FieldAccessor(field));
                }
            }
            for (Method method : clazz.getMethods()) {
                if (method.getName().startsWith("set") && method.isAnnotationPresent(FieldConvertMapping.class)) {
                    FieldConvertMapping annotation = method.getAnnotation(FieldConvertMapping.class);
                    String sourceKey = annotation.source().isEmpty()
                            ? method.getName().substring(3, 4).toLowerCase() + method.getName().substring(4)
                            : annotation.source();
                    mapping.put(sourceKey, new FieldAccessor(method));
                }
            }
            return Collections.unmodifiableMap(mapping);
        });
    }

    private static Object convertType(Object value, Class<?> targetType) {
        if (value == null) return null;
        if (targetType.isAssignableFrom(value.getClass())) return value;
        Converter<?> converter = CONVERTERS.get(targetType);
        if (converter != null) {
            return converter.convert(value);
        }
        if (targetType.isEnum()) {
            return Enum.valueOf((Class<Enum>) targetType, value.toString());
        }
        for (Class<?> interfaceType : targetType.getInterfaces()) {
            if (CONVERTERS.containsKey(interfaceType)) {
                return CONVERTERS.get(interfaceType).convert(value);
            }
        }

        throw new IllegalArgumentException("无法转换类型: " + value.getClass() + " -> " + targetType);
    }


    /**
     * 注册自定义类型转换器
     */
    public static <T> void registerConverter(Class<T> targetType, Converter<T> converter) {
        CONVERTERS.put(targetType, converter);
    }

    private static class FieldAccessor {
        private final Member member;
        private final Class<?> targetType;

        FieldAccessor(Field field) {
            this.member = field;
            this.targetType = field.getType();
            field.setAccessible(true);
        }

        FieldAccessor(Method setterMethod) {
            this.member = setterMethod;
            this.targetType = setterMethod.getParameterTypes()[0];
        }

        void setValue(Object target, Object value) throws Exception {
            if (member instanceof Field) {
                ((Field) member).set(target, value);
            } else if (member instanceof Method) {
                ((Method) member).invoke(target, value);
            }
        }

        Class<?> getTargetType() {
            return targetType;
        }
    }

    @FunctionalInterface
    public interface Converter<T> {
        T convert(Object value);
    }
}