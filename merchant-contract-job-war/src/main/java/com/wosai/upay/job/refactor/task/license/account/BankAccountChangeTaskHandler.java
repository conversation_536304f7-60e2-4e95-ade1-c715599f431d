package com.wosai.upay.job.refactor.task.license.account;

import avro.shaded.com.google.common.collect.Maps;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.*;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.status.ConfigStatusEnum;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.providers.FuyouProvider;
import com.wosai.upay.job.refactor.biz.acquirer.fuyou.FuYouAcquirerFacade;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.dao.InternalScheduleSubTaskDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseAuditApplyDTO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.DefaultStatusEnum;
import com.wosai.upay.job.refactor.model.enums.PaywayEnum;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV2Task;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV2MainTaskContext;
import com.wosai.upay.job.refactor.task.license.update.LicenseUpdateToAcquirerTaskHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 结算账户变更任务
 * 针对营业执照变更任务没法同时变更结算账户信息的场景
 *
 * <AUTHOR>
 * @date 2025/3/11 16:29
 */
@Component
@Slf4j
public class BankAccountChangeTaskHandler {

    @Resource
    private InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    @Resource
    private ContractTaskDAO contractTaskDAO;

    @Resource
    private ChangeAccountWithLicenseUpdate changeAccountWithLicenseUpdate;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private McProviderDAO mcProviderDAO;

    @Autowired
    private MerchantService merchantService;


    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Resource
    private FuYouAcquirerFacade fuYouAcquirerFacade;

    /**
     * 营业执照变更任务需要额外的结算账户变更任务
     *
     * @param acquirer   收单机构
     * @param mainTaskDO 任务
     * @return true- 不支持变更结算信息，false- 支持变更结算信息
     */
    public boolean isLicenseTaskNeedExtraChangeAccountTask(String acquirer, InternalScheduleMainTaskDO mainTaskDO) {
        if (StringUtils.equals(acquirer, AcquirerTypeEnum.FU_YOU.getValue())) {
            BusinessLicenseCertificationV2MainTaskContext context = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class);
            Boolean needUpdateBankAccount = context.getNeedUpdateBankAccount();
            if (!needUpdateBankAccount) {
                return false;
            }
            Integer newAccountType = MapUtils.getInteger(context.getBankAccount(), MerchantBankAccount.TYPE);
            String newAccountNumber = MapUtils.getString(context.getBankAccount(), MerchantBankAccount.NUMBER);
            Integer originalDefaultAccountType = context.getOriginalDefaultAccountType();
            String originalDefaultAccountNumber = context.getOriginalDefaultAccountNumber();
            if (fuYouAcquirerFacade.enableUpdateLicenseAndAccountTogether(mainTaskDO.getMerchantSn(), newAccountType, newAccountNumber,
                    originalDefaultAccountType, originalDefaultAccountNumber)) {
                log.info("isLicenseTaskNeedExtraChangeAccountTask 富友变更营业执照且修改账户信息，不需要额外的结算账户变更任务, merchantSn:{}", mainTaskDO.getMerchantSn());
                return false;
            }
        }
        return CollectionUtils.containsAny(applicationApolloConfig.listLicenseTaskNotSupportChangeAccountAcquirer(), acquirer);
    }

    public InternalScheduleSubTaskProcessResultBO handleBankAccountChange(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        List<InternalScheduleSubTaskDO> internalScheduleSubTaskDOS = internalScheduleSubTaskDAO.listByMainIds(Lists.newArrayList(mainTaskDO.getId()));
        Optional<InternalScheduleSubTaskDO> subTaskOpt = internalScheduleSubTaskDOS.stream()
                .filter(t -> StringUtils.equals(t.getTaskType(),
                        BusinessLicenceCertificationV2Task.SUB_TASK_TYPE_UPDATE_LICENSE)).findAny();
        if (!subTaskOpt.isPresent()) {
            log.error("handleBankAccountChange 结算账户变更任务依赖的营业执照变更任务不存在, merchantSn:{}", mainTaskDO.getMerchantSn());
            return InternalScheduleSubTaskProcessResultBO.fail("系统异常");
        }
        InternalScheduleSubTaskDO licenseUpdateTask = subTaskOpt.get();
        if (licenseUpdateTask.isProcessFail()) {
            subTaskDO.setRemark("依赖的营业执照变更任务失败");
            return InternalScheduleSubTaskProcessResultBO.fail(licenseUpdateTask.getResult());
        }
        if (licenseUpdateTask.isProcessSuccess()) {
            if (subTaskDO.isWaitProcess()) {
                return submitChangeAccountTask(mainTaskDO, subTaskDO);
            } else if (subTaskDO.isWaitExternalResult()) {
                BusinessLicenceCertificationV2Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV2Task.SubTaskContextBOInner.class);
                if (Objects.isNull(subTaskContextBOInner) || Objects.isNull(subTaskContextBOInner.getUpdateBankAccountContractTaskId())) {
                    return submitChangeAccountTask(mainTaskDO, subTaskDO);
                }
                InternalScheduleSubTaskProcessResultBO changeAccountTaskResult = getChangeAccountTaskResult(mainTaskDO, subTaskDO);
                BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = getMainTaskContextBOInner(mainTaskDO);
                String merchantId = MapUtils.getString(mainTaskContextBOInner.getMerchant(), DaoConstants.ID);
                BusinessLicenseAuditApplyDTO auditApplyDTO = mainTaskContextBOInner.getAuditApplyDTO();
                if (changeAccountTaskResult.processStatusIsSuccess() && mainTaskContextBOInner.getNeedUpdateBankAccount() ) {
                    changeAccountWithLicenseUpdate.updateAccountWhenContractSuccess(mainTaskContextBOInner, merchantId, auditApplyDTO);
                }
                if (changeAccountTaskResult.processStatusIsFail() && mainTaskContextBOInner.getNeedUpdateBankAccount() ) {
                    changeAccountWithLicenseUpdate.updateAccountWhenContractFail(mainTaskContextBOInner, merchantId, auditApplyDTO);
                }
                return changeAccountTaskResult;
            }
        }
        return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待营业执照认证任务完成");
    }

    private BusinessLicenseCertificationV2MainTaskContext getMainTaskContextBOInner(InternalScheduleMainTaskDO mainTaskDO) {
        return JSON.parseObject(mainTaskDO.getContext(),
                BusinessLicenseCertificationV2MainTaskContext.class);
    }

    private Map<String, Object> getContextMapByMainTaskDO(InternalScheduleMainTaskDO mainTaskDO) {
        BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(),
                BusinessLicenseCertificationV2MainTaskContext.class);
        Map<String, Object> contextMap = Maps.newHashMap();
        contextMap.put(ParamContextBiz.KEY_MERCHANT, mainTaskContextBOInner.getMerchant());
        contextMap.put(ParamContextBiz.KEY_BANK_ACCOUNT, mainTaskContextBOInner.getBankAccount());
        contextMap.put(ParamContextBiz.KEY_BANK_INFO, mainTaskContextBOInner.getBankInfo());
        contextMap.put(ParamContextBiz.KEY_BUSINESS_LICENCE, mainTaskContextBOInner.getMerchantBusinessLicense());
        return contextMap;
    }

    private InternalScheduleSubTaskProcessResultBO getChangeAccountTaskResult(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationV2Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV2Task.SubTaskContextBOInner.class);
        if (Objects.isNull(subTaskContextBOInner) || Objects.isNull(subTaskContextBOInner.getUpdateBankAccountContractTaskId())) {
            log.error("结算账户变更任务ID为空,subTaskId={}", subTaskDO.getId());
            return InternalScheduleSubTaskProcessResultBO.fail("系统异常");
        }
        Long taskId = subTaskContextBOInner.getUpdateBankAccountContractTaskId();
        Optional<ContractTaskDO> taskOpt = contractTaskDAO.getByPrimaryKey(taskId);
        if (!taskOpt.isPresent()) {
            log.error("结算账户变更任务为空,subTaskId={}", subTaskDO.getId());
            return InternalScheduleSubTaskProcessResultBO.fail("系统异常");
        }
        ContractTaskDO taskDO = taskOpt.get();
        if (taskDO.processFail()) {
            return InternalScheduleSubTaskProcessResultBO.fail(getErrorMsgFromAccountChangeContractTask(taskDO));
        }
        if (taskDO.processSuccess()) {
            return InternalScheduleSubTaskProcessResultBO.success("更新成功");
        }
        return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待更新任务完成");
    }

    private String getErrorMsgFromAccountChangeContractTask(ContractTaskDO taskDO) {
        try {
            if (Objects.isNull(taskDO) || StringUtils.isBlank(taskDO.getResult())) {
                return  "结算账户变更失败";
            }
            Map<String, Object> root = JSON.parseObject(taskDO.getResult(), new TypeReference<Map<String, Object>>(){});
            Object result = root.getOrDefault("result", "");
            return result instanceof String ? (String) result : "结算账户变更失败";
        } catch (Exception e) {
            log.warn("getErrorMsgFromAccountChangeContractTask error, merchantSn:{}", taskDO.getMerchantSn(), e);
            return "结算账户变更失败";
        }
    }

    public Long doSubmitChangeAccountContractTask(InternalScheduleMainTaskDO mainTaskDO, String acquirer) {
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(mainTaskDO.getMerchantSn());
        List<MerchantProviderParamsDO> params = merchantTradeParamsBiz.listParamsByMerchantSn(mainTaskDO.getMerchantSn());
        Map<String, MerchantProviderParamsDO> acquirerParamsMap = params.stream()
                .filter(t -> Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .collect(Collectors.toMap(
                        t -> mcProviderDAO.getByProvider(t.getProvider().toString()).map(McProviderDO::getAcquirer).orElse(""),
                        Function.identity(),
                        (a, b) -> a));
        MerchantProviderParamsDO merchantProviderParamsDO = acquirerParamsMap.get(acquirer);

        ContractTaskDO contractTaskDO = new ContractTaskDO();
        contractTaskDO.setMerchantSn(mainTaskDO.getMerchantSn());
        contractTaskDO.setMerchantName(merchantInfo.getName());
        contractTaskDO.setType(ContractTaskTypeEnum.SETTLEMENT_ACCOUNT_CHANGE.getValue());
        Map<String, Object> contextMap = getContextMapByMainTaskDO(mainTaskDO);
        contextMap.put(LicenseUpdateToAcquirerTaskHandler.LICENSE_UPDATE_V2_NEED_UPDATE_ACCOUNT_KEY, true);
        contractTaskDO.setEventContext(JSON.toJSONString(contextMap));
        contractTaskDO.setAffectSubTaskCount(1);
        contractTaskDO.setAffectStatusSuccessTaskCount(0);
        // TODO 如果非当前收单机构，setRuleGroupId赋值看一下是否需要改为当前收单机构
        contractTaskDO.setRuleGroupId(merchantProviderParamsDO.getRuleGroupId());
        ContractSubTaskDO contractSubTaskDO = new ContractSubTaskDO();
        contractSubTaskDO.setStatusInfluPTask(AffectPrimaryTaskStatusEnum.YES.getValue());
        contractSubTaskDO.setMerchantSn(mainTaskDO.getMerchantSn());
        contractSubTaskDO.setDefaultChannel(DefaultStatusEnum.NOT_DEFAULT.getValue());
        contractSubTaskDO.setChangeConfig(ConfigStatusEnum.NOT_CONFIG.getValue());
        contractSubTaskDO.setTaskType(ContractSubTaskTypeEnum.BANK_CARD_UPDATE.getValue());
        contractSubTaskDO.setPayway(PaywayEnum.ACQUIRER.getValue());
        contractSubTaskDO.setScheduleStatus(ScheduleStatusEnum.CAN.getValue());
        // TODO 这里channel和ruleGroupId应该通过接口获取
        contractSubTaskDO.setChannel(acquirer);
        contractSubTaskDO.setContractRule(merchantProviderParamsDO.getContractRule());
        contractSubTaskDO.setRuleGroupId(merchantProviderParamsDO.getRuleGroupId());
        return contractTaskDAO.batchInsertTasks(contractTaskDO, Lists.newArrayList(contractSubTaskDO));
    }

    private InternalScheduleSubTaskProcessResultBO submitChangeAccountTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        Long taskId = doSubmitChangeAccountContractTask(mainTaskDO, mainTaskDO.getAcquirer());
        BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = getMainTaskContextBOInner(mainTaskDO);
        BusinessLicenseAuditApplyDTO auditApplyDTO = mainTaskContextBOInner.getAuditApplyDTO();
        if (mainTaskContextBOInner.getNeedUpdateBankAccount()) {
            changeAccountWithLicenseUpdate.updateDefaultAccountVerifyStatus(MapUtils.getString(mainTaskContextBOInner.getMerchant(), DaoConstants.ID),
                    MerchantBankAccount.VERIFY_STATUS_INPROGRESS, auditApplyDTO.getSubmitUserId(), auditApplyDTO.getSubmitUserName(), auditApplyDTO.getPlatform());
        }
        BusinessLicenceCertificationV2Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV2Task.SubTaskContextBOInner.class);
        if (Objects.isNull(subTaskContextBOInner)) {
            subTaskContextBOInner = new BusinessLicenceCertificationV2Task.SubTaskContextBOInner();
        }
        subTaskContextBOInner.setUpdateBankAccountContractTaskId(taskId);
        subTaskDO.setContext(JSON.toJSONString(subTaskContextBOInner));
        return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待结算账户变更任务完成");
    }
}
