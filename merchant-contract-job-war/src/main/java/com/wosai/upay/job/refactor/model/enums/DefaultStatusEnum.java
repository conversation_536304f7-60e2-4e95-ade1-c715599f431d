package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 默认状态枚举
 *
 * <AUTHOR>
 */
public enum DefaultStatusEnum implements ITextValueEnum<Integer> {

    NOT_DEFAULT(0, "非默认"),


    DEFAULT(1, "默认");


    private final Integer value;
    private final String text;

    DefaultStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
