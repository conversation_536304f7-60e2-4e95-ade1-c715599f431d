package com.wosai.upay.job.refactor.model.converter;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.model.dto.AcquirerDto;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
public class McAcquirerConverter {

    public static AcquirerDto toAcquirerDto(McAcquirerDO mcAcquirerDO) {
        if (mcAcquirerDO == null) {
            return null;
        }
        AcquirerDto acquirerDto = new AcquirerDto();
        BeanUtils.copyProperties(mcAcquirerDO, acquirerDto);
        acquirerDto.setClear_type(mcAcquirerDO.getClearType());
        acquirerDto.setCreate_at(mcAcquirerDO.getCreateAt());
        acquirerDto.setUpdate_at(mcAcquirerDO.getUpdateAt());
        if (WosaiStringUtils.isNotEmpty(mcAcquirerDO.getMetadata())) {
            acquirerDto.setMetadata(JSON.parseArray(mcAcquirerDO.getMetadata(), AcquirerDto.Provider.class));
        }
        return acquirerDto;
    }

}
