package com.wosai.upay.job.volcengine.dataCenter;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.utils.json.JSON;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.merchant.exceptions.BusinessException;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.job.volcengine.dto.DataCenterMessageBody;
import com.wosai.upay.job.volcengine.enums.DataAppUtil;
import com.wosai.upay.job.volcengine.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 数据中心服务
 */
@Slf4j
@Component
public class DataCenterProducer {

    @Resource(name = "dataCenterKafkaTemplate")
    private KafkaTemplate<String, String> kafkaTemplate;
    @Resource(name = "dataCenterProducerConfig")
    private DataCenterProducerConfig producerConfig;


    /**
     * 同步用户属性
     */
    public void publishProfile(String merchantId, Object params) {
        try {
            DataCenterMessageBody body = new DataCenterMessageBody()
                    .setMessage_type(MessageTypeEnum.PROFILE.name())
                    .setApp_id(DataAppUtil.B_MERCHANT.getCurrentAppId())
                    .setUser_unique_id(merchantId)
                    .setEvent_params(params);
            publish(body);
        } catch (Exception e) {
            log.error("publishProfile error {} {}", merchantId, JSON.toJSONString(params), e);
        }
    }

    /**
     * 发布火山事件
     */
    public void publishEvent(String eventName, String merchantId, Object params) {
        try {
            DataCenterMessageBody body = new DataCenterMessageBody()
                    .setMessage_type(MessageTypeEnum.EVENT.name())
                    .setEvent_name(eventName)
                    .setApp_id(DataAppUtil.B_MERCHANT.getCurrentAppId())
                    .setUser_unique_id(merchantId)
                    .setEvent_params(params);
            publish(body);
        } catch (Exception e) {
            log.error("publishEvent error {} {} {}", eventName, merchantId, JSON.toJSONString(params), e);
        }
    }


    /**
     * 发送火山消息
     *
     * @param body
     */
    private void publish(DataCenterMessageBody body) {
        if (Objects.isNull(body)) {
            throw new BusinessException("当前事件为空.");
        }
        if (StringUtils.isBlank(body.getMessage_type())) {
            body.setMessage_type(MessageTypeEnum.EVENT.name());
        }
        if (Objects.isNull(body.getEvent_params())) {
            throw new BusinessException(String.format("当前事件eventParams为空. body:%s", JSONObject.toJSONString(body)));
        }
        //追加时间戳
        appendTimestamp(body.getEvent_params());

        String key = body.getUser_unique_id();
        if (Objects.isNull(key)) {
            key = "N/A";
        }
        String bodyStr = null;
        try {
            bodyStr = JSONObject.toJSONString(body);
            ListenableFuture<SendResult<String, String>> result = kafkaTemplate
                    .send(producerConfig.getTopic(), key, bodyStr);
            final String finalBodyStr = bodyStr;
            result.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
                @Override
                public void onFailure(Throwable ex) {
                    log.error("发送消息失败. topic:{}, brokerAddress:{}, body:{}",
                            producerConfig.getTopic(), producerConfig.getBrokerAddress(), finalBodyStr, ex);
                }

                @Override
                public void onSuccess(SendResult<String, String> result) {
                    log.info("发送成功 topic:{} body:{}", producerConfig.getTopic(), finalBodyStr);
                }
            });
        } catch (Exception e) {
            log.error("写入kafka失败. topic:{},brokerAddress:{},body:{}",
                    producerConfig.getTopic(), producerConfig.getBrokerAddress(), bodyStr, e);
            throw new CommonPubBizException("写入kafka失败", e);
        }
    }


    private void appendTimestamp(Object eventParams) {
        try {
            if (!(eventParams instanceof Map)) {
                return;
            }
            Map<String, Object> eventParamsMap = (Map<String, Object>) eventParams;
            if (MapUtils.isEmpty(eventParamsMap)) {
                return;
            }
            final String timestampFiled = "timestamp";
            if (eventParamsMap.get(timestampFiled) != null) {
                return;
            }
            eventParamsMap.put(timestampFiled, System.currentTimeMillis());
        } catch (Exception e) {
            log.error("appendTimestamp error eventParams:{},cause:{}", JSONObject.toJSONString(eventParams), e);
        }
    }

}
