package com.wosai.upay.job.refactor.event;

import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.databus.event.merchant.contract.MerchantContractStatusChangeEvent;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.PaymentModeChangeBiz;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandMerchantInfoQueryResp;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.PaymentModeChangeReq;
import com.wosai.upay.job.model.application.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 进件状态变化的事件监听器
 */
@Slf4j
@Component
public class ContractStatusChangeEventListener implements ApplicationListener<ContractStatusChangeEvent> {

    @Autowired
    private PaymentModeChangeBiz paymentModeChangeBiz;
    @Autowired
    private BrandBusinessClient brandBusinessClient;

    @Override
    public void onApplicationEvent(ContractStatusChangeEvent event) {
        MerchantContractStatusChangeEvent statusChangeEvent = event.getSource();
        if (ContractStatus.STATUS_SUCCESS != statusChangeEvent.getStatus()) {
            return;
        }
        // 判断是不是品牌商户 品牌商户是否使用着品牌支付模式
        BrandMerchantInfoQueryResp brandMerchantInfoQueryResp = brandBusinessClient.getBrandMerchantInfoByMerchantId(statusChangeEvent.getMerchantId());
        if (brandMerchantInfoQueryResp.isSubBrandMerchant() && brandMerchantInfoQueryResp.isBrandPayMode()) {
            PaymentModeChangeReq req = new PaymentModeChangeReq();
            req.setMerchantId(event.getSource().getMerchantId());
            if (PaymentModeEnum.WX_BRAND_MODE.equals(brandMerchantInfoQueryResp.getPaymentMode())) {
                req.setTargetPaymentMode(PaymentModeChangeReq.TARGET_PAYMENT_MODE_WECHAT);
            }
            if (PaymentModeEnum.ALI_BRAND_MODE.equals(brandMerchantInfoQueryResp.getPaymentMode())) {
                req.setTargetPaymentMode(PaymentModeChangeReq.TARGET_PAYMENT_MODE_ALIPAY);
            }
            if (PaymentModeEnum.ALI_WX_BRAND_MODE.equals(brandMerchantInfoQueryResp.getPaymentMode())) {
                req.setTargetPaymentMode(PaymentModeChangeReq.TARGET_PAYMENT_MODE_ALIPAY_WECHAT);
            }
            if (Objects.nonNull(req.getTargetPaymentMode())) {
                req.setRemark("进件服务");
                req.setOperatorId("进件服务");
                req.setRemark("进件通过切换支付模式");
                CommonResult commonResult = paymentModeChangeBiz.changePaymentMode(req);
                if (commonResult.isSuccess()) {
                    log.info("商户进件通过，切换支付模式成功，商户ID:{} 切换模式:{}", req.getMerchantId(), brandMerchantInfoQueryResp.getPaymentMode().getDesc());
                } else {
                    log.error("商户进件通过，切换支付模式失败，商户ID:{} 切换模式:{} 失败原因:{}", req.getMerchantId(), brandMerchantInfoQueryResp.getPaymentMode().getDesc(), commonResult.getMsg());
                }
            }
        }
    }
}
