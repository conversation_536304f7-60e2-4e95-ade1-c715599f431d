package com.wosai.upay.job.refactor.task.license.micro.builder.processor;

import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.task.license.micro.builder.context.TradeParamsBuilderContext;

/**
 * 支付方式处理器接口
 * 使用策略模式，为不同的支付方式提供不同的处理逻辑
 * 
 * <AUTHOR>
 */
public interface PaywayProcessor {
    
    /**
     * 处理参数
     * 
     * @param newParam 新参数对象
     * @param oldParam 旧参数对象
     * @param context 构建上下文
     */
    void processParam(MerchantProviderParamsDO newParam,
                      MerchantProviderParamsDO oldParam,
                      TradeParamsBuilderContext context);
    
    /**
     * 支持的支付方式
     */
    Integer getSupportedPayway();
    
    /**
     * 支持的收单机构类型
     */
    String getSupportedAcquirerType();
}
