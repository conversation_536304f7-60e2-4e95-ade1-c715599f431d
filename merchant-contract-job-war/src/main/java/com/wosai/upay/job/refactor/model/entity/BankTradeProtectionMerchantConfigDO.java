package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.*;
import com.wosai.upay.job.refactor.model.enums.BankTradeProtectionStatusEnum;
import lombok.Data;

/**
 * 银行交易保障商户配置表表实体对象
 *
 * <AUTHOR>
 */
@TableName("bank_trade_protection_merchant_config")
@Data
public class BankTradeProtectionMerchantConfigDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 打开状态 0-关闭保障 1-打开保障
     */
    @TableField(value = "open_status")
    private Integer openStatus;
    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Timestamp mtime;
    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Timestamp ctime;


    /**
     * 是否打开
     *
     * @return 是否打开 true:打开 false:未打开
     */
    public boolean isOpen() {
        if (Objects.isNull(this.openStatus)) {
            return true; // 默认打开
        }
        return Objects.equals(this.openStatus, BankTradeProtectionStatusEnum.OPEN.getValue());
    }

    /**
     * 是否关闭
     *
     * @return 是否关闭 true:关闭 false:未关闭
     */
    public boolean isClose() {
        return Objects.equals(this.openStatus, BankTradeProtectionStatusEnum.CLOSE.getValue());
    }

}

