package com.wosai.upay.job.refactor.utils;

import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * map工具类
 *
 * <AUTHOR>
 * @date 2023/11/13 10:22
 */
public final class MapUtils {

    private MapUtils() {
    }

    /**
     * 从给定的Map中获取指定键的值，如果key存在，则执行提供的操作。
     *
     * @param map    要查找值的Map
     * @param key    要查找的键
     * @param action 对值执行的操作
     * @param <K>    键的类型
     * @param <V>    值的类型
     */
    public static <K, V> void ifPresent(Map<K, V> map, K key, Consumer<V> action) {
        if (map.containsKey(key)) {
            action.accept(map.get(key));
        }
    }

    /**
     * 从给定的Map中获取指定键的值，如果key存在且通过前置校验，则执行提供的操作。
     *
     * @param map         要查找值的Map
     * @param key         要查找的键
     * @param preCheck    前置校验，如果为true，则执行操作
     * @param action      对值执行的操作
     * @param <K>         键的类型
     * @param <V>         值的类型
     */
    public static <K, V> void ifPresentAndValid(Map<K, V> map, K key, Predicate<V> preCheck, Consumer<V> action) {
        if (map.containsKey(key)) {
            V value = map.get(key);
            if (preCheck.test(value)) {
                action.accept(value);
            }
        }
    }
}
