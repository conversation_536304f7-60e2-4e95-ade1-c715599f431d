package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.service.MerchantUserService;
import com.wosai.sales.core.service.UserService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.bankDirect.CcbDirectBiz;
import com.wosai.upay.job.biz.bankDirect.CmbcDirectBiz;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.enume.CmbcStatusEnum;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.refactor.service.localcache.IndustryMappingLocalCacheService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.cmbc.response.MerchantQueryResponse;
import com.wosai.upay.merchant.contract.service.CMBCService;
import com.wosai.upay.merchant.contract.service.HXService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;


/**
 * @Description: 民生银行处理类
 * <AUTHOR>
 * @Date 2025/5/12 15:35
 */
@Component(ProviderUtil.CMBC_CHANNEL)
@Slf4j
@Transactional
public class CmbcProvider extends AbstractProvider {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;


    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;


    @Autowired
    private CMBCService cmbcService;

    @Lazy
    @Autowired
    private CmbcDirectBiz cmbcDirectBiz;



    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        // 民生暂时不支持更新
        return null;
    }

    @Override
    public ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask contractSubTask) {
        if (!ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())){
            return null;
        }
        Integer payWay = contractSubTask.getPayway();
        Map<String, String> contextParam = contractTask.getEventContext();
            ContractResponse contractResponse;
            if (Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue())) {
                contractResponse = cmbcService.contractMerchant(contextParam);
                if (contractResponse.isSuccess()) {
                    final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
                    // 已提交待银行审核
                    cmbcDirectBiz.recordViewProcess(apply, BankDirectApplyViewStatusEnum.DISTRIBUTING.getValue(), new Date());
                }
            } else {
                // 其他支付类型：微信，支付宝、云闪付
                contractResponse = cmbcService.contractMerchantOtherPayWay(contractSubTask);
            }
            return contractResponse;

    }




    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        // 民生不支持更新操作
        return null;
    }

    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        throw new ContractBizException("民生银行不支持此业务");
    }

    @Override
    protected ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        return cmbcService.queryContractStatusByContractId(contractSubTask);
    }

    @Override
    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        return doHandleContractStatus(contractSubTask, response);
    }

    /**
     * 查询的状态做处理
     *
     * @param contractSubTask
     * @param response
     * @return
     */
    public HandleQueryStatusResp doHandleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        //失败 修改状态
        if (response.isBusinessFail()) {
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage(response.getMessage());
        } else if (response.isSystemFail()) {
            //可重试异常 不做任何处理
            return new HandleQueryStatusResp()
                    .setRetry(true)
                    .setMessage(response.getMessage());
        } else {
            //返回值
            Map<String, Object> callbackMsg = response.getResponseParam();
            final MerchantQueryResponse merchantQueryResponse = JSONObject.parseObject(JSONObject.toJSONString(callbackMsg), MerchantQueryResponse.class);

            //审批状态 0-已受理 1-处理中 2-处理完成 3-作废
            final String status = merchantQueryResponse.getStatus();
            final String currentApprove = merchantQueryResponse.getCurrentApprove();
            final String approveNode = merchantQueryResponse.getApproveNode();
            final String originResult = contractSubTask.getResult();
            //状态
            StringBuilder messageBuilder = new StringBuilder(CmbcStatusEnum.getMessage(status));
            //获取银行直连申请任务
            final Long pTaskId = contractSubTask.getP_task_id();
            if(!Objects.equals(status,CmbcStatusEnum.ACCEPTED.getCode())) {
                messageBuilder.append(";审批人:").append(currentApprove);
            }
            if(Objects.equals(status,CmbcStatusEnum.PROCESSING.getCode())) {
                messageBuilder.append(";审批节点:").append(approveNode);
            }
            if(Objects.equals(status,CmbcStatusEnum.CANCELLED.getCode())) {
                messageBuilder.append(":").append(merchantQueryResponse.getMessage());
            }
            final String result = messageBuilder.toString();
            updateSubAndParentTaskResult(contractSubTask.getId(), originResult, result);


            final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(pTaskId);

            if (Objects.equals(status, CmbcStatusEnum.CANCELLED.getCode())) {
                //民生退回
                return new HandleQueryStatusResp()
                        .setFail(true)
                        .setMessage(result);
            }
            if (!Objects.equals(status, CmbcStatusEnum.COMPLETED.getCode())) {
                return new HandleQueryStatusResp()
                        .setRetry(true)
                        .setMessage(result);
            }
            //银行已审核,待商户微信实名认证
            cmbcDirectBiz.recordViewProcess(apply,  BankDirectApplyViewStatusEnum.AUTHING_ENTERPRISE.getValue(), new Date());
            return new HandleQueryStatusResp().setSuccess(true)
                    .setMessage(result);
        }
    }





    /**
     * <AUTHOR>
     * @Description: 同时更新sub_task和task中的result
     * @time 17:24
     */
    public void updateSubAndParentTaskResult(Long id, String originResult, String targetResult) {
        if (Objects.equals(originResult, targetResult)) {
            return;
        }
        ContractSubTask subTask = new ContractSubTask();
        subTask.setId(id);
        subTask.setResult(targetResult);
        contractSubTaskMapper.updateByPrimaryKey(subTask);
        //为了保持contractTask同步;
        final ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(id);
        final Long pTaskId = contractSubTask.getP_task_id();
        final ContractTask task = contractTaskMapper.selectByPrimaryKey(pTaskId);
        if (Objects.equals(task.getResult(), targetResult)) {
            return;
        }
        //只有影响主任务的时候才会去更新主任务的中的result
        if (Objects.equals(contractSubTask.getStatus_influ_p_task(), 1)) {
            final ContractTask contractTask = new ContractTask();
            contractTask.setId(pTaskId);
            contractTask.setResult(targetResult);
            contractTask.setPriority(task.getPriority());
            contractTaskMapper.updateByPrimaryKey(contractTask);
        }
    }





}

