package com.wosai.upay.job.controller;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.service.FuyouCallBackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Description: Fuyou回调
 * <AUTHOR>
 * @Date 2023/10/13
 **/

@RestController
@RequestMapping("/fuyou")
@Slf4j
public class FuyouController {

    @Autowired
    private FuyouCallBackService fuyouCallBackService;

    @PostMapping(path = "/subMchIdCallBack", produces = {"application/json;charset=utf-8"})
    @ResponseBody
    public int subMchIdCallBack(@RequestBody Map<String, Object> params) throws Exception {
        log.info("fuyou subMchId callback : {}", JSON.toJSONString(params));
        return fuyouCallBackService.querySubTaskByContractId(params);
    }

    @PostMapping(path = "/contractCallBack", produces = {"application/json;charset=utf-8"})
    @ResponseBody
    public int contractCallBack(@RequestBody Map<String, Object> params) throws Exception {
        log.info("fuyou contract callback : {}", JSON.toJSONString(params));
        return fuyouCallBackService.contractSubTaskByContractId(params);
    }

    @PostMapping(path = "/updateCallBack", produces = {"application/json;charset=utf-8"})
    @ResponseBody
    public int updateCallBack(@RequestBody Map<String, Object> params) throws Exception {
        log.info("fuyou update callback : {}", JSON.toJSONString(params));
        return fuyouCallBackService.updateSubTaskByContractId(params);
    }


    @PostMapping(path = "/changeAuditPush")
    @ResponseBody
    public int changeAuditPush(@RequestParam Map<String, Object> params) {
        log.info("fuyou changeAuditPush callback : {}", JSON.toJSONString(params));
        final String req = MapUtils.getString(params, "req");
        final Map<String, Object> map = JSONObject.parseObject(req, Map.class);
        return fuyouCallBackService.changeAuditPush(map);
    }

}
