package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 商户切换收单机构记录表表实体对象
 *
 * <AUTHOR>
 */
@TableName("mc_acquirer_change")
@Data
public class McAcquirerChangeDO {

    public static final String TRADE_APP_ID_KEY = "tradeAppId";

    public static final String FORCE_CHANGE_KEY = "forceChange";

    public static final String LOG_PARAMS_KEY = "logParamsDto";

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 申请id，唯一标志
     */
    @TableField(value = "apply_id")
    private String applyId;
    /**
     * 商户sn
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 商户id
     */
    @TableField(value = "merchant_id")
    private String merchantId;
    /**
     * 原收单机构
     */
    @TableField(value = "source_acquirer")
    private String sourceAcquirer;
    /**
     * 目标收单机构
     */
    @TableField(value = "target_acquirer")
    private String targetAcquirer;
    /**
     * 状态 1：已提交 2：已发起报备 3：报备成功 5：已关闭交易权限  7：已发起提现  9：切换交易参数成功  11：已打开交易权限  19：切换成功  20：切换失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 提示语
     */
    @TableField(value = "memo")
    private String memo;
    /**
     * 状态变更过程
     */
    @TableField(value = "process")
    private String process;
    /**
     * 额外信息
     */
    @TableField(value = "extra")
    private String extra;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;
    /**
     * 是否立刻切换收单机构
     */
    @TableField(value = "immediately")
    private Integer immediately;


}

