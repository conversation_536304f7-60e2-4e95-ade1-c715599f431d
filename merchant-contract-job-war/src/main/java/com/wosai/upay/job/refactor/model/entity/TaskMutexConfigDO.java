package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/12/19
 */
@TableName("task_mutex_config")
@Data
public class TaskMutexConfigDO {

    @TableId
    private Long id;
    /**
     * 类型标识
     */
    @TableField(value = "type_identifier")
    private String typeIdentifier;
    /**
     * 类型分类
     */
    @TableField(value = "type_category")
    private Integer typeCategory;
    /**
     * 互斥的进件任务类型列表
     */
    @TableField(value = "mutex_task_types")
    private String mutexTaskTypes;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;
}
