package com.wosai.upay.job.refactor.event;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.enume.WxUseType;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import com.wosai.upay.merchant.contract.service.ComposeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/22
 */
@Component
@Slf4j
public class PayMchUpdateSuccessListener implements ApplicationListener<PayMchUpdateSuccessEvent> {

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ComposeService composeService;

    @Autowired
    private BlueSeaService blueSeaService;

    private static List<Integer> ONLINE_USE_LIST = Arrays.asList(WxUseType.ONLINE_PAYMENT.getCode(), WxUseType.SCENARIO_PAYMENT.getCode());

    @Override
    public void onApplicationEvent(PayMchUpdateSuccessEvent event) {
        merchantProviderParamsDAO.getByMerchantSnAndProviderAndPayWay(event.getMerchantSn(), event.getProvider(), event.getPayWay())
                .stream()
                .filter(paramsDO -> ONLINE_USE_LIST.contains(paramsDO.getWxUseType()))
                .forEach(paramsDO -> {
                    try {
                        ContractResponse response = null;
                        if (Objects.equals(PaywayEnum.ALIPAY.getValue(), event.getPayWay())) {
                            response = blueSeaService.updateAliMchInfoBySubMchId(paramsDO.getPayMerchantId(), new HashMap());
                        } else if (Objects.equals(PaywayEnum.WEIXIN.getValue(), event.getPayWay())) {
                            response = composeService.updateWxMchInfoBySubMchId(paramsDO.getPayMerchantId(), new HashMap());
                        }
                        if (Objects.nonNull(response)) {
                            if (response.isSuccess()) {
                                log.info("同步线上收款参数成功 {} {}", event.getMerchantSn(), paramsDO.getPayMerchantId());
                            } else {
                                log.info("同步线上收款参数失败 {} {} {}", event.getMerchantSn(), paramsDO.getPayMerchantId(), response.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        log.info("同步线上收款参数异常 {} {}", event.getMerchantSn(), paramsDO.getPayMerchantId(), e);
                    }
                });
    }


}
