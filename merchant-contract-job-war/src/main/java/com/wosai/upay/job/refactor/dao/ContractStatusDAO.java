package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.ContractStatusDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;

import java.util.Optional;


/**
 * 报备总状态表表数据库访问层 {@link ContractStatusDO}
 * 对ContractStatusMapper层做出简单封装 {@link ContractStatusDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class ContractStatusDAO extends AbstractBaseDAO<ContractStatusDO, ContractStatusDynamicMapper> {

    public ContractStatusDAO(SqlSessionFactory sqlSessionFactory, ContractStatusDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户号获取商户报备总状态
     *
     * @param merchantSn 商户号
     * @return 商户报备总状态
     */
    public Optional<ContractStatusDO> getByMerchantSn(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        return super.selectOne(new LambdaQueryWrapper<ContractStatusDO>().eq(ContractStatusDO::getMerchantSn, merchantSn));
    }

    /**
     * 根据商户号删除商户报备总状态
     *
     * @param merchantSn 商户号
     */
    public void deleteByMerchantSn(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return;
        }
        entityMapper.delete(new LambdaQueryWrapper<ContractStatusDO>().eq(ContractStatusDO::getMerchantSn, merchantSn));
    }
}
