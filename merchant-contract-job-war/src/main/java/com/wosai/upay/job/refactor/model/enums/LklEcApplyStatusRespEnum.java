package com.wosai.upay.job.refactor.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/1/2 15:30
 */
@Getter
public enum LklEcApplyStatusRespEnum {
    /**当前电子合同状态
     * 0-不存在电子合同,需要进行申请
     * 1-人工审核中,
     * 2-人工审核通过,
     * 3-人工审核失败,
     * 4-签约地址过期,
     * 5-签约成功,
     * 6-签约失败,
     */

    NOT_EXISTS(0, "不存在电子合同,需要进行申请"),
    AUDITING(1, "人工审核中"),
    AUDIT_PASSED(2, "人工审核通过"),
    AUDIT_FAILED(3, "人工审核失败"),
    SIGN_ADDRESS_EXPIRED(4, "签约地址过期"),
    SIGN_SUCCESS(5, "签约成功"),
    SIGN_FAILED(6, "签约失败");



    private final int value;
    private final String text;

    LklEcApplyStatusRespEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }



    public static LklEcApplyStatusRespEnum getEnumByValue(Integer value) {
        for (LklEcApplyStatusRespEnum lklEcApplyStatusRespEnum : LklEcApplyStatusRespEnum.values()) {
            if(Objects.equals(value,lklEcApplyStatusRespEnum.getValue())) {
                return lklEcApplyStatusRespEnum;
            }
        }
        return null;
    }
}
