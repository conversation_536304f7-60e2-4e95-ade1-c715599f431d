package com.wosai.upay.job.refactor.event;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.WxSettlementIdChangeBiz;
import com.wosai.upay.job.biz.comboparams.AliPayServiceCodeBiz;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import com.wosai.upay.merchant.contract.service.ComposeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.concurrent.CompletableFuture;

import static javax.management.timer.Timer.ONE_MINUTE;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
@Component
@Slf4j
public class TradeParamsChangeListener implements ApplicationListener<TradeParamsChangeEvent> {

    @Autowired
    private AliPayServiceCodeBiz aliPayServiceCodeBiz;

    @Autowired
    private WxSettlementIdChangeBiz wxSettlementIdChangeBiz;

    @Autowired
    private ComposeService composeService;

    @Autowired
    private BlueSeaService blueSeaService;

    @Autowired
    private RedisLock redisLock;

    @Override
    public void onApplicationEvent(TradeParamsChangeEvent event) {
        if (PaywayEnum.ALIPAY.getValue().equals(event.getMerchantProviderParams().getPayway())) {
            handleAliPay(event);
        } else if (PaywayEnum.WEIXIN.getValue().equals(event.getMerchantProviderParams().getPayway())) {
            handleWeixin(event);
        }
    }

    private void handleAliPay(TradeParamsChangeEvent event) {
        MerchantProviderParams merchantProviderParams = event.getMerchantProviderParams();

        //如果是支付宝原商户号已开启预授权，默认同步预授权
        aliPayServiceCodeBiz.appendServiceCode(event.getMerchantId(), merchantProviderParams.getPay_merchant_id());

        if (needSync(merchantProviderParams)) {
            CompletableFuture.runAsync(() -> {
                        try {
                            ContractResponse response = blueSeaService.updateAliMchInfoBySubMchId(merchantProviderParams.getPay_merchant_id(), new HashMap());
                            log.info("{} {} 更新成功 {}", merchantProviderParams.getMerchant_sn(), merchantProviderParams.getPay_merchant_id(), response.getMessage());
                        } catch (Exception e) {
                            log.warn("{} {} 更新失败", merchantProviderParams.getMerchant_sn(), merchantProviderParams.getPay_merchant_id(), e);
                        }
                    }
            );
        }
    }

    private void handleWeixin(TradeParamsChangeEvent event) {
        MerchantProviderParams merchantProviderParams = event.getMerchantProviderParams();
        if (event.isCheckSettlement()) {
            wxSettlementIdChangeBiz.handleSettlementId(merchantProviderParams);
        }

        if (needSync(merchantProviderParams)) {
            CompletableFuture.runAsync(() -> {
                        try {
                            ContractResponse response = composeService.updateWxMchInfoBySubMchId(merchantProviderParams.getPay_merchant_id(), new HashMap());
                            log.info("{} {} 更新成功 {}", merchantProviderParams.getMerchant_sn(), merchantProviderParams.getPay_merchant_id(), response.getMessage());
                        } catch (Exception e) {
                            log.warn("{} {} 更新失败", merchantProviderParams.getMerchant_sn(), merchantProviderParams.getPay_merchant_id(), e);
                        }
                    }
            );
        }
    }

    /**
     * 是否需要同步一遍信息给AT
     * 正常基本信息变更只同步给当前在用AT，切换时需要再次同步一遍给正在使用的AT
     *
     * @param merchantProviderParams
     * @return
     */
    private boolean needSync(MerchantProviderParams merchantProviderParams) {
        // 刚进件完成切换不同步
        if (System.currentTimeMillis() - merchantProviderParams.getCtime() < 60 * ONE_MINUTE) {
            return false;
        }
        // 多业务切换时只同步一次
        return redisLock.lock("TradeParamsChangeListener_sync_at_" + merchantProviderParams.getPay_merchant_id(), merchantProviderParams.getPay_merchant_id(), 5 * ONE_MINUTE);
    }
}
