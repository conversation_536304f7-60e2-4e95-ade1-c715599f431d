package com.wosai.upay.job.refactor.model.bo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;

import java.util.Map;

/**
 * 银行账户BO对象
 *
 * <AUTHOR>
 */
@Data
public class MerchantBankAccountBO {
    /**
     * 主键
     */
    @JSONField(name = "id")
    private String id;

    @JSONField(name = "merchant_id")
    private String merchantId;
    /**
     * 账户类型：1：个人账户；2：企业账户
     */
    @JSONField(name = "type")
    private Integer type;
    /**
     * 账户持有人名称
     */
    @JSONField(name = "holder")
    private String holder;
    /**
     * 账户持有人证件类型：1 身份证；2 港澳居民来往内地通行证； 3 台湾居民来往大陆通行证； 4 非中华人民共和国护照； 5 中国护照； 6 港澳居民居住证； 7 台湾居民居住证
     */
    @JSONField(name = "id_type")
    private Integer idType;
    /**
     * 账户持有人证件编号
     */
    @JSONField(name = "identity")
    private String identity;
    /**
     * 开户人证件照正面照片
     */
    @JSONField(name = "holder_id_front_photo")
    private String holderIdFrontPhoto;
    /**
     * 开户人证件照背面照片
     */
    @JSONField(name = "holder_id_back_photo")
    private String holderIdBackPhoto;
    /**
     * 状态: -1未提交 0-待认证;1 - 认证成功 2-认证失败; | 身份证正面OCR认证状态
     */
    @JSONField(name = "holder_id_front_ocr_status")
    private Integer holderIdFrontOcrStatus;
    /**
     * 状态: -1未提交 0-待认证;1 - 认证成功 2-认证失败; | 身份证反面OCR认证状态
     */
    @JSONField(name = "holder_id_back_ocr_status")
    private Integer holderIdBackOcrStatus;
    /**
     * 状态: -1未提交 0-待认证;1 - 认证成功 2-认证失败; | 身份证认证状态
     */
    @JSONField(name = "holder_id_status")
    private Integer holderIdStatus;
    /**
     * 工商税务号
     */
    @JSONField(name = "tax_payer_id")
    private String taxPayerId;
    /**
     * 账号
     */
    @JSONField(name = "number")
    private String number;
    /**
     * 卡号(账号)真实性验证状态 0未验证 1 验证中 2验证有效 3验证失败
     */
    @JSONField(name = "verify_status")
    private Integer verifyStatus;
    /**
     * 开户银行名称
     */
    @JSONField(name = "bank_name")
    private String bankName;
    /**
     * 分支行名称
     */
    @JSONField(name = "branch_name")
    private String branchName;
    /**
     * 银行卡有效期
     */
    @JSONField(name = "card_validity")
    private String cardValidity;
    /**
     * 银行卡证件有效期开始时间
     */
    @JSONField(name = "card_validity")
    private String cardIdValidityStartDate;
    /**
     * 银行卡证件有效期结束时间
     */
    @JSONField(name = "card_validity")
    private String cardIdValidityEndDate;
    /**
     * 扩展字段
     */
    @JSONField(name = "extend")
    private Map extend;
    /**
     * 清算行号
     */
    @JSONField(name = "clearing_number")
    private String clearingNumber;
    /**
     * 开户行号
     */
    @JSONField(name = "opening_number")
    private String openingNumber;
    /**
     * 分支行所在城市
     */
    @JSONField(name = "city")
    private String city;
    /**
     * 和账号绑定的手机号
     */
    @JSONField(name = "cellphone")
    private String cellphone;
    /**
     * 扩展字段
     */
    @JSONField(name = "extra")
    private Map extra;
    /**
     * 换卡时间
     */
    @JSONField(name = "change_time")
    private Long changeTime;
    /**
     * 对私: 表示银行卡照片，对公: 表示开户许可证
     */
    @JSONField(name = "bank_card_image")
    private String bankCardImage;
    /**
     * 转账凭证
     */
    @JSONField(name = "transfer_voucher")
    private String transferVoucher;
    /**
     * 证件有效期
     */
    @JSONField(name = "id_validity")
    private String idValidity;
    /**
     * 营业执照授权函
     */
    @JSONField(name = "letter_of_authorization")
    private String letterOfAuthorization;
    /**
     * 辅助证明材料，最多三张图片url，用逗号隔开
     */
    @JSONField(name = "hand_letter_of_authorization")
    private String handLetterOfAuthorization;

    @JSONField(name = "relation_or_legal_rep_held_auth")
    private String relationOrLegalRepHeldAuth;
    /**
     * 银行卡照片/开户许可证审核状态 -1: 未提交; 0: 待认证; 1: 认证成功; 2: 认证失败;
     */
    @JSONField(name = "bank_card_status")
    private Integer bankCardStatus;
    /**
     * 身份证住址
     */
    @JSONField(name = "holder_id_card_address")
    private String holderIdCardAddress;
    /**
     * 身份证签发机关
     */
    @JSONField(name = "holder_id_card_issuing_authority")
    private String holderIdCardIssuingAuthority;
    /**
     * 创建时间
     */
    @JSONField(name = "ctime")
    private Long ctime;
    /**
     * 更新时间
     */
    @JSONField(name = "mtime")
    private Long mtime;
    /**
     * 删除状态
     */
    @JSONField(name = "deleted")
    private Integer deleted;
    /**
     * 版本号
     */
    @JSONField(name = "version")
    private Long version;

    /**
     * 额外修改字段
     */
    @JSONField(name = "change_extra")
    private Map changeExtra;

    /**
     * 法人姓名
     */
    @JSONField(name = "legal_person_name")
    private String legalPersonName;

    /**
     * 法人注册号
     */
    @JSONField(name = "legal_person_register_no")
    private String legalPersonRegisterNo;

    /**
     * 营业执照照片
     */
    @JSONField(name = "business_license_photo")
    private String businessLicensePhoto;

    /**
     * 商户经营名称
     */
    @JSONField(name = "business_name")
    private String businessName;

    /**
     * 是否为当前商户正在使用的银行卡 (0: 否，1: 是)
     */
    @JSONField(name = "default_status")
    private Integer defaultStatus;


    public Map toMap() {
        return JSON.parseObject(JSON.toJSONString(this));
    }


}

