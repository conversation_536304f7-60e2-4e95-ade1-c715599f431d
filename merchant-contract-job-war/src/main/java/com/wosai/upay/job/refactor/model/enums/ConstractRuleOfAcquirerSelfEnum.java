package com.wosai.upay.job.refactor.model.enums;


/**
 * 收单机构报备唯一标识类型枚举
 *
 * <AUTHOR>
 */
public enum ConstractRuleOfAcquirerSelfEnum {


    /**
     * 拉卡拉
     */
    LKL("lkl"),

    /**
     * 通联
     */
    TONG_LIAN("tonglian"),

    /**
     * 拉卡拉v3
     */
    LKL_V3( "lklV3"),

    /**
     * 华夏银行
     */
    HXB("hxb");

    ConstractRuleOfAcquirerSelfEnum(String text) {
        this.text = text;
    }
    private final String text;


    public String getText() {
        return this.text;
    }

}
