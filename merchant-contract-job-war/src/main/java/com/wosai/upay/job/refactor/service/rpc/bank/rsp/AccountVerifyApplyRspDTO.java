package com.wosai.upay.job.refactor.service.rpc.bank.rsp;

import lombok.Data;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2025/2/19 14:58
 */
@Data
public class AccountVerifyApplyRspDTO {

    /**
     * 待验证
     */
    public static final int STATUS_PENDING = 0;

    /**
     * 验证中
     */
    public static final int STATUS_PROGRESSING = 1;

    /**
     * 验证成功
     */
    public static final int STATUS_SUCCESS = 2;

    /**
     * 验证失败
     */
    public static final int STATUS_FAIL = 3;

    /**
     * 等待金额验证
     */
    public static final int STATUS_WAIT_FOR_VERIFY = 4;

    /**
     * 代付失败
     */
    public static final int STATUS_PAYMENT_FAIL = 5;

    /**
     * 银行退单
     */
    public static final int STATUS_BANK_CHARGEBACK_FAIL = 6;

    /**
     * 验证业务方唯一编号
     */
    private String businessId;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 账户类型：1：个人账户；2：企业账户
     */
    private Integer cardType;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 证件类型
     */
    private Integer idType;

    /**
     * 验证类型 1.三要素 2.个人代付 3.对公代付
     */
    private Integer verifyType;

    /**
     * 验证状态
     */
    private Integer status;

    /**
     * 外部验证单号
     */
    private String outBizId;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 响应参数
     */
    private String response;

    /**
     * 失败原因
     */
    private String failMsg;

    /**
     * 等待回填金额验证
     * 如果状态是等待金额验证 此后状态可能不再变更
     *
     * @return true-等待回填金额验证，false-不需要回填金额验证
     */
    public boolean waitForBackFillAmountStatus() {
        return STATUS_WAIT_FOR_VERIFY == status;
    }

    /**
     * 任务成功
     */
    public boolean taskSuccess() {
        return STATUS_SUCCESS == status;
    }

    /**
     * 任务失败
     */
    public boolean taskFail() {
        return STATUS_FAIL == status || STATUS_PAYMENT_FAIL == status || STATUS_BANK_CHARGEBACK_FAIL == status;
    }

    /**
     * 等待验证或者验证中
     */
    public boolean waitForVerifyOrProgressing() {
        return STATUS_PENDING == status || STATUS_PROGRESSING == status;
    }
}
