package com.wosai.upay.job.refactor.model.converter;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.dto.ContractRuleCustomDto;
import com.wosai.upay.job.model.dto.ContractRuleDto;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
public class McContractRuleConverter {

    public static ContractRuleDto contractRuleDOToContractRuleDto(McContractRuleDO mcContractRuleDO) {
        if (mcContractRuleDO == null) {
            return null;
        }
        ContractRuleDto dto = new ContractRuleDto();
        BeanUtils.copyProperties(mcContractRuleDO, dto);

        dto.setIs_default(mcContractRuleDO.getIsDefault());
        dto.setIs_insert(mcContractRuleDO.getIsInsert());
        dto.setIs_insert_influ_ptask(mcContractRuleDO.getIsInsertInfluPtask());
        dto.setIs_update(mcContractRuleDO.getIsUpdate());
        dto.setIs_update_influ_ptask(mcContractRuleDO.getIsUpdateInfluPtask());
        dto.setCreate_at(mcContractRuleDO.getCreateAt());
        dto.setUpdate_at(mcContractRuleDO.getUpdateAt());
        if (WosaiStringUtils.isNotEmpty(mcContractRuleDO.getMetadata())) {
            dto.setMetadata(JSON.parseObject(mcContractRuleDO.getMetadata()));
        }
        return dto;
    }

    public static McContractRuleDO contractRuleDtoToContractRuleDO(ContractRuleDto dto) {
        if (dto == null) {
            return null;
        }
        McContractRuleDO ruleDO = new McContractRuleDO();
        BeanUtils.copyProperties(dto, ruleDO);

        ruleDO.setIsDefault(dto.getIs_default());
        ruleDO.setIsInsert(dto.getIs_insert());
        ruleDO.setIsInsertInfluPtask(dto.getIs_insert_influ_ptask());
        ruleDO.setIsUpdate(dto.getIs_update());
        ruleDO.setIsUpdateInfluPtask(dto.getIs_update_influ_ptask());
        ruleDO.setCreateAt(dto.getCreate_at());
        ruleDO.setUpdateAt(dto.getUpdate_at());
        if (WosaiMapUtils.isNotEmpty(dto.getMetadata())) {
            ruleDO.setMetadata(JSON.toJSONString(dto.getMetadata()));
        }
        return ruleDO;
    }

    public static ContractRuleCustomDto contractRuleDOToContractRuleCustomDto(McContractRuleDO mcContractRuleDO) {
        if (mcContractRuleDO == null) {
            return null;
        }
        ContractRuleCustomDto dto = new ContractRuleCustomDto();
        BeanUtils.copyProperties(mcContractRuleDO, dto);
        
        dto.setIs_default(mcContractRuleDO.getIsDefault());
        dto.setIs_insert(mcContractRuleDO.getIsInsert());
        dto.setIs_insert_influ_ptask(mcContractRuleDO.getIsInsertInfluPtask());
        dto.setIs_update(mcContractRuleDO.getIsUpdate());
        dto.setIs_update_influ_ptask(mcContractRuleDO.getIsUpdateInfluPtask());
        dto.setCreate_at(mcContractRuleDO.getCreateAt());
        dto.setUpdate_at(mcContractRuleDO.getUpdateAt());
        if (WosaiStringUtils.isNotEmpty(mcContractRuleDO.getMetadata())) {
            dto.setMetadata(JSON.parseObject(mcContractRuleDO.getMetadata()));
        }
        return dto;
    }
}
