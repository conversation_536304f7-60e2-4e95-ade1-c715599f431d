package com.wosai.upay.job.providers;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.terminal.basic.TerminalBasicInsertEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;
import com.wosai.upay.core.bean.request.TradeExtConfigCreateRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigQueryRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigUpdateRequest;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandDetailInfoQueryResp;
import com.wosai.upay.job.externalservice.brand.model.BrandMerchantInfoQueryResp;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.event.UpdateMerchantBankAccountEvent;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsExtDO;
import com.wosai.upay.job.service.MobilePosServiceImpl;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import com.wosai.upay.model.direct.GetDevParamReq;
import com.wosai.upay.service.CrmEdgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description: lklv3
 * <AUTHOR>
 * @Date 2021/4/21 10:16 上午
 **/
@Slf4j
@Component(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
public class LklV3Provider extends AbstractProvider {

    @Autowired
    LklV3Service lklV3Service;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    LklV3ShopTermBiz lklV3ShopTermBiz;


    @Autowired
    LklV3ShopTermMapper mapper;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    CrmEdgeService crmEdgeService;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Autowired
    private ForeignCardMapper foreignCardMapper;

    @Value("${lklMobilePos.devCode}")
    private String lklMobilePosDevCode;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private BrandBusinessClient brandBusinessClient;


    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        int influPtask = AcquirerTypeEnum.LKL_V3.getValue().equals(acquirer) || isSubBiz(merchantSn, AcquirerTypeEnum.LKL_V3.getValue()) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setRetry(0);
        Integer taskType = null;
        if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
            Map requestParam = MapUtils.getMap(paramContext, "cardRequestParam");
            if (!CollectionUtils.isEmpty(requestParam)) {
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
            } else {
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
            }
        } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
        } else if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()) {
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE;
        } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {
            String crmUpdate = MapUtils.getString(paramContext, "crmUpdate");
            if (StringUtils.isNotEmpty(crmUpdate)) {
                if ("0".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
                } else if ("1".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                } else if ("2".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH;
                }
            }
        } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
            if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
                //更新营业执照
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
            }
        }
        if (Objects.nonNull(taskType)) {
            return subTask.setTask_type(taskType);
        } else {
            return null;
        }
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (needPayFor(contextParam, sub, contractTask)) {
            return null;
        }
        LklV3Param lklV3Param = buildParam(contractChannel, sub, LklV3Param.class);
        ContractResponse contractResponse = lklV3Service.contractMerchantWithParams(contextParam, lklV3Param);
        String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
        updateClearProvider(merchantId, sub.getMerchant_sn());
        return contractResponse;
    }


    /**
     * 处理增网增终新增任务
     *
     * @param event
     * @return
     */
    @Override
    public void produceInsertTerminalTaskByRule(TerminalBasicInsertEvent event, MerchantInfo merchant) {
        String merchantSn = merchant.getSn();
        String terminalId = event.getTerminalId();
        String merchantId = event.getData().getMerchantId();
        final String termType = applicationApolloConfig.getLklV3AddTermType();
        if (!termType.contains(String.valueOf(event.getData().getType()))) {
            return;
        }
        String storeId = event.getData().getStoreId();
        StoreInfo store = storeService.getStoreById(storeId, devCode);
        if (Objects.nonNull(lklV3ShopTermBiz.getTermInfo(store.getSn(), terminalId))) {
            return;
        }
        Tuple2<Integer, Long> shopContract = lklV3ShopTermBiz.shopContract(store.getSn());
        if (Objects.isNull(shopContract)) {
            log.info("doInsert商户:{},门店号:{},还没有创建对应的拉卡拉门店", merchantId,store.getSn());
            return;
        }
        Map context = CollectionUtil.hashMap(CommonModel.STORE_SN, store.getSn(),
                "terminalId", terminalId,
                ParamContextBiz.MERCHANT_FEE_RATES, getFeeRate(merchantId)
        );
        //终端校验
        Map terminal = terminalService.getTerminalByTerminalId(terminalId);
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        //简易智能新pos
        List<String> simpleSuperPosList = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
        Boolean simpleSuperPos = simpleSuperPosList.contains(vendorAppAppid);
        if (simpleSuperPos) {
            //产品说绑定一定在银行卡业务开通以后,所以可以拿到绑定银行卡pos的费率
            final ApiRequestParam param = new ApiRequestParam();
            final GetDevParamReq devParamReq = new GetDevParamReq();
            devParamReq.setDevCode("GF7GNKL9O48N");
            devParamReq.setMerchantId(merchantId);
            param.setBodyParams(devParamReq);
            final Map devParam = crmEdgeService.getDevParam(param);
            Map feeMap = (Map) devParam.get("feeMap");
            log.info("商户号merchantSn:{},终端Id:{},绑定拉卡拉银行卡终端费率:{}", merchantSn, terminalId, JSONObject.toJSONString(feeMap));
            //T9 POS终端绑定报备lkl费率兜底
            if (CollectionUtils.isEmpty(feeMap)) {
                feeMap = JSONObject.parseObject("{\"credit\":{\"fee\":\"0.55\"},\"debit\":{\"fee\":\"0.55\"}}", Map.class);
            }
            //银行卡费率
            final List<Map> bankFeeList = lklV3ShopTermBiz.getLklBankFeeList(feeMap);
            //原有费率
            final List<Map> merchantFeeRates = (List<Map>) context.get(ParamContextBiz.MERCHANT_FEE_RATES);
            merchantFeeRates.addAll(bankFeeList);
            context.put(ParamContextBiz.MERCHANT_FEE_RATES, merchantFeeRates);
        }

        ContractTask task = new ContractTask().setMerchant_sn(merchantSn).setMerchant_name(merchant.getName()).setStatus(0).setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                .setType(ProviderUtil.CONTRACT_TYPE_ADDTERM).setAffect_status_success_task_count(0).setAffect_sub_task_count(1).setEvent_context(JSON.toJSONString(context));
        contractTaskBiz.insert(task);
        ContractSubTask subTask = new ContractSubTask()
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM)
                .setStatus_influ_p_task(1)
                .setChannel(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
                .setMerchant_sn(merchant.getSn())
                .setContract_rule(McConstant.RULE_GROUP_LKLV3)
                .setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                .setSchedule_dep_task_id(shopContract.get_2())
                .setSchedule_status(shopContract.get_1())
                .setP_task_id(task.getId());
        contractSubTaskMapper.insert(subTask);
        lklV3ShopTermBiz.addTerm(store.getSn(), terminalId, subTask.getId());
    }

    @Override
    public ContractResponse addTerminal(ContractTask task, ContractSubTask subTask){
        Map context = JSONObject.parseObject(task.getEvent_context(), Map.class);
        List feeData = (List) MapUtils.getObject(context, ParamContextBiz.MERCHANT_FEE_RATES);
        ContractResponse contractResponse;
        LklV3Param v3Param = contractParamsBiz.buildContractParamsByContractSubTask(subTask, LklV3Param.class);
        //todo 创建这两个task 的时候要 设定好storesn， 编造好terminalSn
        String storeSn = MapUtils.getString(context, CommonModel.STORE_SN);
        String terminalId = MapUtils.getString(context, "terminalId");
        if (ProviderUtil.SUB_TASK_TASK_TYPE_ADD_SHOP.equals(subTask.getTask_type())) {
            String payMerchantId = WosaiMapUtils.getString(context, ParamContextBiz.PAY_MERCHANT_ID);
            contractResponse = lklV3Service.addShop(
                    WosaiStringUtils.isEmpty(payMerchantId) ? merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(task.getMerchant_sn(), ProviderEnum.PROVIDER_LAKALA_V3.getValue()).getPay_merchant_id() : payMerchantId
                    , feeData, storeSn, v3Param);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_BRAND_ADD_SHOP.equals(subTask.getTask_type())) {
            contractResponse = lklV3Service.addShop(
                    WosaiMapUtils.getString(context, ParamContextBiz.BRAND_PAY_MERCHANT_ID),
                    feeData, storeSn, v3Param);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM.equals(subTask.getTask_type())) {
            contractResponse = lklV3Service.addTerm(
                    merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(task.getMerchant_sn(), ProviderEnum.PROVIDER_LAKALA_V3.getValue()).getPay_merchant_id(),
                    feeData,
                    lklV3ShopTermBiz.getShopId(MapUtils.getString(context, CommonModel.STORE_SN)),
                    terminalId,
                    v3Param);
        } else {
            //处理拉卡拉终端解绑
            final String termNo = lklV3ShopTermBiz.getTermNo(storeSn, terminalId);
            if(StringUtils.isNotBlank(termNo)) {
                contractResponse = lklV3Service.updateTermStatusToLkl(
                        merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(task.getMerchant_sn(), ProviderEnum.PROVIDER_LAKALA_V3.getValue()).getPay_merchant_id()
                        , termNo, 0, v3Param);
            }else {
                contractResponse = new ContractResponse().setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage("拉卡拉终端还未审核通过");
            }

        }
        if(contractResponse.isBusinessFail()) {
            //调用失败的时候
            if (context.containsKey("terminalId") &&
                    StrUtil.contains(MapUtils.getString(context,"terminalId"), MobilePosServiceImpl.LKL_MOBILE_POS_TERMINAL_ID)) {
                //开通中的手机POS置为失败
                final ForeignCard foreignCard = foreignCardMapper.selectByMerchantSnAndCode(task.getMerchant_sn(), lklMobilePosDevCode);
                if(Objects.nonNull(foreignCard) && Objects.equals(foreignCard.getStatus(),ForeignCard.STATUS_PROCESS)) {
                    foreignCardMapper.updateByPrimaryKeySelective(new ForeignCard().setId(foreignCard.getId()).setStatus(ForeignCard.STATUS_FAIL));
                }
            }
        }
        if (contractResponse.isSuccess() && ProviderUtil.SUB_TASK_TASK_TYPE_UNBIND_TERM.equals(subTask.getTask_type())) {
            lklV3ShopTermBiz.unbindTerm(storeSn, terminalId);
        }
        return contractResponse;
     }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(contractTask.getType()) && needPayFor(contextParam, sub, contractTask)) {
            return null;
        }
        if (ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE.equals(contractTask.getType())
                && sub.getTask_type().equals(ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE)
                && needPayFor(contextParam, sub, contractTask)) {
            return null;
        }
        ContractResponse response = null;
        String merchantSn = sub.getMerchant_sn();
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        if (Objects.isNull(acquirerParams)) {
            return null;
        }
        String merInnerNo = acquirerParams.getPay_merchant_id();
        LklV3Param lklV3Param = buildParam(contractChannel, sub, LklV3Param.class);
        Integer taskType = sub.getTask_type();
        if (ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(taskType)) {
            response = lklV3Service.updateMerchantBasic(merInnerNo, contextParam, lklV3Param);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(taskType) || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(taskType)) {
            response = lklV3Service.updateMerchantBankAccount(merInnerNo, contextParam, lklV3Param);
            if (response.isSuccess()) {
                applicationEventPublisher.publishEvent(new UpdateMerchantBankAccountEvent(this,
                        sub.getId(),
                        ProviderEnum.PROVIDER_LKLORG.getValue(),
                        ProviderUtil.LKL_ORG_PROVIDER_CHANNEL));
            }
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(taskType)) {
            Object merchantFeeRates = MapUtils.getObject(contextParam, ParamContextBiz.MERCHANT_FEE_RATES);
            if (JSON.toJSONString(merchantFeeRates).contains(LakalaConstant.FEERATE_TYPECODE)) {
                response = lklV3Service.updateMerchantFeerate(merInnerNo, contextParam, lklV3Param);
            } else {
                String merchantId = (String) BeanUtil.getNestedProperty(contextParam, "merchant.id");
                List feeRatesV3 = getFeeRate(merchantId);
                contextParam.put(ParamContextBiz.MERCHANT_FEE_RATES, feeRatesV3);
                response = lklV3Service.updateMerchantFeerate(merInnerNo, contextParam, lklV3Param);
            }
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE.equals(taskType)) {
            Integer status = MapUtils.getInteger(MapUtils.getMap(contextParam, "merchant"), Merchant.STATUS);
            response = lklV3Service.updateMerchantStatusToLkl(merInnerNo, status, lklV3Param);
            if (response.isSuccess() && Merchant.STATUS_ENABLED == status) {
                List<LklV3Term> unbinds = lklV3ShopTermBiz.getUnbindLklV3Term(merchantSn);
                if (!StringUtil.listEmpty(unbinds)) {
                    unbinds.forEach(unbind -> {
                        final String termNo = unbind.getTermNo();
                        if(StringUtils.isNotBlank(termNo)) {
                            lklV3Service.updateTermStatusToLkl(merInnerNo, termNo, ValidStatusEnum.INVALID.getValue(), lklV3Param);
                        }
                    });
                }
            }
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(taskType)) {
            response = lklV3Service.updateByCrm(merInnerNo, contextParam, lklV3Param);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(taskType)) {
            response = lklV3Service.updateMerchantBusinessLicense(merInnerNo, contextParam, lklV3Param);
            // 营业执照信息变更也变更了结算信息
            if (response.isSuccess()) {
                if (isUpdateLicenseNeedUpdateAccount(contextParam)) {
                    applicationEventPublisher.publishEvent(new UpdateMerchantBankAccountEvent(this,
                            sub.getId(),
                            ProviderEnum.PROVIDER_LKLORG.getValue(),
                            ProviderUtil.LKL_ORG_PROVIDER_CHANNEL));
                }
            }
        }
        if (Objects.nonNull(response) && 200 == response.getCode()) {
            sub.setContract_id(MapUtils.getString(response.getTradeParam(), LakalaConstant.CONTRACTID));
        }
        return response;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        throw new ContractBizException(getProviderBeanName() + "暂不支持配置微信appid");
    }

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    /**
     * 将跟随 失败任务[新增商户入网] 的 增商增终任务 切换至成功的subTask下
     * todo 不一定这么做 尚未测试
     *
     * @param merchantSn
     * @param taskId
     */
    public void updateAddShopAndTermTask(String merchantSn, Long taskId) {
        List<ContractTask> contracts = contractTaskMapper.getContractsBySnAndType(merchantSn, ProviderUtil.CONTRACT_TYPE_INSERT);
        if (contracts.size() == 1) {
            return;
        }
        //失败的入网任务
        List<ContractTask> failedTasks = contracts.stream().filter(
                contract -> contract.getRule_group_id().contains(McConstant.RULE_GROUP_LKLV3) && TaskStatus.FAIL.getVal().equals(contract.getStatus())
        ).collect(Collectors.toList());
        if (StringUtil.listEmpty(failedTasks)) {
            return;
        }
        //失败的入网任务的 subtask
        List<ContractSubTask> failedSubTasks = failedTasks.stream().map(task -> contractSubTaskMapper.selectLKLV3TaskByPTaskId(task.getId())).collect(Collectors.toList());
        //失败的入网环境 的 shop term subTask
        failedSubTasks.forEach(failedSubTask -> {
            List<ContractSubTask> subTasks = contractSubTaskMapper.selectShopAndTermSubTaskByDependTaskId(failedSubTask.getId());
            subTasks.forEach(subTask -> {
                contractSubTaskMapper.updateShopTermSubTask(subTask.getId(), taskId);
            });
        });
    }

    @Override
    public void handleSqbTerminalBind(MerchantInfo merchant, Map terminal, Integer provider) {
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        final String terminalId = BeanUtil.getPropString(terminal, DaoConstants.ID);
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        //provider_terminal 不存在该门店的终端记录
        final ProviderTerminal providerTerminal = providerTerminalBiz.existProviderTerminal(provider, storeSn, merchantSn, terminalSn, acquirerParams.getPay_merchant_id());
        //判断是否需要插入绑定任务
        if (Objects.isNull(providerTerminal)) {
            final LklV3ShopTerm lklV3ShopTerm = lklV3ShopTermBiz.selectLklV3ShopTermByStoreSn(storeSn);
            //lklV3还未回调成功
            if (StringUtils.isEmpty(Optional.ofNullable(lklV3ShopTerm).orElseGet(LklV3ShopTerm::new).getShopId())) {
                return;
            }
            //简易智能新pos返回两个终端号但是只需要绑定普通的那个终端号
            List<String> simpleSuperPosList = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
            Boolean simpleSuperPos = simpleSuperPosList.contains(vendorAppAppid);
            LklV3Term v3Term;
            if (simpleSuperPos) {
                v3Term = lklV3ShopTerm.getLklV3TermInfo().parallelStream().filter(lklV3Term -> Objects.equals(lklV3Term.getDevSerialNo(), terminalId) && Objects.equals(lklV3Term.getBusiTypeCode(), "QR_CODE_CARD") && StringUtils.isNotBlank(lklV3Term.getTermNo())).findAny().orElseGet(LklV3Term::new);
            } else {
                v3Term = lklV3ShopTerm.getLklV3TermInfo().parallelStream().filter(lklV3Term -> Objects.equals(lklV3Term.getDevSerialNo(), terminalId) && StringUtils.isNotBlank(lklV3Term.getTermNo())).findAny().orElseGet(LklV3Term::new);
            }
            final String providerTerminalId = v3Term.getTermNo();
            if (Objects.isNull(providerTerminalId)) {
                return;
            }
            providerTerminalBiz.sqbTerminalConnectionProviderTerminal(merchantSn, providerTerminalId, acquirerParams.getPay_merchant_id(), ProviderEnum.PROVIDER_LAKALA_V3.getValue(), vendorAppAppid, terminalSn, storeSn);
            log.info("终端绑定=>新创建终端Id:{},门店:{},交易参数:{}", providerTerminalId, storeSn, JSONObject.toJSONString(params));
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(), param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                            providerTerminalId,
                            storeSn,
                            terminalSn));
            return;
        }

        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("终端绑定=>已经存在收钱吧终端Id:{},门店:{},交易参数:{}", providerTerminal.getProvider_terminal_id(), storeSn, JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(), param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        terminalSn));

    }

    @Override
    public void handleSqbTerminalUnBind(MerchantInfo merchant, Map terminal, Integer provider) {
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        //provider_terminal 不存在该门店的终端记录
        final ProviderTerminal providerTerminal = providerTerminalBiz.existProviderTerminal(provider, storeSn, merchantSn, terminalSn, acquirerParams.getPay_merchant_id());
        //无需解绑
        if (Objects.isNull(providerTerminal)) {
            return;
        }
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        //判断是否需要插入解绑任务
        final List<MerchantProviderParams> needDeleteMerchantProviderParams = providerTerminalBiz.getNeedDeleteMerchantProviderParams(providerTerminal, params);
        needDeleteMerchantProviderParams.stream().forEach(
                param -> providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(), param.getPayway(),
                        ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        providerTerminal.getStore_sn(),
                        terminalSn)
        );
    }


    @Override
    public void handleSqbMerchantProviderTerminal(String merchantSn, Integer provider) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        final ProviderTerminal providerTerminal = providerTerminalBiz.existMerchantProviderTerminal(provider, merchantSn, acquirerParams.getPay_merchant_id());
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        //不存在则绑定
        final String termNo = getMerchantTermNo(merchantSn);
        if (StringUtils.isEmpty(termNo)) {
            log.info("lklV3商户绑定=>商户号:{},终端Id找不到", merchantSn);
            return;
        }
        if (Objects.isNull(providerTerminal)) {
            providerTerminalBiz.merchantConnectionProviderTerminal(merchantSn, termNo, acquirerParams.getPay_merchant_id(), provider);
            log.info("lklV3商户绑定=>商户号:{},不存在商户级别,现在开始重新绑定,终端Id:{},交易参数:{}", merchantSn, termNo, JSONObject.toJSONString(params));
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(),
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                            termNo,
                            null,
                            null));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        if (!Objects.equals(termNo, providerTerminal.getProvider_terminal_id())) {
            log.info("lklV3商户绑定=>商户号:{},两次终端不一致请排查,原始终端Id:{},任务终端Id:{}", merchantSn, termNo, providerTerminal.getProvider_terminal_id());
            return;
        }
        log.info("lklV3商户绑定=>商户号:{},已经存在终端新增子商户号,终端Id:{},交易参数:{}", merchantSn, providerTerminal.getProvider_terminal_id(), JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(),
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        null,
                        null)
        );
    }


    public void handleSqbMerchantProviderTerminal(String merchantSn, String acquirerMerchantId,  Integer provider, String termNo) {
        final ProviderTerminal providerTerminal = providerTerminalBiz.existMerchantProviderTerminal(provider, merchantSn, acquirerMerchantId);
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if (StringUtils.isEmpty(termNo)) {
            log.info("lklV3商户绑定=>商户号:{},终端Id找不到", merchantSn);
            return;
        }
        if (Objects.isNull(providerTerminal)) {
            providerTerminalBiz.merchantConnectionProviderTerminal(merchantSn, termNo, acquirerMerchantId, provider);
            log.info("lklV3商户绑定=>商户号:{},不存在商户级别,现在开始重新绑定,终端Id:{},交易参数:{}", merchantSn, termNo, JSONObject.toJSONString(params));
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(),
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                            termNo,
                            null,
                            null));
            return;
        }
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        if (!Objects.equals(termNo, providerTerminal.getProvider_terminal_id())) {
            log.info("lklV3商户绑定=>商户号:{},两次终端不一致请排查,原始终端Id:{},任务终端Id:{}", merchantSn, termNo, providerTerminal.getProvider_terminal_id());
            return;
        }
        log.info("lklV3商户绑定=>商户号:{},已经存在终端新增子商户号,终端Id:{},交易参数:{}", merchantSn, providerTerminal.getProvider_terminal_id(), JSONObject.toJSONString(merchantProviderParams));
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(),
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        null,
                        null)
        );
    }

    @Override
    public ContractResponse queryMerchantContractResult(String providerMerchantId) {
        LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        ContractResponse contractResponse = lklV3Service.queryMerchantContractResultV2(providerMerchantId, lklV3Param);
        if (contractResponse.isSuccess()) {
            Map respData = WosaiMapUtils.getMap(contractResponse.getResponseParam(), LakalaConstant.RESPDATA);
            String status = WosaiMapUtils.getString(respData, LakalaConstant.STATUS);
            if ("SUCCESS".equals(status) || "U_SUCCESS".equals(status)) {
                contractResponse.setTradeParam(CollectionUtil.hashMap("status", MerchantProviderParamsExtDO.UNION_PAY_SUCCESS));
            } else if ("FAIL".equals(status) || "U_FAIL".equals(status)) {
                contractResponse.setTradeParam(CollectionUtil.hashMap("status", MerchantProviderParamsExtDO.UNION_PAY_FAIL));
            } else if ("D_SUCCESS".equals(status)) {
                contractResponse.setTradeParam(CollectionUtil.hashMap("status", MerchantProviderParamsExtDO.UNION_PAY_DELETE));
            } else {
                throw new ContractBizException("未知开通状态");
            }
        }
        return contractResponse;
    }

    @Override
    public ContractResponse queryTermContractResult(String merCupNo, String termNo) {
        LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        return lklV3Service.queryTermContractResultV2(merCupNo, termNo, lklV3Param);
    }

    @Override
    public void handleSqbStoreTerminal(String storeSn, String merchantSn, Integer provider) {
        log.info("拉卡拉门店终端处理: {} {}", merchantSn, storeSn);
        // 判断是不是品牌商户
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        BrandMerchantInfoQueryResp brandMerchantInfoQueryResp = brandBusinessClient.getBrandMerchantInfoByMerchantId(WosaiMapUtils.getString(merchant, DaoConstants.ID));
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        // 如果是品牌商户 && 是品牌支付模式 =》走品牌的终端绑定逻辑，生成两个终端号，一个和自己的子商户号做绑定，一个和品牌主商户的子商户号做绑定
        if (brandMerchantInfoQueryResp.isSubBrandMerchant() && brandMerchantInfoQueryResp.isBrandPayMode()) {
            log.info("拉卡拉品牌门店终端处理: {} {}", merchantSn, storeSn);
            doHandleBrandStoreTerminal(merchant, storeSn, provider, brandMerchantInfoQueryResp.getBrandId(), acquirerParams);
            return;
        }
        //provider_terminal 是否存在该门店的终端记录
        final ProviderTerminal providerTerminal = providerTerminalBiz.getStoreProviderTerminal(provider, storeSn, merchantSn, acquirerParams.getPay_merchant_id());
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        //不存在则绑定
        if (Objects.isNull(providerTerminal)) {
            final LklV3ShopTerm lklV3ShopTerm = mapper.selectByStoreSnAndMerInnerNo(storeSn, acquirerParams.getPay_merchant_id());
            //lklV3还未回调成功
            if (StringUtils.isEmpty(Optional.ofNullable(lklV3ShopTerm).orElseGet(LklV3ShopTerm::new).getShopId())) {
                return;
            }
            final LklV3Term v3Term = lklV3ShopTerm.getLklV3TermInfo().parallelStream()
                    .filter(lklV3Term -> Objects.equals(lklV3Term.getDevSerialNo(), storeSn) && StringUtils.isNotBlank(lklV3Term.getTermNo()))
                    .findAny()
                    .orElseGet(LklV3Term::new);
            if (StringUtils.isBlank(v3Term.getTermNo()) || v3Term.getTermNo().length() > 8) {
                log.info("lklV3门店绑定v3Term信息异常:{},门店号:{},provider:{}", JSONObject.toJSONString(v3Term), storeSn, provider);
                return;
            }
            final String providerTerminalId = v3Term.getTermNo();
            providerTerminalBiz.sqbStoreTerminalConnectionProviderTerminal(merchantSn, providerTerminalId, acquirerParams.getPay_merchant_id(), ProviderEnum.PROVIDER_LAKALA_V3.getValue(), storeSn);
            log.info("lklV3门店绑定=>新创建终端Id:{},门店:{},交易参数:{}", providerTerminalId, storeSn, JSONObject.toJSONString(params));
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(),
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                            providerTerminalId,
                            storeSn,
                            null));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("lklV3门店绑定=>已经存在终端Id:{},门店:{},交易参数:{}", providerTerminal.getProvider_terminal_id(), storeSn, JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(),
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        null)
        );
    }

    private void doHandleBrandStoreTerminal(Map merchant, String storeSn, Integer provider, String brandId, MerchantProviderParams acquirerParams) {
        List<MerchantProviderParams> selfParams = getMerchantProviderParamsByProvider(provider, acquirerParams.getMerchant_sn());
        BrandDetailInfoQueryResp brandDetailInfoQueryResp = brandBusinessClient.getBrandDetailInfoByBrandId(brandId);
        String brandMainMerchantSn = brandDetailInfoQueryResp.getMainMerchantSn();
        MerchantProviderParams brandAcquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(brandMainMerchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        List<MerchantProviderParams> brandParams = getMerchantProviderParamsByProvider(provider, brandMainMerchantSn).stream().filter(r -> PaywayEnum.WEIXIN.getValue().equals(r.getPayway()) || PaywayEnum.ALIPAY.getValue().equals(r.getPayway())).collect(Collectors.toList());
        // 生成绑定任务
        produceBoundTask(selfParams, acquirerParams.getPay_merchant_id(), merchant, provider, storeSn, false);
        produceBoundTask(brandParams, brandAcquirerParams.getPay_merchant_id(), merchant, provider, storeSn, true);
    }

    /**
     * 门店级别终端绑定 将targetParams中的子商户号和acquirerParams的收单机构商户号做绑定
     * @param targetParams 待绑定的参数列表
     * @param acquirerMerchantId 要报备和绑定的收单机构商户号
     * @param merchant 商户信息
     * @param provider 通道
     * @param storeSn 门店号
     */
    private void produceBoundTask(List<MerchantProviderParams> targetParams, String acquirerMerchantId, Map merchant, Integer provider, String storeSn, boolean brand) {
        String merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        ProviderTerminal providerTerminal = providerTerminalBiz.getStoreProviderTerminal(provider, storeSn, merchantSn, acquirerMerchantId);
        if (Objects.isNull(providerTerminal)) {
            final LklV3ShopTerm lklV3ShopTerm = mapper.selectByStoreSnAndMerInnerNo(storeSn, acquirerMerchantId);
            // 说明还没报备过，生成终端报备的任务
            if (Objects.isNull(lklV3ShopTerm) && brand) {
                Map context = CollectionUtil.hashMap(CommonModel.STORE_SN, storeSn,
                        ParamContextBiz.MERCHANT_FEE_RATES, getFeeRate(WosaiMapUtils.getString(merchant, DaoConstants.ID)),
                        ParamContextBiz.BRAND_PAY_MERCHANT_ID, acquirerMerchantId
                );
                Tuple2<Integer, Long> dependId = lklV3ShopTermBiz.merchantContract(merchantSn);
                if (dependId == null) {
                    log.error("未找到商户入网子任务: {}", merchantSn);
                    return;
                }
                ContractTask task = new ContractTask()
                        .setMerchant_sn(merchantSn)
                        .setMerchant_name(WosaiMapUtils.getString(merchant, Merchant.NAME))
                        .setStatus(TaskStatus.PENDING.getVal())
                        .setRule_group_id(AcquirerTypeEnum.LKL_V3.getValue())
                        .setType(ProviderUtil.CONTRACT_TYPE_ADDTERM)
                        .setAffect_status_success_task_count(0)
                        .setAffect_sub_task_count(1)
                        .setEvent_context(JSON.toJSONString(context));
                contractTaskBiz.insert(task);
                ContractSubTask update = new ContractSubTask()
                        .setStatus_influ_p_task(1)
                        .setChannel(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
                        .setMerchant_sn(merchantSn)
                        .setSchedule_dep_task_id(dependId.get_2())
                        .setSchedule_status(dependId.get_1())
                        .setContract_rule(McConstant.RULE_GROUP_LKLV3)
                        .setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                        .setP_task_id(task.getId())
                        .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_BRAND_ADD_SHOP);
                contractSubTaskMapper.insert(update);
                lklV3ShopTermBiz.addShop(merchantSn, storeSn, acquirerMerchantId, update.getId());
                return;
            }
            //lklV3还未回调成功
            if (StringUtils.isEmpty(Optional.ofNullable(lklV3ShopTerm).orElseGet(LklV3ShopTerm::new).getShopId())) {
                return;
            }
            final LklV3Term v3Term = lklV3ShopTerm.getLklV3TermInfo().parallelStream()
                    .filter(lklV3Term -> Objects.equals(lklV3Term.getDevSerialNo(), storeSn) && StringUtils.isNotBlank(lklV3Term.getTermNo()))
                    .findAny()
                    .orElseGet(LklV3Term::new);
            if (StringUtils.isBlank(v3Term.getTermNo()) || v3Term.getTermNo().length() > 8) {
                log.info("lklV3门店绑定v3Term信息异常:{},门店号:{},provider:{}", JSONObject.toJSONString(v3Term), storeSn, provider);
                return;
            }
            final String termNo = v3Term.getTermNo();
            if (brand) {
                handleBrandMerchantProviderTerminal(merchantSn, storeSn, termNo, provider, acquirerMerchantId, targetParams);
            }
            providerTerminalBiz.sqbStoreTerminalConnectionProviderTerminal(merchantSn, termNo, acquirerMerchantId, provider, storeSn);
            targetParams.forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(),
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                            termNo,
                            storeSn,
                            null,
                            brand ? param.getMerchant_sn() : null));
        } else {
            if (brand) {
                handleBrandMerchantProviderTerminal(merchantSn, storeSn, providerTerminal.getProvider_terminal_id(), provider, acquirerMerchantId, targetParams);
            }
            //判断是否需要插入绑定任务
            final List<MerchantProviderParams> notSyncParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, targetParams);
            //已经绑定了不用处理
            if (WosaiCollectionUtils.isNotEmpty(notSyncParams)) {
                notSyncParams.forEach(param ->
                        providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                                param.getPay_merchant_id(),
                                param.getProvider(),
                                param.getPayway(),
                                ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                                providerTerminal.getProvider_terminal_id(),
                                storeSn,
                                null,
                                brand ? param.getMerchant_sn() : null));
            }
        }
    }

    private void handleBrandMerchantProviderTerminal(String merchantSn, String storeSn, String termNo, Integer provider, String acquirerMerchantId, List<MerchantProviderParams> targetParams) {
        // 如果是第一家门店，并且品牌对应的商户级别终端不存在。就去创建
        ProviderTerminal merchantProviderTerminal = providerTerminalBiz.existMerchantProviderTerminal(provider, merchantSn, acquirerMerchantId);
        if (storeSn.equals(storeBiz.getFirstStoreSnByMerchantSn(merchantSn))) {
            if (Objects.isNull(merchantProviderTerminal)) {
                providerTerminalBiz.createMerchantProviderTerminal(merchantSn, termNo, acquirerMerchantId, provider);
            }
            for (MerchantProviderParams brandParam : targetParams) {
                String sn = String.format("%s:%s", merchantSn, brandParam.getPay_merchant_id());
                final TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
                queryRequest.setSn(sn);
                queryRequest.setSnType(TradeExtConfigQueryRequest.SN_TYPE_MERCHANT_SUB_MCH);
                queryRequest.setProvider(provider);
                final TradeExtConfigQueryResponse tradeExtConfig = tradeConfigService.queryTradeExtConfig(queryRequest);
                if (!Objects.isNull(tradeExtConfig)) {
                    TradeExtConfigUpdateRequest updateRequest = new TradeExtConfigUpdateRequest();
                    updateRequest.setSn(sn);
                    updateRequest.setSnType(TradeExtConfigUpdateRequest.SN_TYPE_MERCHANT_SUB_MCH);
                    updateRequest.setProvider(provider);
                    TradeExtConfigContentModel content = new TradeExtConfigContentModel();
                    content.setTermId(termNo);
                    updateRequest.setContent(content);
                    tradeConfigService.updateTradeExtConfig(updateRequest);
                } else {
                    TradeExtConfigCreateRequest request = new TradeExtConfigCreateRequest();
                    request.setSn(sn);
                    request.setSnType(TradeExtConfigCreateRequest.SN_TYPE_MERCHANT_SUB_MCH);
                    request.setProvider(provider);
                    TradeExtConfigContentModel content = new TradeExtConfigContentModel();
                    content.setTermId(termNo);
                    request.setContent(content);
                    tradeConfigService.createTradeExtConfig(request);
                }
            }
        }
    }

    @Override
    public void createProviderTerminal(String merchantSn, Integer provider) {
        doCreateProviderTerminal(merchantSn, provider);
    }

    @Override
    public List getFeeRate(String merchantId) {
        if (StringUtil.empty(merchantId)) {
            return null;
        }
        List<Map> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigs(merchantId);
        Map weixinConfig = null;
        Map alipayConfig = null;
        Map unionConfig = null;
        Map bestPayConfig = null;
        for (Map merchantConfig : merchantConfigs) {
            int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
            if (payway == PaywayEnum.ALIPAY.getValue()) {
                alipayConfig = merchantConfig;
            } else if (payway == PaywayEnum.WEIXIN.getValue()) {
                weixinConfig = merchantConfig;
            } else if (payway == PaywayEnum.UNIONPAY.getValue()) { //银联二维码支付
                unionConfig = merchantConfig;
            } else if (payway == PaywayEnum.BESTPAY.getValue()) {
                bestPayConfig = merchantConfig;
            }
        }
        if (weixinConfig == null || alipayConfig == null) {
            throw new ContextParamException("商户的支付宝或者微信交易配置merchant_config未配置");
        }
        String alipayFeerate = getLadderFee(alipayConfig, MerchantConfig.WAP_FEE_RATE);
        FeeData weixinFeerate = getLadderFeeV2(weixinConfig, MerchantConfig.WAP_FEE_RATE);
        String unionFeeRate = getLadderFee(unionConfig, MerchantConfig.WAP_FEE_RATE);
        String bestPayFeeRate = getLadderFee(bestPayConfig, MerchantConfig.B2C_FEE_RATE);
        List list = Lists.newArrayList(
                CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, LakalaConstant.FEERATE_CODE_ALIPAY, LakalaConstant.FEERATE_PCT, alipayFeerate),
                CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, LakalaConstant.FEERATE_CODE_BESTPAY, LakalaConstant.FEERATE_PCT, bestPayFeeRate)
        );
        //添加微信费率
        handleFee(list, weixinFeerate, TYPE_WEIXIN);
        //云闪付非分级费率
        if (!isLadderFeeRate(unionConfig)) {
            list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "314", LakalaConstant.FEERATE_PCT, unionFeeRate));
            list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "315", LakalaConstant.FEERATE_PCT, unionFeeRate));
            return list;
        }
        //设置lklV3云闪付阶梯费率,详情参见https://jira.wosai-inc.com/browse/CUA-2939
        final List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = JSONObject.parseArray(JSONObject.toJSONString(BeanUtil.getProperty(unionConfig, MerchantConfig.LADDER_FEE_RATES)), ListMchFeeRateResult.LadderFeeRate.class);
        //按照从小到大排序
        ladderFeeRates.sort(Comparator.comparing(ListMchFeeRateResult.LadderFeeRate::getMax, Comparator.nullsLast(Double::compareTo)));
        //以阶梯费率的max属性为key,wap_fee_rate值为value;
        final Map<Double, String> map = ladderFeeRates.stream()
                .collect(Collectors.toMap(entry -> entry.getMax(), entry -> entry.getWapFeeRate(), (k1, k2) -> k1));
        //阶梯费率是否在1000以下
        final boolean match = map.keySet().stream().filter(Objects::nonNull).allMatch(key -> key.compareTo(1000.0) < 0);
        //分级1000以下
        if (match) {
            list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "314", LakalaConstant.FEERATE_PCT, unionFeeRate));
            list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "315", LakalaConstant.FEERATE_PCT, unionFeeRate));
            return list;
        }
        //分级1000以上
        final List<ListMchFeeRateResult.LadderFeeRate> rates = ladderFeeRates.stream()
                .filter(x -> rangeInDefined(1000.0, x.getMin(), x.getMax()))
                .limit(1)
                .collect(Collectors.toList());
        //包含1000块的区间
        final ListMchFeeRateResult.LadderFeeRate rangeInDefinedRate = rates.get(0);
        //1000以下的最大费率
        list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "413", LakalaConstant.FEERATE_PCT, rangeInDefinedRate.getWapFeeRate()));
        list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "414", LakalaConstant.FEERATE_PCT, rangeInDefinedRate.getWapFeeRate()));
        list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "314", LakalaConstant.FEERATE_PCT, unionFeeRate));
        list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "315", LakalaConstant.FEERATE_PCT, unionFeeRate));
        return list;
    }

    @Override
    public String getMerchantTermNo(String merchantSn) {
        final Map merchant = merchantService.getMerchantBySn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map merchantConfig = this.tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        final Object paramObject = Optional.ofNullable(merchantConfig).map(config -> config.get(MerchantConfig.PARAMS)).orElseThrow(() -> new ContractBizException("不存在该商户或者params不存在"));
        Map params = JSON.parseObject(JSON.toJSONString(paramObject), Map.class);
        String lakalaTerminalId = null;
        if (params != null) {
            Map lakalaTradeParams = JSON.parseObject(MapUtils.getString(params, TransactionParam.LAKALA_TRADE_PARAMS));
            lakalaTerminalId = MapUtils.getString(lakalaTradeParams, TransactionParam.LAKALA_TERM_ID);
        }
        return lakalaTerminalId;
    }


    @Override
    public ContractResponse doProcessMicroUpgradeTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        log.info("contractTask id {},ContractSubTask id {}",contractTask.getId(),sub.getId());
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        LklV3Param lklV3Param = buildParam(contractChannel, sub, LklV3Param.class);
        ContractResponse contractResponse = lklV3Service.contractMerchantWithParams(contextParam, lklV3Param);
        return contractResponse;
    }
}