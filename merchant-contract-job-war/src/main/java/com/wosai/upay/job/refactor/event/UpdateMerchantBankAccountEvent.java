package com.wosai.upay.job.refactor.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2023/12/19
 */
@Getter
public class UpdateMerchantBankAccountEvent extends ApplicationEvent {

    /**
     * 更新银行卡子任务的ID
     */
    private Long subTaskId;

    /**
     * 用来查询指定AT子商户号
     */
    private int provider;

    /**
     * 子任务渠道
     */
    private String channel;


    public UpdateMerchantBankAccountEvent(Object source, Long subTaskId, int provider, String channel) {
        super(source);
        this.subTaskId = subTaskId;
        this.provider = provider;
        this.channel = channel;
    }
}
