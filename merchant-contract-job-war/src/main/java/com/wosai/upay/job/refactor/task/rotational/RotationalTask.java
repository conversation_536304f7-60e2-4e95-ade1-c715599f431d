package com.wosai.upay.job.refactor.task.rotational;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ScheduleStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.AddAffectStatusSuccessTaskCountBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.BatchGetScheduleTaskPatternTypeEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleMainTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.refactor.task.AbstractInternalScheduleTaskHandleTemplate;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 轮询任务
 *
 * <AUTHOR>
 * @date 2025/4/23 15:22
 */
@Slf4j
@Component
public class RotationalTask extends AbstractInternalScheduleTaskHandleTemplate {

    @Resource
    private InterScheduleTaskServiceImpl interScheduleTaskService;

    @Resource
    protected ApplicationContext applicationContext;

    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;

    @Resource
    private ContractSubTaskMapper contractSubTaskMapper;

    @Resource
    private ContractTaskDAO contractTaskDAO;

    @Autowired
    private TaskResultService taskResultService;

    /**
     * 子任务处理器映射，key为子任务类型的值，value为对应的处理器
     */
    private Map<String, RotationalSubTaskProcessor> subTaskProcessorMap;

    /**
     * 初始化子任务处理器映射
     */
    @PostConstruct
    public void init() {
        subTaskProcessorMap = new ConcurrentHashMap<>();
        try {
            Map<String, RotationalSubTaskProcessor> processorBeans = applicationContext.getBeansOfType(RotationalSubTaskProcessor.class);
            subTaskProcessorMap = processorBeans.values().stream()
                    .collect(Collectors.toMap(
                            processor -> processor.getSubTaskType().getValue(),
                            processor -> processor,
                            (existing, replacement) -> {
                                log.warn("发现重复的子任务处理器: {} 和 {}",
                                        existing.getClass().getName(),
                                        replacement.getClass().getName());
                                return existing;
                            }
                    ));
        } catch (Exception e) {
            log.error("初始化轮询子任务处理器映射失败", e);
            subTaskProcessorMap = new ConcurrentHashMap<>();
        }
    }

    /**
     * 获取任务类型
     *
     * @return 任务类型
     */
    @Override
    public InternalScheduleTaskTypeEnum getTaskType() {
        return InternalScheduleTaskTypeEnum.ROTATIONAL_TASK;
    }

    /**
     * 获取任务执行属性taskExecuteProperty
     */
    @Override
    protected ScheduleTaskExecutePropertyBO getTaskExecuteProperty() {
        ScheduleTaskExecutePropertyBO executeProperty = new ScheduleTaskExecutePropertyBO();
        executeProperty.setBatchGetPatternType(BatchGetScheduleTaskPatternTypeEnum.PENDING_AND_WAITING_EXT_TASKS);
        return executeProperty;
    }

    /**
     * 处理单个子任务逻辑
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     * @return 子任务执行结果
     */
    @Override
    protected InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        RotationalSubTaskProcessor rotationalSubTaskProcessor = subTaskProcessorMap.get(subTaskDO.getTaskType());
        if (Objects.isNull(rotationalSubTaskProcessor)) {
            log.error("不支持的子任务类型: {}", subTaskDO.getTaskType());
            throw new ContractBizException("不支持的子任务类型: " + subTaskDO.getTaskType());
        }
        InternalScheduleSubTaskProcessResultBO resultBO = rotationalSubTaskProcessor.handleRotationalSubTask(mainTaskDO, subTaskDO);
        try {
            postProcessIfContractTask(mainTaskDO, resultBO);
        } catch (Exception e) {
            log.error("轮询任务处理子任务后置逻辑失败, merchantSn:{}, subTaskId:{}",mainTaskDO.getMerchantSn(), subTaskDO.getId(), e);
        }
        return resultBO;
    }

    /**
     * 如果是contractTask任务，涉及任务状态的变更
     * 示例：com.wosai.upay.job.scheduler.ContractTaskSchedule#queryFuyouUpdateStatus()
     */
    private void postProcessIfContractTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskProcessResultBO resultBO) {
        RotationalTaskContext rotationalTaskContext = JSON.parseObject(mainTaskDO.getContext(), RotationalTaskContext.class);
        if (!rotationalTaskContext.getBelongToContractTask()) {
            return;
        }
        Optional<ContractSubTaskDO> subTaskDOOpt = contractSubTaskDAO.getByPrimaryKey(rotationalTaskContext.getContractSubTaskId());
        Optional<ContractTaskDO> contractTaskDOOpt = contractTaskDAO.getByPrimaryKey(rotationalTaskContext.getContractTaskId());
        if (!subTaskDOOpt.isPresent() || !contractTaskDOOpt.isPresent()) {
            return;
        }
        ContractSubTaskDO contractSubTaskDO = subTaskDOOpt.get();
        ContractTaskDO contractTaskDO = contractTaskDOOpt.get();
        String result = resultBO.getResult();
        String responseMsg = resultBO.getResponseMsg();
        contractSubTaskDO.setResponseBody(responseMsg);
        contractSubTaskDO.setResult(result);
        if (resultBO.processStatusIsSuccess()) {
            contractSubTaskDO.setStatus(ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue());
            contractSubTaskDAO.updateByPrimaryKeySelective(contractSubTaskDO);
            contractSubTaskMapper.setEnableScheduleByDepId(contractSubTaskDO.getId());
            if (Objects.equals(contractSubTaskDO.getStatusInfluPTask(), AffectPrimaryTaskStatusEnum.YES.getValue())) {
                addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(contractSubTaskDO.getPTaskId());
                if (!Objects.equals(contractTaskDO.getStatus(), ContractTaskProcessStatusEnum.BEING_PROCESSING.getValue())
                        && !Objects.equals(contractTaskDO.getStatus(), ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue())) {
                    taskResultService.changeStatusAndResultV2(contractTaskDO.getId(), contractSubTaskDO.getId(), contractTaskDO.getStatus(), null, false);
                }
            }
        }
        if (resultBO.processStatusIsFail()) {
            Map resultMap = CollectionUtil.hashMap("channel", contractSubTaskDO.getChannel(),
                    "message", resultBO.getResult(),
                    "result", resultBO.getResult());
            if (Objects.nonNull(contractSubTaskDO.getPayway())) {
                resultMap.put("payway", contractSubTaskDO.getPayway());
            }
            String resultMessage = JSON.toJSONString(resultMap);
            contractSubTaskDO.setStatus(ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue());
            contractSubTaskDAO.updateByPrimaryKeySelective(contractSubTaskDO);
            if (Objects.equals(contractSubTaskDO.getStatusInfluPTask(), AffectPrimaryTaskStatusEnum.YES.getValue())) {
                taskResultService.changeStatusAndResultV2(contractTaskDO.getId(), contractSubTaskDO.getId(), ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue(), resultMessage, false);
            } else {
                List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByDependTaskId(contractSubTaskDO.getId());
                if (WosaiCollectionUtils.isNotEmpty(contractSubTasks)
                        && contractSubTasks.stream().anyMatch(t -> Objects.equals(t.getStatus_influ_p_task(), AffectPrimaryTaskStatusEnum.YES.getValue()))) {
                    taskResultService.changeStatusAndResultV2(contractTaskDO.getId(), contractSubTaskDO.getId(), ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue(), resultMessage, false);
                }
            }
        }
    }

    @Resource
    private AddAffectStatusSuccessTaskCountBiz addAffectStatusSuccessTaskCountBiz;

    private String getContractTaskResult(String message) {
        HashMap<String, String> map = Maps.newHashMap();
        map.put(CommonModel.RESULT_MESSAGE, message);
        return JSON.toJSONString(map);
    }

    /**
     * 任务构建轮询任务
     */
    public void buildTaskForContractTask(RotationalTaskContext rotationalTaskContext) {
        if (Objects.isNull(rotationalTaskContext)) {
            throw new ContractBizException("轮询任务上下文为空");
        }
        InternalScheduleMainTaskDO internalScheduleMainTaskDO = buildMainTaskAndSubTask(rotationalTaskContext);
        interScheduleTaskService.insertTasks(internalScheduleMainTaskDO, Lists.newArrayList(buildSubTask(rotationalTaskContext)));
    }

    private InternalScheduleSubTaskDO buildSubTask(RotationalTaskContext rotationalTaskContext) {
        InternalScheduleSubTaskDO subTaskDO = new InternalScheduleSubTaskDO();
        subTaskDO.setType(getTaskType().getValue());
        subTaskDO.setTaskType(rotationalTaskContext.getSubTaskTypeEnum().getValue());
        subTaskDO.setMerchantSn(rotationalTaskContext.getMerchantSn());
        subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue());
        subTaskDO.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        return subTaskDO;
    }

    private InternalScheduleMainTaskDO buildMainTaskAndSubTask(RotationalTaskContext rotationalTaskContext) {
        InternalScheduleMainTaskDO internalScheduleMainTaskDO = new InternalScheduleMainTaskDO();
        internalScheduleMainTaskDO.setMerchantSn(rotationalTaskContext.getMerchantSn());
        internalScheduleMainTaskDO.setType(getTaskType().getValue());
        internalScheduleMainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.WAIT_PROCESS.getValue());
        internalScheduleMainTaskDO.setAffectStatusSubTaskNum(1);
        internalScheduleMainTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        internalScheduleMainTaskDO.setContext(rotationalTaskContext.toString());
        return internalScheduleMainTaskDO;
    }

}
