package com.wosai.upay.job.refactor.task.license.entity;

import com.wosai.mc.utils.PhotoUtils;
import com.wosai.upay.job.refactor.model.annotation.FieldConvertMapping;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 营业执照DTO
 *
 * <AUTHOR>
 * @date 2025/1/15 11:26
 */
@Data
public class BusinessLicenseDTO {

    public static final String AUXILIARY_PROOF_MATERIALS_KEY = "auxiliary_proof_materials";

    /**
     * 商户经营名称
     */
    @FieldConvertMapping(source="business_name")
    private String businessName;

    /**
     * 营业证照类型
     */
    @FieldConvertMapping(source="type")
    private Integer type;

    /**
     * 营业证照照片
     */
    @FieldConvertMapping(source="photo")
    private String licensePhoto;

    /**
     * 统一社会信用代码
     */
    @FieldConvertMapping(source="number")
    private String number;

    /**
     * 营业证照名称
     */
    @FieldConvertMapping(source="name")
    private String name;

    /**
     * 营业证照上的法人姓名
     */
    @FieldConvertMapping(source="registered_legal_person_name")
    private String legalPersonName;

    /**
     * 注册地址
     */
    @FieldConvertMapping(source="address")
    private String registerAddress;

    /**
     * 有效期
     */
    @FieldConvertMapping(source="validity")
    private String validity;

    /**
     * 法人证件类型
     */
    @FieldConvertMapping(source="legal_person_id_type")
    private int legalPersonCertificateType;

    /**
     * 法人证件正面照
     */
    @FieldConvertMapping(source="legal_person_id_card_front_photo")
    private String legalPersonCertificateFrontPhoto;

    /**
     * 法人证件反面照
     */
    @FieldConvertMapping(source="legal_person_id_card_back_photo")
    private String legalPersonCertificateBackPhoto;

    /**
     * 法人证件号码
     */
    @FieldConvertMapping(source="legal_person_id_number")
    private String legalPersonCertificateNumber;

    /**
     * 法人证件姓名
     */
    @FieldConvertMapping(source="legal_person_name")
    private String legalPersonCertificateName;

    /**
     * 法人证件有效期
     */
    @FieldConvertMapping(source="id_validity")
    private String legalPersonCertificateValidity;

    /**
     * 法人证件地址
     */
    @FieldConvertMapping(source="legal_person_id_card_address")
    private String legalPersonCertificateAddress;

    /**
     * 法人证件签发机关
     */
    @FieldConvertMapping(source="legal_person_id_card_issuing_authority")
    private String legalPersonCertificateIssuingAuthority;

    /**
     * 辅助证明材料
     */
    @FieldConvertMapping(source="auxiliary_proof_materials")
    private String auxiliaryProofMaterials;

    /**
     * 备注
     */
    @FieldConvertMapping(source="remark")
    private String remark;

    public void basePhotos() {
        if (StringUtils.isNotBlank(this.licensePhoto)) {
            this.licensePhoto = PhotoUtils.baseUrl(this.licensePhoto);
        }
        if (StringUtils.isNotBlank(this.legalPersonCertificateFrontPhoto)) {
            this.legalPersonCertificateFrontPhoto = PhotoUtils.baseUrl(this.legalPersonCertificateFrontPhoto);
        }
        if (StringUtils.isNotBlank(this.legalPersonCertificateBackPhoto)) {
            this.legalPersonCertificateBackPhoto = PhotoUtils.baseUrl(this.legalPersonCertificateBackPhoto);
        }
        if (StringUtils.isNotBlank(this.auxiliaryProofMaterials)) {
            this.auxiliaryProofMaterials = PhotoUtils.baseUrl(this.auxiliaryProofMaterials);
        }

    }
    
}
