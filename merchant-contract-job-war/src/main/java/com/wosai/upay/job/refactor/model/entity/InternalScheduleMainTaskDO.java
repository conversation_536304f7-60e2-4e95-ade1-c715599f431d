package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.*;
import com.google.common.collect.Lists;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleMainTaskStatusEnum;
import lombok.Data;

/**
 * 内部调度任务主表表实体对象
 *
 * <AUTHOR>
 */
@TableName("internal_schedule_main_task")
@Data
public class InternalScheduleMainTaskDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 任务类型
     */
    @TableField(value = "type")
    private Integer type;
    /**
     * 任务状态 0-待处理 1-处理中 2-等待下次调度中(授权,审核,回调等,该状态需要重复被拉起调度) 3-处理失败 4-处理成功
     * 默认0
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 影响任务状态的子任务数量
     */
    @TableField(value = "affect_status_sub_task_num")
    private Integer affectStatusSubTaskNum;
    /**
     * 已经处理成功的子任务数量
     */
    @TableField(value = "already_success_sub_task_num")
    private Integer alreadySuccessSubTaskNum;
    /**
     * 任务上下文信息,json类型,每个type对应的json格式固定
     */
    @TableField(value = "context")
    private String context;
    /**
     * 处理结果信息
     */
    @TableField(value = "result")
    private String result;
    /**
     * 备注信息
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;
    /**
     * 结算通道
     */
    @TableField(value = "provider")
    private Integer provider;
    /**
     * 版本号
     */
    @TableField(value = "version")
    private Integer version;
    /**
     * 可以调度开始时间（如果不为null，大于此时间才可以执行任务）
     */
    @TableField(value = "enable_scheduled_time")
    private Timestamp enableScheduledTime;
    /**
     * 是否可被调度 0-不可调度 1-可调度
     */
    @TableField(value = "enable_scheduled_status")
    private Integer enableScheduledStatus;
    /**
     * 上次被调度时间
     */
    @TableField(value = "last_scheduled_time")
    private Timestamp lastScheduledTime;
    /**
     * 任务到期时间,超过此时间任务置为失败
     */
    @TableField(value = "expiration_time")
    private Timestamp expirationTime;
    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Timestamp mtime;
    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Timestamp ctime;

    public void setResult(String result) {
        int maxLength = 200;
        if (Objects.isNull(result)) {
            this.result = null;
            return;
        }
        if (result.length() > maxLength) {
            this.result = result.substring(0, maxLength);
            return;
        }
        this.result = result;
    }

    public boolean taskExpired() {
        return Objects.nonNull(this.expirationTime) && this.expirationTime.before(new Date());
    }

    public void updateStatusFailed(String result) {
        this.status = InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue();
        this.result = result;
    }

    public boolean taskCanNotHandled() {
        return (Objects.equals(this.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue())
                || Objects.equals(this.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue())
                || Objects.equals(this.getStatus(), InternalScheduleMainTaskStatusEnum.BEING_PROCESSING.getValue()));
    }

    /**
     * 任务是否处理成功
     *
     * @return 是否处理成功
     */
    public boolean taskSuccess() {
        return Objects.equals(this.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue());
    }

    /**
     * 任务还未到达调度时间
     *
     * @return 未到达调度时间 -true未到达
     */
    public boolean taskCanScheduledTimeNotArrive() {
        if (Objects.isNull(this.enableScheduledTime)) {
            return false;
        }
        return this.enableScheduledTime.after(new Date());
    }

    public boolean taskFail() {
        return Objects.equals(this.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue());
    }


    public boolean taskInProcessing() {
        return !Lists.newArrayList(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue(), InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue()).contains(this.getStatus());
    }
}

