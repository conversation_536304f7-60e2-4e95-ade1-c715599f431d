package com.wosai.upay.job.payactivity.alipay;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.MchSnapshot;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.repository.BlueSeaTaskRepository;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.model.IndustryCode;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description: 支付宝活动处理
 * <AUTHOR>
 * @Date: 2021/7/27 5:37 下午
 */
@Component
@Slf4j
public abstract class AbstractAlipayActivityService {

    @Autowired
    private BlueSeaBiz blueSeaBiz;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private BlueSeaTaskRepository blueSeaTaskRepository;
    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;

    /**
     * 活动task生成
     *
     * @param merchantSn
     * @param auditId
     * @param activityType
     * @param formBody
     * @return
     */
    public CommonResult createTaskApply(String merchantSn, Long auditId, int activityType, Map formBody) {
        CommonResult result;
        try {
            /** 共同校验*/
            if (!blueSeaBiz.checkRepetiveness(merchantSn, activityType)) {
                throw new ContractBizException(BlueSeaConstant.REASON_REPETIVENESS);
            }
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            String merchantId = MapUtils.getString(merchant, CommonModel.ID);
            if (WosaiMapUtils.isEmpty(merchant)) {
                throw new ContractBizException("商户不存在");
            }
            Tuple2<String, Boolean> aliMchInfo = blueSeaBiz.getInUseMchId(merchantSn);
            String aliMchId = aliMchInfo.get_1();
            if (WosaiStringUtils.isEmpty(aliMchId)) {
                throw new ContractBizException("商户没有支付宝子商户号");
            }

            String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
            if (WosaiStringUtils.isEmpty(industryId)) {
                throw new ContractBizException("行业不满足活动要求");
            }
            String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);

            // 活动支持的行业code 返回all支持所有的行业
            String supportIndustry = supportIndustry();
            if (!supportIndustry.equals("all") && !supportIndustry.contains(aliMcc)) {
                throw new ContractBizException("行业不满足活动要求");
            }

            /**不同活动特殊校验*/
            activityTypeCheck(merchant,formBody,aliMchInfo.get_2());

            /**不同活动mch_snapshot*/
            MchSnapshot snapshot = MchSnapshot.builder().aliMcc(aliMcc).direct(aliMchInfo.get_2()).build();
            buildSnapshot(snapshot, merchant,formBody,aliMcc,aliMchId);

            /**新增task表*/
            creatTask(merchantSn, merchantId, aliMchId, auditId, activityType, formBody, snapshot);

            result = new CommonResult(CommonResult.SUCCESS, "处理成功活动报名中");

        } catch (ContractException e) {
            log.error("商户{}申请支付宝活动{},业务异常", merchantSn, activityType,e);
            result = new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        } catch (Exception e) {
            log.error("商户{}申请支付宝活动{},系统异常", merchantSn, activityType,e);
            result = new CommonResult(CommonResult.ERROR, e.getMessage());
        }
        return result;
    }


    /**
     * 活动支持的行业code
     *
     * @return
     */
    public abstract String supportIndustry();

    /**
     * @param merchant 商户信息
     * @param formBody
     * @param direct   间直连标示 true 直连 false间连
     */
    public abstract void activityTypeCheck( Map merchant,Map formBody, Boolean direct);

    /**
     * 不同活动生成的商户快照
     *
     * @return
     */
    public abstract MchSnapshot buildSnapshot(MchSnapshot snapshot ,  Map merchant, Map formBody,String aliMcc,String aliMchId);


    /**
     * 新增任务
     *
     * @param merchantSn   商户Sn
     * @param merchantId   商户ID
     * @param aliMchId     支付宝渠道商户号
     * @param auditId      审批ID
     * @param activityType 活动类型
     * @param formBody     申请报文
     * @param snapshot     商户快照
     */
    public void creatTask(String merchantSn, String merchantId, String aliMchId,
                          Long auditId, int activityType,
                          Map formBody, MchSnapshot snapshot) {
        BlueSeaTask task = new BlueSeaTask();
        Long applyId = MapUtils.getLong(formBody,"apply_id");
        task.setAudit_id(auditId);
        task.setApply_id(applyId);
        task.setAli_mch_id(aliMchId);
        task.setMerchant_sn(merchantSn);
        task.setMerchant_id(merchantId);
        task.setStatus(BlueSeaConstant.PENDING);
        formBody.remove("apply_id");
        task.setForm_body(JSON.toJSONString(formBody));
        task.setMch_snapshot(JSON.toJSONString(snapshot));
        task.setType(activityType);
        blueSeaTaskRepository.insertSelectiveAndStoreSn(task);
    }

    /**
     * 新增任务
     *
     * @param merchantSn   商户Sn
     * @param merchantId   商户ID
     * @param aliMchId     支付宝渠道商户号
     * @param auditId      审批ID
     * @param activityType 活动类型
     * @param formBody     申请报文
     * @param snapshot     商户快照
     */
    public void creatTaskWithStoreSn(String merchantSn, String merchantId, String aliMchId,
                          Long auditId, int activityType,
                          Map formBody, MchSnapshot snapshot,String storeSn) {
        BlueSeaTask task = new BlueSeaTask();
        Long applyId = MapUtils.getLong(formBody, "apply_id");
        task.setAudit_id(auditId);
        task.setApply_id(applyId);
        task.setAli_mch_id(aliMchId);
        task.setMerchant_sn(merchantSn);
        task.setMerchant_id(merchantId);
        task.setStatus(BlueSeaConstant.PENDING);
        task.setForm_body(JSON.toJSONString(formBody));
        task.setMch_snapshot(JSON.toJSONString(snapshot));
        task.setType(activityType);
        task.setStore_sn(storeSn);
        blueSeaTaskRepository.insertSelectiveAndStoreSn(task);
    }


    /**
     * 活动申请前置处理
     */
    public abstract void activityApplyPreHandle(BlueSeaTask task);

    /**
     * 线上申请活动
     *
     * @param task
     */
    public abstract void activityApply(BlueSeaTask task);


    /**
     * 活动申请成功后置处理
     *
     * @param task
     */
    public abstract void activityApplyPostHandle(BlueSeaTask task);

    /**
     * 切换费率
     */
    public CommonResult changeFeeRate(String merchantSn, Long auditId, int activityType, Map formBody) {
        return new CommonResult(CommonResult.SUCCESS, "处理成功");
    }

    /**
     * 审批驳回
     * @param merchantSn
     * @param auditId
     * @param activityType
     * @param formBody
     * @return
     */
    public abstract CommonResult auditReject(String merchantSn, Long auditId, int activityType, Map formBody);

    /**
     * 审批通过
     *
     * @param merchantSn
     * @param auditId
     * @param activityType
     * @param formBody
     * @return
     */
    public abstract CommonResult auditApprove(String merchantSn, Long auditId, int activityType, Map formBody);

}
