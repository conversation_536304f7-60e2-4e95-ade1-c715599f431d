package com.wosai.upay.job.refactor.task.license.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.wosai.mc.utils.PhotoUtils;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 银行账户信息DTO
 *
 * <AUTHOR>
 * @date 2025/1/15 11:26
 */
@Data
public class BankAccountDTO {

        public static final Integer LEGAL_PERSONAL = 1;

        public static final Integer CORPORATE = 2;

        public static final Integer OTHER_PERSONAL = 3;

        public static final Integer AUTHORIZED_CORPORATE = 4;

        public static final String SETTLEMENT_ACCOUNT_TYPE_KEY = "settlement_account_type";

        /**
         * 结算账户类型
         * 1-法人代表个人账户 2-对公企业账户 3-其它个人账户 4-授权对公企业账户
         */
        @JsonProperty("settlement_account_type")
        @JSONField(name = "settlement_account_type")
        private Integer settlementAccountType;

        /**
         * 银行卡照片
         */
        @JsonProperty("bank_card_image")
        @JSONField(name = "bank_card_image")
        private String bankCardPhoto;

        /**
         * 账户名称
         */
        @JsonProperty("holder")
        @JSONField(name = "holder")
        private String holder;

        /**
         * 银行卡号
         */
        @JsonProperty("number")
        @JSONField(name = "number")
        private String accountNumber;

        /**
         * 开户银行
         */
        @JsonProperty("bank_name")
        @JSONField(name = "bank_name")
        private String openingBank;

        /**
         * 开户城市
         */
        @JsonProperty("city")
        @JSONField(name = "city")
        private String openingCity;

        /**
         * 开户支行
         */
        @JsonProperty("branch_name")
        @JSONField(name = "branch_name")
        private String openingBranch;

        /**
         * 开户支行号
         */
        @JsonProperty("opening_number")
        @JSONField(name = "opening_number")
        private String openingNumber;

        /**
         * 清算行号
         */
        @JsonProperty("clearing_number")
        @JSONField(name = "clearing_number")
        private String clearingNumber;

        /**
         * 银行卡有效期
         */
        @JsonProperty("card_validity")
        @JSONField(name = "card_validity")
        private String cardValidity;

        /**
         * 证件类型
         */
        @JsonProperty("id_type")
        @JSONField(name = "id_type")
        private Integer certificateType;

        /**
         * 证件正面照片
         */
        @JsonProperty("holder_id_front_photo")
        @JSONField(name = "holder_id_front_photo")
        private String certificateFrontPhoto;

        /**
         * 证件反面照片
         */
        @JsonProperty("holder_id_back_photo")
        @JSONField(name = "holder_id_back_photo")
        private String certificateBackPhoto;


        /**
         * 证件号码
         */
        @JsonProperty("identity")
        @JSONField(name = "identity")
        private String certificateNumber;

        /**
         * 证件有效期
         */
        @JsonProperty("id_validity")
        @JSONField(name = "id_validity")
        private String certificateValidity;

        /**
         * 证件地址
         */
        @JsonProperty("holder_id_card_address")
        @JSONField(name = "holder_id_card_address")
        private String certificateAddress;

        /**
         * 证件签发机关
         */
        @JsonProperty("holder_id_card_issuing_authority")
        @JSONField(name = "holder_id_card_issuing_authority")
        private String certificateIssuingAuthority;


        /**
         * 授权函
         */
        @JsonProperty("letter_of_authorization")
        @JSONField(name = "letter_of_authorization")
        private String letterOfAuthorization;

        /**
         * 辅助证明材料
         * 法人手持授权函或关系证明（法人和授权函的合照）
         */
        @JsonProperty("hand_letter_of_authorization")
        @JSONField(name = "hand_letter_of_authorization")
        private String handLetterOfAuthorization;

        /**
         * 法人手持授权函或关系证明（法人和授权函的合照）
         */
        @JsonProperty("relation_or_legal_rep_held_auth")
        @JSONField(name = "relation_or_legal_rep_held_auth")
        private String relationOrLegalRepHeldAuth;

        // 以下字段最终落到merchant_bank_account表的extra字段

        /**
         * 证件姓名
         */
        @JsonProperty("certificate_name")
        @JSONField(name = "certificate_name")
        private String certificateName;

        /**
         * 权对公账户证明
         */
        @JsonProperty("authorize_corp_acc_proof")
        @JSONField(name = "authorize_corp_acc_proof")
        private String authorizeCorpAccProof;

        /**
         * 授权对公关系证明
         */
        @JsonProperty("authorize_corp_rel_proof")
        @JSONField(name = "authorize_corp_rel_proof")
        private String authorizeCorpRelProof;


        /**
         * 结算账户营业证照法人证件号
         */
        @JsonProperty("sett_acc_lic_lp_certificate_num")
        @JSONField(name = "sett_acc_lic_lp_certificate_num")
        private String settAccLicLpCertificateNum;

        /**
         * 结算账户营业证照法人证件正面照片
         */
        @JsonProperty("sett_acc_lic_lp_certificate_front_photo")
        @JSONField(name = "sett_acc_lic_lp_certificate_front_photo")
        private String settAccLicLpCertificateFrontPhoto;

        /**
         * 结算账户营业证照法人证件反面照片
         */
        @JsonProperty("sett_acc_lic_lp_certificate_back_photo")
        @JSONField(name = "sett_acc_lic_lp_certificate_back_photo")
        private String settAccLicLpCertificateBackPhoto;

        /**
         * 辅助证明材料
         */
        @JsonProperty("auxiliary_proof_materials")
        @JSONField(name = "auxiliary_proof_materials")
        private String auxiliaryProofMaterials;

        /**
         * 其他照片
         */
        @JsonProperty("other_photos")
        @JSONField(name = "other_photos")
        private String otherPhotos;

        /**
         * 根据账户结算类型计算账户类型
         *
         * @return 账户类型 1-对私 2-对公
         */
        public Integer calculateTypeBySettlementAccountType() {
                if (Objects.equals(this.settlementAccountType, LEGAL_PERSONAL)
                        || Objects.equals(this.settlementAccountType, OTHER_PERSONAL)) {
                        return BankAccountTypeEnum.PERSONAL.getValue();
                } else if (Objects.equals(this.settlementAccountType, CORPORATE)
                        || Objects.equals(this.settlementAccountType, AUTHORIZED_CORPORATE)) {
                        return BankAccountTypeEnum.PUBLIC.getValue();
                }
                throw new ContractBizException("不支持的账户结算类型");
        }

        public Map<String, Object> getExtraMap() {
                Map<String, Object> extraMap = new HashMap<>();
                extraMap.put("settlement_account_type", this.settlementAccountType);
                // 产品需求 无论是空还是null，都填充
                extraMap.put("certificate_name", this.certificateName);
                extraMap.put("authorize_corp_acc_proof", PhotoUtils.baseUrl(this.authorizeCorpAccProof));
                extraMap.put("authorize_corp_rel_proof", PhotoUtils.baseUrl(this.authorizeCorpRelProof));
                extraMap.put("sett_acc_lic_lp_certificate_num", this.settAccLicLpCertificateNum);
                extraMap.put("sett_acc_lic_lp_certificate_front_photo", PhotoUtils.baseUrl(this.settAccLicLpCertificateFrontPhoto));
                extraMap.put("sett_acc_lic_lp_certificate_back_photo", PhotoUtils.baseUrl(this.settAccLicLpCertificateBackPhoto));
                extraMap.put("auxiliary_proof_materials", PhotoUtils.baseUrl(this.auxiliaryProofMaterials));
                extraMap.put("other_photos", PhotoUtils.baseUrl(this.otherPhotos));
                return extraMap;
        }

        public void basePhotos() {
                if (StringUtils.isNotBlank(this.bankCardPhoto)) {
                        this.bankCardPhoto = PhotoUtils.baseUrl(this.bankCardPhoto);
                }
                if (StringUtils.isNotBlank(this.certificateFrontPhoto)) {
                        this.certificateFrontPhoto = PhotoUtils.baseUrl(this.certificateFrontPhoto);
                }
                if (StringUtils.isNotBlank(this.certificateBackPhoto)) {
                        this.certificateBackPhoto = PhotoUtils.baseUrl(this.certificateBackPhoto);
                }
                if (StringUtils.isNotBlank(this.letterOfAuthorization)) {
                        this.letterOfAuthorization = PhotoUtils.baseUrl(this.letterOfAuthorization);
                }
                if (StringUtils.isNotBlank(this.handLetterOfAuthorization)) {
                        this.handLetterOfAuthorization = PhotoUtils.baseUrl(this.handLetterOfAuthorization);
                }
                if (StringUtils.isNotBlank(this.settAccLicLpCertificateFrontPhoto)) {
                        this.settAccLicLpCertificateFrontPhoto = PhotoUtils.baseUrl(this.settAccLicLpCertificateFrontPhoto);
                }
                if (StringUtils.isNotBlank(this.settAccLicLpCertificateBackPhoto)) {
                        this.settAccLicLpCertificateBackPhoto = PhotoUtils.baseUrl(this.settAccLicLpCertificateBackPhoto);
                }
                if (StringUtils.isNotBlank(this.auxiliaryProofMaterials)) {
                        this.auxiliaryProofMaterials = PhotoUtils.baseUrl(this.auxiliaryProofMaterials);
                }
                if (StringUtils.isNotBlank(this.otherPhotos)) {
                        this.otherPhotos = PhotoUtils.baseUrl(this.otherPhotos);
                }
                if (StringUtils.isNotBlank(this.authorizeCorpAccProof)) {
                        this.authorizeCorpAccProof = PhotoUtils.baseUrl(this.authorizeCorpAccProof);
                }
                if (StringUtils.isNotBlank(this.authorizeCorpRelProof)) {
                        this.authorizeCorpRelProof = PhotoUtils.baseUrl(this.authorizeCorpRelProof);
                }
        }

}
