package com.wosai.upay.job.externalservice.tag;

import com.wosai.data.crow.api.service.TagIngestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;

/**
 * 标签接口
 * <AUTHOR>
 * @date 2024/12/3
 */
@Slf4j
@Component
public class TagClient {

    @Autowired
    private TagIngestService tagIngestService;
    @Value("${tag.auth.layer.wx.contract}")
    private String authLayerWxContractTagId;
    @Value("${tag.auth.layer.ali.contract}")
    private String authLayerAliContractTagId;

    @Value("${umb.tag}")
    private String umbTag;

    public int removeWxWaitAuthContract(String merchantId) {
        try {
            return tagIngestService.ingest(Collections.singletonList(merchantId), authLayerWxContractTagId, "否");
        } catch (Exception e) {
            log.error("[TagClient#ingestMerchantTag] merchantId:{} tagId:{} value:{}", merchantId, authLayerWxContractTagId, "否", e);
            return 0;
        }
    }

    public int removeAliWaitAuthContract(String merchantId) {
        try {
            return tagIngestService.ingest(Collections.singletonList(merchantId), authLayerAliContractTagId, "否");
        } catch (Exception e) {
            log.error("[TagClient#ingestMerchantTag] merchantId:{} tagId:{} value:{}", merchantId, authLayerAliContractTagId, "否", e);
            return 0;
        }
    }

    public void addUmbTag(String merchantId) {
        try {
            tagIngestService.ingest(Collections.singletonList(merchantId), umbTag, "是");
        } catch (Exception e) {
            log.error("[TagClient#ingestMerchantTag] merchantId:{} tagId:{} value:{}", merchantId, umbTag, "是", e);
        }
    }
}
