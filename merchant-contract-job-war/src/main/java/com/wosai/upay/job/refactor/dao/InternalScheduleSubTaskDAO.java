package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.InternalScheduleSubTaskMapper;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * 内部调度任务子表表数据库访问层 {@link InternalScheduleSubTaskDO}
 * 对InternalScheduleSubTaskMapper层做出简单封装 {@link InternalScheduleSubTaskMapper}
 *
 * <AUTHOR>
 */
@Repository
public class InternalScheduleSubTaskDAO extends AbstractBaseDAO<InternalScheduleSubTaskDO, InternalScheduleSubTaskMapper> {

    public InternalScheduleSubTaskDAO(SqlSessionFactory sqlSessionFactory, InternalScheduleSubTaskMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据主键更新,null字段不参与更新
     *
     * @param entity 待更新对象
     */
    @Override
    public Integer updateByPrimaryKeySelective(InternalScheduleSubTaskDO entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        entity.setMtime(null);
        entity.setCtime(null);
        return super.updateByPrimaryKeySelective(entity);
    }

    /**
     * 根据主任务id列表获取子任务列表
     *
     * @param mainIds 主任务id列表
     * @return 子任务列表
     */
    public List<InternalScheduleSubTaskDO> listByMainIds(List<Long> mainIds) {
        if (CollectionUtils.isEmpty(mainIds)) {
            return Collections.emptyList();
        }
        return entityMapper.selectList(new LambdaQueryWrapper<InternalScheduleSubTaskDO>().in(InternalScheduleSubTaskDO::getMainTaskId, mainIds));
    }
}
