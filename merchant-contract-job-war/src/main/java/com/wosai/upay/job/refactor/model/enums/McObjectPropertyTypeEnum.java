package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;
import lombok.Getter;

/**
 * 进件商户特征对象属性枚举
 *
 * <AUTHOR>
 */
@Getter
public enum McObjectPropertyTypeEnum implements ITextValueEnum<String> {

    MERCHANT_NAME("name", "商户名称"),

    /**
     * 商户类型 1-小微 2-个体户 3-企业商户 4-组织商户
     */
    MERCHANT_TYPE("type", "商户类型"),
    DISTRICT_CODE("districtCode", "区唯一编码"),
    PROVINCE_CODE("provinceCode", "省唯一编码"),
    CITY_CODE("cityCode", "市唯一编码"),
    PROVINCE_NAME("provinceName", "省名称"),
    CITY_NAME("cityName", "市名称"),
    INDUSTRY("industry", "商户行业"),
    /**
     * 所属推广组织path(从一级开始)
     */
    PROMOTION_ORGANIZATION_PATH("promotionOrganizationPath", "所属推广组织"),
    /**
     * 商户所属组织path(从一级开始)
     */
    ORGANIZATION_PATH("organizationPath", "商户所属组织"),
    /**
     * 银行账户类型 1-对私 2-对公
     */
    BANK_ACCOUNT_TYPE("bankAccountType", "银行账户类型"),
    /**
     * 法人类型 0-非法人 1-法人
     */
    LEGAL_PERSON_TYPE("legalPersonType", "法人类型"),
    /**
     * 结算账户类型 1-法人对私 2-非法人对私 3-普通对公 4-其他对公 999-其他
     */
    SETTLEMENT_ACCOUNT_TYPE("settlementAccountType", "结算账户类型"),

    /**
     * 个人证件类型 1-身份证 2-港澳居民来往内地通行证 3-台湾居民来往大陆通行证 4-非中华人民共和国护照 5-中国护照 6-港澳居民居住证 7-台湾居民居住证
     */
    PERSONAL_CERTIFICATE_TYPE("personalCertificateType", "个人证件类型"),

    /**
     * 商户经营名称
     */
    BUSINESS_NAME("businessName", "商户经营名称"),

    /**
     * 商户开通的应用列表json
     */
    OPENED_BUSINESS_APP_ID_LIST_JSON("openedBusinessAppIdListJson", "商户开通的应用列表"),

    /**
     * 目标入网收单机构
     */
    ACQUIRER("acquirer", "目标入网收单机构"),

    /**
     * 品牌商户支付模式
     */
    PAYMENT_MODE("paymentMode", "品牌商户支付模式"),

    /**
     * 门店省 (多个逗号隔开)
     */
    STORE_PROVINCES("storeProvinces", "门店省"),

    /**
     * 门店市 (多个逗号隔开)
     */
    STORE_CITIES("storeCities", "门店市"),

    /**
     * 入网场景 (业务开通/通道切换)
     */
    NET_IN_SCENE("netInScene", "入网场景"),

    /**
     * 法人证件类型
     */
    LEGAL_PERSON_CERTIFICATE_TYPE("legalPersonCertificateType", "法人证件类型"),

    /**
     * 法人身份证年龄
     */
    LEGAL_ID_CARD_AGE("legalIdCardAge", "法人身份证年龄"),

    /**
     * 结算人身份证年龄
     */
    SETTLEMENT_ID_CARD_AGE("settlementIdCardAge", "结算人身份证年龄");


    private final String value;
    private final String text;

    McObjectPropertyTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

}
