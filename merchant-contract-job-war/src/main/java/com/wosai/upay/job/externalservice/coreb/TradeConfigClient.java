package com.wosai.upay.job.externalservice.coreb;

import com.wosai.upay.core.bean.request.BaseTradeExtRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigRemoveRequest;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/26
 */
@Component
@Slf4j
public class TradeConfigClient {

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    /**
     * 清理交易侧终端配置
     *
     * @param merchantSn
     * @param provider
     */
    public void clearProviderTerminal(String merchantSn, Integer provider) {
        TradeExtConfigRemoveRequest removeRequest = new TradeExtConfigRemoveRequest();
        removeRequest.setMerchantSn(merchantSn);
        removeRequest.setProvider(provider);
        removeRequest.setSnTypes(Arrays.asList(BaseTradeExtRequest.SN_TYPE_TERMINAL));
        tradeConfigService.deleteTradeExtConfig(removeRequest);
        supportService.removeCachedParams(merchantSn);
    }

    /**
     * 设置清算标识
     * 进件过程中使用，未进件、进件成功不处理
     *
     * @param merchantId
     * @param merchantSn
     */
    public void updateClearProvider(String merchantId, String merchantSn) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null || contractStatus.getStatus() == ContractStatus.STATUS_SUCCESS) {
            return;
        }
        updateClearProviderByAcquirer(merchantId, contractStatus.getAcquirer());
    }

    /**
     * 设置清算标识
     * 指定收单机构设置
     *
     * @param merchantId
     * @param acquirer
     */
    public void updateClearProviderByAcquirer(String merchantId, String acquirer) {
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        if (Objects.nonNull(mcAcquirerDO) && Objects.nonNull(mcAcquirerDO.getClearanceProvider())) {
            tradeConfigService.updateClearanceProvider(merchantId, mcAcquirerDO.getClearanceProvider());
            log.info("设置清算标识 {} {} {}", merchantId, acquirer, mcAcquirerDO.getClearanceProvider());
        } else {
            log.info("收单机构没有清算标识 {} {}", merchantId, acquirer);
        }
    }

    /**
     * 设置清算标识
     * 指定收单机构设置，用于营业执照升级后
     *
     * @param merchantId
     * @param acquirer
     */
    public void updateClearanceProviderAndSwitchTime(String merchantId, String acquirer) {
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        if (Objects.nonNull(mcAcquirerDO) && Objects.nonNull(mcAcquirerDO.getClearanceProvider())) {
            tradeConfigService.updateMerchantSwitchMchTime(merchantId, mcAcquirerDO.getClearanceProvider(), System.currentTimeMillis());
            tradeConfigService.updateClearanceProvider(merchantId, mcAcquirerDO.getClearanceProvider());
            log.info("设置清算标识 {} {} {}", merchantId, acquirer, mcAcquirerDO.getClearanceProvider());
        } else {
            log.info("收单机构没有清算标识 {} {}", merchantId, acquirer);
        }
    }

    /**
     * 设置清算标识为 -1 ，用于禁止交易
     *
     * @param merchantId
     */
    public void updateClearanceProviderToSwitch(String merchantId) {
        tradeConfigService.updateClearanceProvider(merchantId, TransactionParam.CLEARANCE_PROVIDER_SWITCH);
        log.info("设置清算标识 {} {}", merchantId, TransactionParam.CLEARANCE_PROVIDER_SWITCH);
    }


}
