package com.wosai.upay.job.refactor.dao;

import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.ProviderTerminalTaskDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.ProviderTerminalTaskDO;


/**
 * 收单机构终端报备任务表表数据库访问层 {@link ProviderTerminalTaskDO}
 * 对ProviderTerminalTaskMapper层做出简单封装 {@link ProviderTerminalTaskDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class ProviderTerminalTaskDAO extends AbstractBaseDAO<ProviderTerminalTaskDO, ProviderTerminalTaskDynamicMapper> {

    public ProviderTerminalTaskDAO(SqlSessionFactory sqlSessionFactory, ProviderTerminalTaskDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }
}
