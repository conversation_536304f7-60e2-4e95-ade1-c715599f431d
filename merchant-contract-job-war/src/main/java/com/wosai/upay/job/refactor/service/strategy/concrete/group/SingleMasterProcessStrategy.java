package com.wosai.upay.job.refactor.service.strategy.concrete.group;

import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDAO;
import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDetailDAO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRuleDetailDO;
import com.wosai.upay.job.refactor.model.enums.GroupCombinedTypeEnum;
import com.wosai.upay.job.refactor.model.enums.GroupTypeEnum;
import com.wosai.upay.job.refactor.service.strategy.GroupCombinedProcessStrategy;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 一主无备-报备规则组策略组合处理策略
 *
 * <AUTHOR>
 * @date 2023/12/13 17:42
 */
@Component
public class SingleMasterProcessStrategy implements GroupCombinedProcessStrategy {

    @Resource
    private GroupCombinedStrategyDetailDAO groupCombinedStrategyDetailDAO;

    @Resource
    private GroupCombinedStrategyDAO groupCombinedStrategyDAO;

    /**
     * 获取策略类型
     *
     * @return 报备规则组策略组合类型枚举
     */
    @Override
    public GroupCombinedTypeEnum getType() {
        return GroupCombinedTypeEnum.SINGLE_MASTER_NO_BACKUP;
    }

    /**
     * 获取符合的收单机构进件规则detail
     *
     * @param groupCombinedStrategies 规则组对应的策略组合detail
     * @return 符合条件的策略组合detail
     */
    @Override
    public List<GroupCombinedStrategyDetailDO> listSatisfactionDetails(List<GroupCombinedStrategyDetailDO> groupCombinedStrategies) {
        if (CollectionUtils.isEmpty(groupCombinedStrategies)) {
            return Collections.emptyList();
        }
        return groupCombinedStrategies.stream()
                .filter(t -> Objects.equals(t.getGroupType(), GroupTypeEnum.PRIMARY.getValue())).limit(1L).collect(Collectors.toList());
    }

    /**
     * 新增并且返回收单机构进件规则,不存在新增后返回,存在直接返回
     *
     * @param groupCombinedStrategyDetailDOS 进件报备规则组策略组合detail
     * @return 进件报备规则组策略
     */
    @Override
    public Optional<GroupCombinedStrategyDO> getGroupCombineStrategy(List<GroupCombinedStrategyDetailDO> groupCombinedStrategyDetailDOS) {
        if (CollectionUtils.isEmpty(groupCombinedStrategyDetailDOS)) {
            throw new ContractBizException("进件报备规则组策略组合detail为空");
        }
        GroupCombinedStrategyDetailDO groupCombinedStrategyDetailDO = groupCombinedStrategyDetailDOS.get(0);
        Map<Long, GroupCombinedStrategyDO> strategyDOMap = groupCombinedStrategyDAO.listByGroupType(getTypeValue()).stream().collect(Collectors.toMap(GroupCombinedStrategyDO::getId, Function.identity()));
        Map<String, Long> strategyDetailMap = groupCombinedStrategyDetailDAO.listByStrategyIds(strategyDOMap.values().stream()
                        .map(GroupCombinedStrategyDO::getId).collect(Collectors.toList()))
                .stream().filter(t -> Objects.equals(t.getAcquirer(), groupCombinedStrategyDetailDO.getAcquirer()))
                .collect(Collectors.toMap(GroupCombinedStrategyDetailDO::getGroupId, GroupCombinedStrategyDetailDO::getGroupStrategyId, (k1, k2) -> k1));
        return Optional.ofNullable(strategyDOMap.get(strategyDetailMap.get(groupCombinedStrategyDetailDO.getGroupId())));
    }
}
