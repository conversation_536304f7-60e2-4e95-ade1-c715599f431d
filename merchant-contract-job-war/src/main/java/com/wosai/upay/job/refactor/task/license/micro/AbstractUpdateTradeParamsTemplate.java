package com.wosai.upay.job.refactor.task.license.micro;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.model.dto.ProviderTerminalContext;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.biz.provider.McProviderBiz;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.service.impl.InternalKeyValueServiceImpl;
import com.wosai.upay.job.refactor.service.rpc.coreb.CoreBTradeConfigService;
import com.wosai.upay.job.refactor.service.rpc.coreb.req.CoreBTradeExtConfigRemoveRequest;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationTask;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV3Task;
import com.wosai.upay.job.refactor.utils.BeanCopyUtils;
import com.wosai.upay.job.refactor.utils.MapUtils;
import com.wosai.upay.job.service.task.BusinessLicenceTaskService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 抽象的营业执照认证交易参数处理
 *
 * <AUTHOR>
 * @date 2024/9/13 16:01
 */
@Slf4j
public abstract class AbstractUpdateTradeParamsTemplate {

    @Resource
    protected MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    protected TradeConfigService tradeConfigService;

    @Resource
    protected CoreBTradeConfigService coreBTradeConfigService;

    @Resource
    private ProviderTerminalDAO providerTerminalDAO;

    @Resource
    private ProviderTerminalBindConfigDAO providerTerminalBindConfigDAO;

    @Resource
    protected InternalKeyValueServiceImpl internalKeyValueService;

    @Resource
    protected ProviderTerminalTaskDAO providerTerminalTaskDAO;

    @Resource
    private SubBizParamsDAO subBizParamsDAO;

    @Resource
    protected MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Resource
    private AcquirerFacade acquirerFacade;

    @Resource
    private PlatformTransactionManager transactionManager;

    private TransactionTemplate transactionTemplate;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    @Resource
    private ChatBotUtil chatBotUtil;

    @Resource
    private McProviderBiz mcProviderBiz;

    @Autowired
    private SupportService supportService;

    @Autowired
    @Lazy
    protected BusinessLicenceTaskService businessLicenceTaskService;


    @PostConstruct
    public void init() {
        this.transactionTemplate = new TransactionTemplate(transactionManager);
    }

    /**
     * 获取收单机构
     *
     * @return 收单机构
     */
    public abstract String getAcquirer();


    /**
     * 更新银联侧支付源绑定的银联商户号
     * 新增交易参数 备份原来的参数
     * 更新交易侧pay is null的参数信息
     * 备份和删除终端相关的参数
     * 商户级别终端及支付源绑定
     * 异步门店和终端的重新报备和绑定
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     */
    public void updateParamsAfterContractSuccess(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        checkSubTaskDataVersionAndInitIfEmpty(subTaskDO);
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(subTaskDO.getMerchantSn());
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        BusinessLicenceCertificationTask.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationTask.SubTaskContextBOInner.class);
        updateUnionPayWayUnionMerchantId(subTaskDO, subTaskResponseDTOInner.getNewUnionMerchantId(), subTaskContextBOInner.getOldUnionMerchantId());
        List<MerchantProviderParamsDO> newParams = Lists.newArrayList();
        transactionTemplate.execute(new TransactionCallback<Void>() {
            @Override
            public Void doInTransaction(TransactionStatus transactionStatus) {
                boolean updateTradeParamsForPayIsNullSuccess = false;
                try {
                    updateTradeParamsForPayIsNull(merchantInfo, subTaskDO);
                    updateTradeParamsForPayIsNullSuccess = true;
                    newParams.addAll(addNewTradeParamsAndBackUpOldParams(subTaskDO));
                    deleteTerminalRelatedParams(subTaskDO);
                    deleteAllExceptMerchantLevelTradeExtConfig(mainTaskDO);
                } catch (Exception e) {
                    log.warn("小微升级商户更新sqb交易参数和终端处理报错,开始回滚原交易参数,sn={}, mainTaskId={}", mainTaskDO.getMerchantSn(), mainTaskDO.getId(), e);
                    transactionStatus.setRollbackOnly();
                    if (updateTradeParamsForPayIsNullSuccess) {
                        log.warn("更新sqb交易参数和终端处理报错,开始回滚原交易参数,交易侧也需要回滚,sn={}, mainTaskId={}", mainTaskDO.getMerchantSn(), mainTaskDO.getId());
                        rollbackTradeParamsForPayIsNullWithCatchException(merchantInfo, mainTaskDO, subTaskDO);
                    }
                    rollBackAllParamsUnionPayWayOldUnionMerchantId(subTaskDO, subTaskContextBOInner.getOldUnionMerchantId());
                    throw new ContractBizException("银联商户号更新后，更新sqb交易参数和终端处理报错", e);
                }
                return null;
            }
        });
        // todo 这里逻辑待优化，应该放在事务里，但是海科的contract服务接口会去查可能查不到，所以本期暂时放在事务外面（其实终端绑定任务本身也是异步的）
        addMerchantLevelStoreAndTerminalBind(mainTaskDO, subTaskDO, merchantInfo, newParams);
        acquirerSpecialProcess(mainTaskDO, subTaskDO);
        asyncExistedStoreAndTerminalBind(mainTaskDO, subTaskDO);
    }




    /**
     * 更新银联侧支付源绑定的银联商户号
     * 新增交易参数 备份原来的参数
     * 更新交易侧pay is null的参数信息
     * 备份和删除终端相关的参数
     * 商户级别终端及支付源绑定
     * 异步门店和终端的重新报备和绑定
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     */
    public void updateParamsAfterContractSuccessV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        checkSubTaskDataVersionAndInitIfEmptyV3(subTaskDO);
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(subTaskDO.getMerchantSn());
        BusinessLicenceCertificationV3Task.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationV3Task.SubTaskResponseDTOInner.class);
        BusinessLicenceCertificationV3Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV3Task.SubTaskContextBOInner.class);
        List<MerchantProviderParamsDO> newParams = Lists.newArrayList();
        transactionTemplate.execute(new TransactionCallback<Void>() {
            @Override
            public Void doInTransaction(TransactionStatus transactionStatus) {
                boolean updateTradeParamsForPayIsNullSuccess = false;
                try {
                    updateTradeParamsForPayIsNull(merchantInfo, subTaskDO);
                    updateTradeParamsForPayIsNullSuccess = true;
                    newParams.addAll(addNewTradeParamsAndBackUpOldParamsV3(mainTaskDO,subTaskDO));
                    deleteTerminalRelatedParams(subTaskDO);
                    deleteAllExceptMerchantLevelTradeExtConfig(mainTaskDO);
                } catch (Exception e) {
                    log.warn("小微升级商户更新sqb交易参数和终端处理报错,开始回滚原交易参数,sn={}, mainTaskId={}", mainTaskDO.getMerchantSn(), mainTaskDO.getId(), e);
                    transactionStatus.setRollbackOnly();
                    if (updateTradeParamsForPayIsNullSuccess) {
                        log.warn("更新sqb交易参数和终端处理报错,开始回滚原交易参数,交易侧也需要回滚,sn={}, mainTaskId={}", mainTaskDO.getMerchantSn(), mainTaskDO.getId());
                        rollbackTradeParamsForPayIsNullWithCatchException(merchantInfo, mainTaskDO, subTaskDO);
                    }
                    throw new ContractBizException("回滚失败", e);
                }
                return null;
            }
        });
        // todo 这里逻辑待优化，应该放在事务里，但是海科的contract服务接口会去查可能查不到，所以本期暂时放在事务外面（其实终端绑定任务本身也是异步的）
        addMerchantLevelStoreAndTerminalBindV3(mainTaskDO, subTaskDO, merchantInfo, newParams);
        acquirerSpecialProcessV3(mainTaskDO, subTaskDO);
        asyncExistedStoreAndTerminalBindV3(mainTaskDO, subTaskDO);
    }

    private void checkSubTaskDataVersionAndInitIfEmpty(InternalScheduleSubTaskDO subTaskDO) {
        if (StringUtils.isBlank(subTaskDO.getContext())) {
            log.error("子任务数据不完整,subTaskId={}", subTaskDO.getId());
            throw new ContractBizException("子任务数据不完整");
        }
        BusinessLicenceCertificationTask.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationTask.SubTaskContextBOInner.class);
        if (Objects.isNull(subTaskContextBOInner)) {
            log.error("子任务context数据为空,subTaskId={}", subTaskDO.getId());
            throw new ContractBizException("子任务数据不完整");
        }
        // 考虑备份数据和任务处理有时间差，交易参数会发生变更，这里重新查询一次
        String acquirer = subTaskDO.getAcquirer();
        Map<String/*acquirer*/, Map<String/*payWay*/, List<MerchantProviderParamsDO>>> acquirerParams = merchantTradeParamsBiz.getMerchantAcquirerParamsMap(subTaskDO.getMerchantSn());
        Map<String, List<MerchantProviderParamsDO>> paramsMap = acquirerParams.get(acquirer);
        if (org.apache.commons.collections4.MapUtils.isEmpty(paramsMap)) {
            log.warn("商户缺少交易参数,merchantSn={},acquirer={}", subTaskDO.getMerchantSn(), acquirer);
            throw new ContractBizException("商户缺少交易参数");
        }
        subTaskContextBOInner.setOldPayWayParamsMap(paramsMap);
        subTaskDO.setContext(JSON.toJSONString(subTaskContextBOInner));
    }



    private void checkSubTaskDataVersionAndInitIfEmptyV3(InternalScheduleSubTaskDO subTaskDO) {
        if (StringUtils.isBlank(subTaskDO.getContext())) {
            log.error("子任务数据不完整,subTaskId={}", subTaskDO.getId());
            throw new ContractBizException("子任务数据不完整");
        }
        BusinessLicenceCertificationV3Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV3Task.SubTaskContextBOInner.class);
        if (Objects.isNull(subTaskContextBOInner)) {
            log.error("子任务context数据为空,subTaskId={}", subTaskDO.getId());
            throw new ContractBizException("子任务数据不完整");
        }
        // 考虑备份数据和任务处理有时间差，交易参数会发生变更，这里重新查询一次
        String acquirer = subTaskDO.getAcquirer();
        Map<String/*acquirer*/, Map<String/*payWay*/, List<MerchantProviderParamsDO>>> acquirerParams = merchantTradeParamsBiz.getMerchantAcquirerParamsMap(subTaskDO.getMerchantSn());
        Map<String, List<MerchantProviderParamsDO>> paramsMap = acquirerParams.get(acquirer);
        if (org.apache.commons.collections4.MapUtils.isEmpty(paramsMap)) {
            log.warn("商户缺少交易参数,merchantSn={},acquirer={}", subTaskDO.getMerchantSn(), acquirer);
            throw new ContractBizException("商户缺少交易参数");
        }
        subTaskContextBOInner.setOldPayWayParamsMap(paramsMap);
        subTaskDO.setContext(JSON.toJSONString(subTaskContextBOInner));
    }

    private void rollbackTradeParamsForPayIsNullWithCatchException(MerchantInfo merchantInfo, InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        try {
            rollbackTradeParamsForPayIsNull(merchantInfo, mainTaskDO, subTaskDO);
        } catch (Exception e) {
            log.error("商户:{}, provider:{}, 回滚交易交易参数失败", mainTaskDO.getMerchantSn(), getProvider(), e);
        }
    }

    private void deleteAllExceptMerchantLevelTradeExtConfig(InternalScheduleMainTaskDO mainTaskDO) {
        BusinessLicenceCertificationTask.MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(),
                BusinessLicenceCertificationTask.MainTaskContextBOInner.class);
        CoreBTradeExtConfigRemoveRequest tradeExtConfigRemoveRequest = new CoreBTradeExtConfigRemoveRequest();
        tradeExtConfigRemoveRequest.setMerchantSn(mainTaskDO.getMerchantSn());
        tradeExtConfigRemoveRequest.setProvider(getProvider());
        tradeExtConfigRemoveRequest.setProviderMchId(mainTaskContextBOInner.getOldInUseAcquirerMerchantId());
        tradeExtConfigRemoveRequest.setSnTypes(Lists.newArrayList(CoreBTradeExtConfigRemoveRequest.SN_TYPE_STORE,
                CoreBTradeExtConfigRemoveRequest.SN_TYPE_TERMINAL,
                CoreBTradeExtConfigRemoveRequest.SN_TYPE_PROVIDER_MCH,
                CoreBTradeExtConfigRemoveRequest.SN_TYPE_STORE_SUB_MCH));
        try {
            coreBTradeConfigService.deleteTradeExtConfig(tradeExtConfigRemoveRequest);
        } catch (Exception e) {
            log.error("商户:{}, req:{}, 删除交易扩展配置失败", mainTaskDO.getMerchantSn(), tradeExtConfigRemoveRequest, e);
        }
    }

    abstract Integer getProvider();

    abstract void rollbackTradeParamsForPayIsNull(MerchantInfo merchantInfo, InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO);


    abstract void asyncExistedStoreAndTerminalBind(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO);

    abstract void asyncExistedStoreAndTerminalBindV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO);

    abstract void acquirerSpecialProcess(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO);

    abstract void acquirerSpecialProcessV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO);


    abstract void addMerchantLevelStoreAndTerminalBind(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO,
                                                       MerchantInfo merchantInfo, List<MerchantProviderParamsDO> newParams);

    abstract void addMerchantLevelStoreAndTerminalBindV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO,
                                                       MerchantInfo merchantInfo, List<MerchantProviderParamsDO> newParams);

    protected void deleteTerminalRelatedParams(InternalScheduleSubTaskDO subTaskDO) {
        deleteTerminalRelated(subTaskDO);
    }


    protected void addPayWayParamsTerminalBindTask(InternalScheduleSubTaskDO subTaskDO, List<MerchantProviderParamsDO> newParams) {
        for (MerchantProviderParamsDO newParam : newParams) {
            if (Objects.equals(PaywayEnum.ACQUIRER.getValue(), newParam.getPayway())) {
                continue;
            }
            ProviderTerminalContext context = ProviderTerminalContext.builder()
                    .subMerchant(newParam.getPayMerchantId())
                    .provider(newParam.getProvider())
                    .payWay(newParam.getPayway())
                    .build();
            ProviderTerminalTaskDO providerTerminalTaskDO = new ProviderTerminalTaskDO();
            providerTerminalTaskDO.setMerchantSn(subTaskDO.getMerchantSn());
            providerTerminalTaskDO.setType(ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType());
            providerTerminalTaskDO.setContext(JSON.toJSONString(context));
            // priority设置
            providerTerminalTaskDAO.insertOne(providerTerminalTaskDO);
        }
    }


    /**
     * 终端删除
     */
    private void deleteTerminalRelated(InternalScheduleSubTaskDO subTaskDO) {
        String merchantSn = subTaskDO.getMerchantSn();
        String provider = StringExtensionUtils.toSafeString(subTaskDO.getProvider());
        List<ProviderTerminalDO> providerTerminalDOS = providerTerminalDAO.listByMerchantSnAndProvider(merchantSn, provider);
        List<ProviderTerminalBindConfigDO> providerTerminalBindConfigDOS = providerTerminalBindConfigDAO.listByMerchantSnAndProvider(merchantSn, provider);
        providerTerminalDAO.batchDeleteByPrimaryKeys(providerTerminalDOS.stream().map(ProviderTerminalDO::getId).collect(Collectors.toList()));
        providerTerminalBindConfigDAO.batchDeleteByPrimaryKeys(providerTerminalBindConfigDOS.stream().map(ProviderTerminalBindConfigDO::getId).collect(Collectors.toList()));
    }

    abstract void updateTradeParamsForPayIsNull(MerchantInfo merchantInfo, InternalScheduleSubTaskDO subTaskDO);

    private List<MerchantProviderParamsDO> addNewTradeParamsAndBackUpOldParams(InternalScheduleSubTaskDO subTaskDO) {
        Map<String/*oldId*/, MerchantProviderParamsDO/*newParams*/> oldToNewParamsMap = buildNewNeedInsertParamsAndReturnOldNewMap(subTaskDO);
        BusinessLicenceCertificationTask.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationTask.SubTaskContextBOInner.class);
        merchantTradeParamsBiz.logicDeleteByIds(subTaskContextBOInner.getOldPayWayParamsMap().values().stream().flatMap(Collection::stream).map(MerchantProviderParamsDO::getId).collect(Collectors.toList()));
        List<SubBizParamsDO> oldSubBizParams = addNewSubBizParamsDeleteOldAndReturnOld(subTaskDO, oldToNewParamsMap);
        merchantTradeParamsBiz.batchInsertParams(new ArrayList<>(oldToNewParamsMap.values()));
        subTaskContextBOInner.setNewParamsIdList(oldToNewParamsMap.values().stream().map(MerchantProviderParamsDO::getId).collect(Collectors.toList()));
        subTaskContextBOInner.setOldSubBizParams(oldSubBizParams);
        subTaskDO.setContext(JSON.toJSONString(subTaskContextBOInner));
        return new ArrayList<>(oldToNewParamsMap.values());
    }


    private List<MerchantProviderParamsDO> addNewTradeParamsAndBackUpOldParamsV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        Map<String/*oldId*/, MerchantProviderParamsDO/*newParams*/> oldToNewParamsMap = buildNewNeedInsertParamsAndReturnOldNewMapV3(mainTaskDO,subTaskDO);
        BusinessLicenceCertificationV3Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV3Task.SubTaskContextBOInner.class);
        merchantTradeParamsBiz.logicDeleteByIds(subTaskContextBOInner.getOldPayWayParamsMap().values().stream().flatMap(Collection::stream).map(MerchantProviderParamsDO::getId).collect(Collectors.toList()));
        List<SubBizParamsDO> oldSubBizParams = addNewSubBizParamsDeleteOldAndReturnOld(subTaskDO, oldToNewParamsMap);
        merchantTradeParamsBiz.batchInsertParams(new ArrayList<>(oldToNewParamsMap.values()));
        subTaskContextBOInner.setNewParamsIdList(oldToNewParamsMap.values().stream().map(MerchantProviderParamsDO::getId).collect(Collectors.toList()));
        subTaskContextBOInner.setOldSubBizParams(oldSubBizParams);
        subTaskDO.setContext(JSON.toJSONString(subTaskContextBOInner));
        return new ArrayList<>(oldToNewParamsMap.values());
    }




    abstract Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMap(InternalScheduleSubTaskDO subTaskDO);


    /**
     * 基于一些基本信息构建新的商户参数,并且和之前老的Id做关联
     * @param mainTaskDO
     * @param subTaskDO
     * @return
     */
    abstract Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMapV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO);



    private List<SubBizParamsDO> addNewSubBizParamsDeleteOldAndReturnOld(InternalScheduleSubTaskDO subTaskDO, Map<String, MerchantProviderParamsDO> oldToNewParamsMap) {
        List<SubBizParamsDO> oldSubBizParams = subBizParamsDAO.listByMerchantSnAndProvider(subTaskDO.getMerchantSn(), subTaskDO.getProvider().toString());
        if (CollectionUtils.isEmpty(oldSubBizParams)) {
            return Collections.emptyList();
        }
        List<SubBizParamsDO> newSubBizParams = Lists.newArrayList();
        for (SubBizParamsDO oldSubBizParam : oldSubBizParams) {
            String extra = oldSubBizParam.getExtra();
            if (StringUtils.isBlank(extra)) {
                continue;
            }
            JSONObject jsonObject = JSON.parseObject(extra);
            List<String> paramsIdList =(List<String>) org.apache.commons.collections4.MapUtils.getObject(jsonObject, oldSubBizParam.getProvider().toString());
            if (CollectionUtils.isNotEmpty(paramsIdList)) {
                List<String> newParamsIdList = paramsIdList.stream().map(oldId -> {
                    if (oldToNewParamsMap.containsKey(oldId)) {
                        return oldToNewParamsMap.get(oldId).getId();
                    }
                    return oldId;
                }).collect(Collectors.toList());
                SubBizParamsDO newSubBizParamDO = BeanCopyUtils.copyProperties(oldSubBizParam, SubBizParamsDO.class);
                newSubBizParamDO.setId(null);
                Map<String, List<String>> newExtraMap = Maps.newHashMap();
                newExtraMap.put(String.valueOf(newSubBizParamDO.getProvider()), newParamsIdList);
                newSubBizParamDO.setExtra(JSON.toJSONString(newExtraMap));
                newSubBizParams.add(newSubBizParamDO);
            }
        }
        subBizParamsDAO.batchDeleteByPrimaryKeys(oldSubBizParams.stream().map(SubBizParamsDO::getId).collect(Collectors.toList()));
        subBizParamsDAO.batchInsert(newSubBizParams);
        return oldSubBizParams;
    }

    /**
     * 更新银联侧支付源绑定的银联商户号
     * 改方法已经考虑了回滚
     */
    private void updateUnionPayWayUnionMerchantId(InternalScheduleSubTaskDO subTaskDO, String newUnionMerchantId, String oldUnionMerchantId) {
        String acquirer = subTaskDO.getAcquirer();
        Optional<AcquirerSharedAbility> sharedAbilityByAcquirer = acquirerFacade.getSharedAbilityByAcquirer(acquirer);
        if (!sharedAbilityByAcquirer.isPresent()) {
            throw new ContractBizException("收单机构" + acquirer + "不支持");
        }
        BusinessLicenceCertificationTask.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationTask.SubTaskContextBOInner.class);
        AcquirerSharedAbility acquirerSharedAbility = sharedAbilityByAcquirer.get();
        Map<String, List<MerchantProviderParamsDO>> payWayParams = subTaskContextBOInner.getOldPayWayParamsMap();
        updateWxParamsUnionMerchantId(subTaskDO, newUnionMerchantId, oldUnionMerchantId, payWayParams.getOrDefault(PaywayEnum.WEIXIN.getValue().toString(), Collections.emptyList()), acquirerSharedAbility);
        updateAliParamsUnionMerchantId(subTaskDO, newUnionMerchantId, oldUnionMerchantId,
                payWayParams.getOrDefault(PaywayEnum.ALIPAY.getValue().toString(), Collections.emptyList()),
                acquirerSharedAbility, payWayParams.getOrDefault(PaywayEnum.WEIXIN.getValue().toString(), Collections.emptyList()));
    }

    private void updateAliParamsUnionMerchantId(InternalScheduleSubTaskDO subTaskDO, String newUnionMerchantId, String oldUnionMerchantId, List<MerchantProviderParamsDO> aliParams,
                                                AcquirerSharedAbility acquirerSharedAbility, List<MerchantProviderParamsDO> wxParams) {
        if (CollectionUtils.isEmpty(aliParams)) {
            return;
        }
        List<MerchantProviderParamsDO> successUpdateUnionAliParams = Lists.newArrayList();
        for (MerchantProviderParamsDO aliParam : aliParams) {
            boolean updateAliPaySuccess = acquirerSharedAbility.updatePayUnionMerchantIdToUnion(aliParam.getId(), newUnionMerchantId);
            if (updateAliPaySuccess) {
                successUpdateUnionAliParams.add(aliParam);
                sleepOneSecond();
                continue;
            }
            log.warn("商户更新支付宝银联商户号失败,开始回滚微信和已经更新的支付宝银联商户号,merchantSn={},payMerchantId={},unionMerchantId={}", subTaskDO.getMerchantSn(), aliParam.getPayMerchantId(), newUnionMerchantId);
            for (MerchantProviderParamsDO wxParam : wxParams) {
                boolean success = acquirerSharedAbility.updatePayUnionMerchantIdToUnion(wxParam.getId(), oldUnionMerchantId);
                if (!success) {
                    logAndNotifyWhenRollBackUnionMerchantIdFail(subTaskDO.getMerchantSn(), PaywayEnum.WEIXIN.getValue(), wxParam.getPayMerchantId(), oldUnionMerchantId);
                } else {
                    sleepOneSecond();
                }
            }
            for (MerchantProviderParamsDO successUpdateUnionAliParam : successUpdateUnionAliParams) {
                boolean success = acquirerSharedAbility.updatePayUnionMerchantIdToUnion(successUpdateUnionAliParam.getId(), oldUnionMerchantId);
                if (!success) {
                    logAndNotifyWhenRollBackUnionMerchantIdFail(subTaskDO.getMerchantSn(), PaywayEnum.ALIPAY.getValue(), successUpdateUnionAliParam.getPayMerchantId(), oldUnionMerchantId);
                } else {
                    sleepOneSecond();
                }
            }
            throw new ContractBizException("更新支付宝银联商户号失败");
        }
    }

    private void updateWxParamsUnionMerchantId(InternalScheduleSubTaskDO subTaskDO, String newUnionMerchantId, String oldUnionMerchantId,
                                               List<MerchantProviderParamsDO> wxParams, AcquirerSharedAbility acquirerSharedAbility) {
        if (CollectionUtils.isEmpty(wxParams)) {
            return;
        }
        List<MerchantProviderParamsDO> successUpdateUnionWxParams = Lists.newArrayList();
        for (MerchantProviderParamsDO wxParam : wxParams) {
            boolean updateWeiXinSuccess = acquirerSharedAbility.updatePayUnionMerchantIdToUnion(wxParam.getId(), newUnionMerchantId);
            if (updateWeiXinSuccess) {
                successUpdateUnionWxParams.add(wxParam);
                // 银联针对每个商户进行限流
                sleepOneSecond();
                continue;
            }
            log.error("商户更新微信银联商户号失败,merchantSn={},payMerchantId={},unionMerchantId={}", subTaskDO.getMerchantSn(), wxParam.getPayMerchantId(), newUnionMerchantId);
            for (MerchantProviderParamsDO successUpdateUnionWxParam : successUpdateUnionWxParams) {
                boolean success = acquirerSharedAbility.updatePayUnionMerchantIdToUnion(successUpdateUnionWxParam.getId(), oldUnionMerchantId);
                if (!success) {
                    logAndNotifyWhenRollBackUnionMerchantIdFail(subTaskDO.getMerchantSn(), PaywayEnum.WEIXIN.getValue(), successUpdateUnionWxParam.getPayMerchantId(), oldUnionMerchantId);
                } else {
                    sleepOneSecond();
                }
            }
            throw new ContractBizException("更新微信银联商户号失败");
        }
    }

    private void sleepOneSecond() {
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException e) {
            log.warn("sleepOneSecond error", e);
        }
    }

    private void logAndNotifyWhenRollBackUnionMerchantIdFail(String merchantSn, Integer payWay, String payMerchantId, String unionMerchantId) {
        log.error("商户回滚银联商户号失败,merchantSn={},payWay={},payMerchantId={},unionMerchantId={}", merchantSn, payWay, payMerchantId, unionMerchantId);
        chatBotUtil.sendMessageToMicroUpgradeChatBot("商户回滚银联商户号失败,merchantSn=" + merchantSn + ",payWay=" + payWay + ",payMerchantId=" + payMerchantId + ",unionMerchantId=" + unionMerchantId);
    }

    private void rollBackAllParamsUnionPayWayOldUnionMerchantId(InternalScheduleSubTaskDO subTaskDO, String oldUnionMerchantId) {
        log.warn("商户回滚所有支付源参数的银联商户号,merchantSn={},原银联商户号={}", subTaskDO.getMerchantSn(), oldUnionMerchantId);
        try {
            BusinessLicenceCertificationTask.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationTask.SubTaskContextBOInner.class);
            Map<String, List<MerchantProviderParamsDO>> oldPayWayParams = subTaskContextBOInner.getOldPayWayParamsMap();
            MapUtils.ifPresent(oldPayWayParams, PaywayEnum.WEIXIN.getValue().toString(), t -> {
                for (MerchantProviderParamsDO merchantProviderParamsDO : t) {
                    acquirerFacade.getSharedAbilityByAcquirer(subTaskDO.getAcquirer()).ifPresent(acquirerSharedAbility -> {
                        boolean success = acquirerSharedAbility.updatePayUnionMerchantIdToUnion(merchantProviderParamsDO.getId(), oldUnionMerchantId);
                        if (!success) {
                            logAndNotifyWhenRollBackUnionMerchantIdFail(subTaskDO.getMerchantSn(), PaywayEnum.WEIXIN.getValue(), merchantProviderParamsDO.getPayMerchantId(), oldUnionMerchantId);
                        }
                    });
                }
            });
            MapUtils.ifPresent(oldPayWayParams, PaywayEnum.ALIPAY.getValue().toString(), t -> {
                for (MerchantProviderParamsDO merchantProviderParamsDO : t) {
                    acquirerFacade.getSharedAbilityByAcquirer(subTaskDO.getAcquirer()).ifPresent(acquirerSharedAbility -> {
                        boolean success = acquirerSharedAbility.updatePayUnionMerchantIdToUnion(merchantProviderParamsDO.getId(), oldUnionMerchantId);
                        if (!success) {
                            logAndNotifyWhenRollBackUnionMerchantIdFail(subTaskDO.getMerchantSn(), PaywayEnum.ALIPAY.getValue(), merchantProviderParamsDO.getPayMerchantId(), oldUnionMerchantId);
                        }
                    });
                }
            });
        } catch (Exception e) {
            log.error("小微升级商户银联商户号回滚出现异常,:merchantSn={}, unionMerchantId={},", subTaskDO.getMerchantSn(), oldUnionMerchantId, e);
        }
    }

    public abstract String getContractTermNo(String merchantSn);

    public void updatePayMerchantConfigAndAppConfigParams(MerchantInfo merchantInfo, MerchantProviderParamsDO merchantProviderParamsDO) {
        try {
            updatePayMerchantConfigParams(merchantInfo, merchantProviderParamsDO);
        } catch (Exception e) {
            log.error("小微升级商户更新merchantConfig参数失败,merchantSn={},paramsId={}", merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getId(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("更新merchantConfig失败,merchantSn=" + merchantProviderParamsDO.getMerchantSn() + ",paramsId=" + merchantProviderParamsDO.getId());
        }
        try {
            updatePayMerchantAppConfigParams(merchantInfo, merchantProviderParamsDO);
        } catch (Exception e) {
            log.error("小微升级商户更新appConfig参数失败,merchantSn={},paramsId={}", merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getId(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("更新appConfig失败,merchantSn=" + merchantProviderParamsDO.getMerchantSn() + ",paramsId=" + merchantProviderParamsDO.getId());
        }
        refreshCacheToPay(merchantInfo);
    }

    private void updatePayMerchantConfigParams(MerchantInfo merchantInfo, MerchantProviderParamsDO merchantProviderParamsDO) {
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantInfo.getId(), merchantProviderParamsDO.getPayway());
        if (org.apache.commons.collections4.MapUtils.isEmpty(merchantConfig)) {
            log.warn("小微升级更新交易侧参数失败,不存在交易参数配置, merchantSn:{}, payWay:{}", merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getPayway());
            return;
        }
        if (!Objects.equals(org.apache.commons.collections4.MapUtils.getInteger(merchantConfig, MerchantConfig.PROVIDER), merchantProviderParamsDO.getProvider())) {
            log.warn("小微升级更新交易侧参数失败,交易参数配置与当前在用的交易参数provider不一致, merchantSn:{}, payWay:{}, cuaProvider:{}, payProvider:{}",
                    merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getPayway(),
                    merchantProviderParamsDO.getProvider(), org.apache.commons.collections4.MapUtils.getInteger(merchantConfig, MerchantConfig.PROVIDER));
            return;
        }
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
        String tradeParamsKey = org.apache.commons.collections4.MapUtils.getString(providerTradeParamsKey, merchantProviderParamsDO.getProvider().toString());
        Map params = org.apache.commons.collections4.MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
        if (org.apache.commons.collections4.MapUtils.isEmpty(params) || !params.containsKey(tradeParamsKey)) {
            return;
        }
        Map tradeParamMap = org.apache.commons.collections4.MapUtils.getMap(params, tradeParamsKey);
        tradeParamMap.put(getMerchantConfigProviderMerchantIdKey(), merchantProviderParamsDO.getProviderMerchantId());
        tradeConfigService.updateMerchantConfig(CollectionUtil.hashMap(
                MerchantConfig.PARAMS, params,
                DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID)));
    }

    private void updatePayMerchantAppConfigParams(MerchantInfo merchantInfo, MerchantProviderParamsDO merchantProviderParamsDO) {
        List<Map<String, Object>> appConfigs = tradeConfigService.getMerchantAppConfigByMerchantIdAndProvider(merchantInfo.getId(), merchantProviderParamsDO.getProvider());
        if (CollectionUtils.isEmpty(appConfigs)) {
            return;
        }
        for (Map<String, Object> appConfig : appConfigs) {
            if (Objects.equals(BeanUtil.getPropInt(appConfig, MerchantAppConfig.PAYWAY), merchantProviderParamsDO.getPayway())) {
                Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
                String tradeParamsKey = org.apache.commons.collections4.MapUtils.getString(providerTradeParamsKey, merchantProviderParamsDO.getProvider().toString());
                Map params = org.apache.commons.collections4.MapUtils.getMap(appConfig, MerchantConfig.PARAMS);
                if (org.apache.commons.collections4.MapUtils.isEmpty(params) || !params.containsKey(tradeParamsKey)) {
                    return;
                }
                Map tradeParamMap = org.apache.commons.collections4.MapUtils.getMap(params, tradeParamsKey);
                tradeParamMap.put(getMerchantConfigProviderMerchantIdKey(), merchantProviderParamsDO.getProviderMerchantId());
                tradeConfigService.updateMerchantAppConfig(CollectionUtil.hashMap(
                        MerchantConfig.PARAMS, params,
                        DaoConstants.ID, BeanUtil.getPropString(appConfig, DaoConstants.ID)));
            }
        }
    }

        private void refreshCacheToPay(MerchantInfo merchantInfo) {
        try {
            supportService.removeCachedParams(merchantInfo.getSn());
        } catch (Exception e) {
            log.error("小微升级商户更新交易侧参数后清除缓存失败,merchantSn={}", merchantInfo.getSn(), e);
        }
    }

    public abstract String getMerchantConfigProviderMerchantIdKey();
}