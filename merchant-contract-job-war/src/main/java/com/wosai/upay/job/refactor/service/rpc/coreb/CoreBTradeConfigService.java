package com.wosai.upay.job.refactor.service.rpc.coreb;

import com.wosai.upay.job.refactor.service.rpc.coreb.req.CoreBTradeExtConfigRemoveRequest;

/**
 * core-b交易配置服务
 *
 * <AUTHOR>
 * @date 2024/10/8 14:04
 */
public interface CoreBTradeConfigService {

    /**
     * 删除交易扩展配置
     *
     * @param request 删除请求
     */
    void deleteTradeExtConfig(CoreBTradeExtConfigRemoveRequest request);


    /**
     * 商户小微升级替换通道商户号时间
     *
     * @param merchantId        商户id
     * @param clearanceProvider 清算通道
     * @param successSwitchTime 成功切换时间
     */
    void updateMerchantSwitchMchTime(String merchantId,Integer clearanceProvider,long successSwitchTime);}
