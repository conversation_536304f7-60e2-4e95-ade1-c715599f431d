package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.biz.bankDirect.PabDirectBiz;
import com.wosai.upay.job.enume.PabContractStatusEnum;
import com.wosai.upay.job.enume.PabContractStatusEnumV2;
import com.wosai.upay.job.enume.PabSubmitStatusEnum;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.util.ObjectComparator;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.pingan.request.SubmitContractInfoRequest;
import com.wosai.upay.merchant.contract.model.pingan.response.GetAgreementStatusResponse;
import com.wosai.upay.merchant.contract.model.pingan.response.GetMchtApplyInfoResponse;
import com.wosai.upay.merchant.contract.service.PabService;
import com.wosai.upay.merchant.contract.service.PingAnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.bankDirect.HxbImportBiz.PAB_WEIXIN_CHANNELNO;
import static com.wosai.upay.job.biz.bankDirect.PabDirectBiz.ORG_ID;


/**
 * 处理平安相关需求
 *
 * <AUTHOR>
 */
@Slf4j
@Component(ProviderUtil.PAB_CHANNEL)
public class PabProvider extends AbstractProvider {

    public static final String PAB_SIGN_RULE = "pab-sign";
    public static final String PAB_RULE = "pab";
    @Lazy
    @Autowired(required = false)
    PabDirectBiz pabDirectBiz;
    @Autowired
    private PingAnService pingAnService;
    @Autowired
    private ContractSubTaskDAO contractSubTaskDAO;
    @Autowired
    private ContractTaskDAO contractTaskDAO;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;
    @Value("${pab_template_code}")
    private String pabTemplateCode;
    @Value("${pab_sign_dev_code}")
    private String pabSignDevCode;

    @Value("${pab_app_template_code}")
    public String pabAppTemplateCode;

    @Value("${pab_app_dev_code}")
    public String pabAppDevCode;

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;
    @Autowired
    private MerchantUserService merchantUserService;
    @Autowired
    private PabService pabService;

    @Autowired
    WechatQrCodeUtils wechatQrCodeUtils;

    @Override
    protected ContractSubTask produceInsertTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        // 初始化基础的子任务信息
        ContractSubTask subTask = initializeSubTask(merchantSn, event, contractRule);
        if (!isPabSignRule(contractRule)) {
            return subTask;
        }
        // 判断是否符合PAB签约规则
        Optional<ContractSubTaskDO> existingTask = contractSubTaskDAO.getByMerchantSnAndRule(
                merchantSn,
                ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue(),
                contractRule.getRule(),
                PaywayEnum.ACQUIRER.getValue()
        );

        if (!existingTask.isPresent()) {
            return subTask;
        }
        if (existingTask.isPresent() && !Objects.equals(existingTask.get().getStatus(), SubTaskStatus.SUCCESS.getVal())) {
            return subTask;
        }
        if (isExistFailContractTask(merchantSn)) {
            return subTask;
        }
        // 若签约信息无变化，则生成成功任务
        if (!isSubmitContractInfoChanged(existingTask.get(), paramContext)) {
            subTask.setStatus(SubTaskStatus.SUCCESS.getVal());
            subTask.setContract_id(existingTask.get().getContractId());
            subTask.setRequest_body(existingTask.get().getRequestBody());
            subTask.setResponse_body(existingTask.get().getResponseBody());
        }
        return subTask;
    }


    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        return null;
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {

        if (!ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
            return null;
        }
        Integer payWay = sub.getPayway();
        Map<String, String> contextParam = contractTask.getEventContext();
        ContractResponse contractResponse;
        //银行申请记录
        final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());

        if (Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue()) && Objects.equals(sub.getContract_rule(), PAB_SIGN_RULE)) {
            contractResponse = pingAnService.submitContractInfo(contextParam);
            if (contractResponse.isSuccess()) {
                notifyMerchantSign(contractTask.getMerchant_sn());
                // 待商户完成授权签约
                pabDirectBiz.recordViewProcess(apply, 10, new Date());
            }
            return contractResponse;
        }
        if (Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue()) && Objects.equals(sub.getContract_rule(), PAB_RULE)) {
            //获取签署使用的applyNo
            final Long pTaskId = sub.getP_task_id();
            final List<ContractSubTask> subTaskList = contractSubTaskMapper.selectByPTaskId(pTaskId);
            final ContractSubTask signTask = subTaskList.stream().filter(subTask -> Objects.equals(subTask.getPayway(), PaywayEnum.ACQUIRER.getValue()) && Objects.equals(subTask.getContract_rule(), PAB_SIGN_RULE))
                    .findFirst().orElseThrow(() -> new ContractBizException("商户还未签约成功"));
            contextParam.put("applyNo", signTask.getContract_id());
            //对于直接生成签约成功的任务,在执行商户信息提交的时候也改变一下签约的状态
            pabDirectBiz.recordViewProcess(apply, 20, new Date());
            contractResponse = pingAnService.contractMerchant(contextParam);
        } else {
            // 其他支付类型：微信，支付宝、云闪付
            contractResponse = pingAnService.contractMerchantOtherPayWay(sub);
            if (contractResponse.isSuccess() && payWay.equals(PaywayEnum.WEIXIN.getValue())) {
                // 银行审核通过后,进入商户待实名认证状态时,在crm中通知销售,告知微信实名认证方法
                final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
                notifyMerchant2WxAuth(contractTask);
            }
        }
        return contractResponse;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        return null;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        //配置微信支付目录
        WeixinConfig configs = agentAppidBiz.getConfig(merchantProviderParams.getMerchant_sn(), ProviderEnum.PROVIDER_PAB.getValue(), PAB_WEIXIN_CHANNELNO, merchantProviderParams.getPay_merchant_id());
        return pabService.wechatSubDevConfig(configs);
    }


    /**
     * 查询任务状态
     *
     * @param contractSubTask
     * @return
     */
    @Override
    protected ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        //签约状态
        if (Objects.equals(contractSubTask.getContract_rule(), PAB_SIGN_RULE)) {
            return pingAnService.getAgreementStatus(contractSubTask.getContract_id(), getOrgId(contractSubTask.getP_task_id()));
        }
        return pingAnService.queryContractStatusByContractId(contractSubTask, getOrgId(contractSubTask.getP_task_id()));
    }

    /**
     * 商户当前是否处于签约中
     *
     * @param contractSubTask
     * @param contractResponse
     * @return
     */
    @Override
    public Boolean doCheck(ContractSubTask contractSubTask, ContractResponse contractResponse) {
        final HandleQueryStatusResp handleQueryStatusResp = handleContractStatus(contractSubTask, contractResponse);
        return handleQueryStatusResp.isRetry();
    }

    /**
     * 初始化并返回包含基础属性的ContractSubTask对象
     *
     * @param merchantSn   商户编号
     * @param event        合同事件对象
     * @param contractRule 签约规则对象
     * @return 初始化后的ContractSubTask对象
     */
    private ContractSubTask initializeSubTask(String merchantSn, ContractEvent event, ContractRule contractRule) {
        return new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(contractRule.getInsertInfluPtask())
                .setChannel(getProviderBeanName())
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT)
                .setChange_config(contractRule.getDefault())
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setRetry(0);
    }


    /**
     * 发送签约通知
     *
     * @param merchantSn
     */
    public void notifyMerchantSign(String merchantSn) {
        //获取商户信息
        final Map merchant = merchantService.getMerchantBySn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, com.wosai.data.dao.DaoConstants.ID);
        // 1:给商户超管角色发送通知（收钱吧APP)
        try {
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            if (userInfo != null) {
                final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
                sendModel.setDevCode(pabSignDevCode);
                sendModel.setTemplateCode(pabTemplateCode);
                sendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                sendModel.setTimestamp(System.currentTimeMillis());
                sendModel.setData(CollectionUtil.hashMap("id", BeanUtil.getPropString(merchant, DaoConstants.ID)));
                log.info("给商户超管角色发送签约通知 merchantSn:{},消息内容:{}", merchantSn, JSONObject.toJSONString(sendModel));
                clientSideNoticeService.sendToMerchantUser(sendModel);
            }
        } catch (Exception e) {
            log.error("给商户超管角色发送通知异常 merchantSn:{},异常信息{}", merchantSn, e);
        }
    }

    /**
     * 判断给定的签约规则是否为PAB签约规则
     *
     * @param contractRule 签约规则对象
     * @return 如果是PAB签约规则则返回true，否则返回false
     */
    private boolean isPabSignRule(ContractRule contractRule) {
        // 比较规则是否为PAB_SIGN_RULE
        return Objects.equals(contractRule.getRule(), PAB_SIGN_RULE);
    }

    /**
     * 检查任务的处理状态是否为成功状态
     *
     * @param taskDO ContractSubTaskDO对象
     * @return 如果任务状态为成功状态则返回true，否则返回false
     */
    private boolean isTaskProcessSuccessful(ContractSubTaskDO taskDO) {
        // 比较任务的状态是否为PROCESS_SUCCESS
        return Objects.equals(taskDO.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue());
    }

    /**
     * 判断商户是否存在失败的进件任务
     * @param merchantSn 商户sn
     * @return 存在失败的rule=pab的子任务，返回true；否则返回false
     */
    public boolean isExistFailContractTask(String merchantSn) {
        // 寻找符合PAB规则的失败子任务
        Optional<ContractSubTaskDO> failContractSubTask = contractSubTaskDAO.getByMerchantSnAndStatus(
                merchantSn,
                ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue(),
                PAB_RULE,
                ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue()
        );
        return failContractSubTask.isPresent();
    }

    /**
     * 比较现有签约信息与新提交的签约信息是否一致
     *
     * @param subTaskDO    上次提交的任务数据对象
     * @param paramContext 本次提交的参数上下文
     * @return 如果签约信息有变化则返回true，无变化则返回false
     */
    private boolean isSubmitContractInfoChanged(ContractSubTaskDO subTaskDO, Map<String, Object> paramContext) {
        Optional<ContractTaskDO> contractTaskDO = contractTaskDAO.getByPrimaryKey(subTaskDO.getPTaskId());

        // 检查是否存在旧的签约任务记录
        if (contractTaskDO.isPresent()) {
            // 解析旧的提交签约信息
            String eventContext = contractTaskDO.get().getEventContext();
            SubmitContractInfoRequest oldInfo = pingAnService.getSubmitContractInfo(JSONObject.parseObject(eventContext, Map.class));

            // 解析本次提交的签约信息
            SubmitContractInfoRequest newInfo = pingAnService.getSubmitContractInfo(paramContext);

            // 比较签约信息中指定字段是否相同，若有变化返回true
            return !ObjectComparator.compare(oldInfo, newInfo, Sets.newHashSet("applyNo", "requestTime", "orgId"));
        }

        // 如果旧数据不存在，则认为信息已变化
        return true;
    }

    /**
     * 获取组织id
     *
     * @param taskId
     * @return
     */
    public String getOrgId(Long taskId) {
        final Optional<ContractTaskDO> contractTaskDO = contractTaskDAO.getByPrimaryKey(taskId);
        final String eventContext = contractTaskDO.get().getEventContext();
        final String orgId = BeanUtil.getPropString(JSONObject.parseObject(eventContext, Map.class), ORG_ID);
        return orgId;
    }

    /**
     * 处理从收单机构查询的状态,判断是过程中或者成功或者失败
     *
     * @param contractSubTask
     * @param response
     * @return
     */
    @Override
    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        return doHandleContractStatus(contractSubTask, response);
    }


    /**
     * 根据查询结果处理合同状态。
     *
     * @param contractSubTask 子任务对象
     * @param response        合同响应对象
     * @return 处理后的查询状态响应
     */
    public HandleQueryStatusResp doHandleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        if (response.isBusinessFail()) {
            return createFailResp(response.getMessage());
        } else if (response.isSystemFail()) {
            return createRetryResp(response.getMessage());
        }

        return handlePabStatus(contractSubTask, response);
    }

    /**
     * 创建失败响应。
     *
     * @param message 错误消息
     * @return 失败响应对象
     */
    private HandleQueryStatusResp createFailResp(String message) {
        return new HandleQueryStatusResp()
                .setFail(true)
                .setMessage(message);
    }

    /**
     * 创建可重试响应。
     *
     * @param message 重试消息
     * @return 可重试响应对象
     */
    private HandleQueryStatusResp createRetryResp(String message) {
        return new HandleQueryStatusResp()
                .setRetry(true)
                .setMessage(message);
    }

    /**
     * 根据具体的合同规则处理合同状态。
     *
     * @param contractSubTask 子任务对象
     * @param response        合同响应对象
     * @return 处理后的查询状态响应
     */
    private HandleQueryStatusResp handlePabStatus(ContractSubTask contractSubTask, ContractResponse response) {
        final String originResult = contractSubTask.getResult();
        final String contractRule = contractSubTask.getContract_rule();
        final Long pTaskId = contractSubTask.getP_task_id();
        final BankDirectApply apply = getBankDirectApplyByTaskId(pTaskId);

        if (Objects.equals(contractRule, PAB_SIGN_RULE)) {
            return handlePabSign(contractSubTask, response, originResult, apply);
        } else {
            return handleMerchantContract(contractSubTask, response, originResult, apply);
        }
    }

    /**
     * 获取银行直连申请任务。
     *
     * @param taskId 任务ID
     * @return 银行直连申请任务
     */
    private BankDirectApply getBankDirectApplyByTaskId(Long taskId) {
        return bankDirectApplyMapper.getApplyByTaskId(taskId);
    }

    /**
     * 处理PAB签约状态。
     *
     * @param contractSubTask 子任务对象
     * @param response        合同响应对象
     * @param originResult    原始结果
     * @param apply           银行直连申请任务
     * @return 查询状态响应
     */
    private HandleQueryStatusResp handlePabSign(ContractSubTask contractSubTask, ContractResponse response, String originResult, BankDirectApply apply) {
        final GetAgreementStatusResponse agreementStatusResponse = cn.hutool.core.bean.BeanUtil.toBean(response.getResponseParam(), GetAgreementStatusResponse.class);
        final String agreementStatus = agreementStatusResponse.getData().getAgreementStatus();
        final String message = PabSubmitStatusEnum.getMessage(agreementStatus);
        updateSubAndParentTaskResult(contractSubTask.getId(), originResult, "签约状态:" + message);
        if (PabSubmitStatusEnum.WAIT.getCode().equals(agreementStatus)) {
            return createRetryResp("签约中");
        } else if (PabSubmitStatusEnum.DONE.getCode().equals(agreementStatus)) {
            pabDirectBiz.recordViewProcess(apply, 20, new Date());
            return createSuccessResp("签约完成");
        } else {
            return createFailResp("签约失败");
        }

    }

    /**
     * 处理商户入网状态。
     *
     * @param contractSubTask 子任务对象
     * @param response        合同响应对象
     * @param originResult    原始结果
     * @param apply           银行直连申请任务
     * @return 查询状态响应
     */
    private HandleQueryStatusResp handleMerchantContract(ContractSubTask contractSubTask, ContractResponse response, String originResult, BankDirectApply apply) {
        final GetMchtApplyInfoResponse getMchtApplyInfoResponse = cn.hutool.core.bean.BeanUtil.toBean(response.getResponseParam(), GetMchtApplyInfoResponse.class);
        final String auditStatus = getMchtApplyInfoResponse.getData().getAuditStatus();
        final String auditRemark = getMchtApplyInfoResponse.getData().getAuditRemark();
        final boolean pabStatusV2 = applicationApolloConfig.getPabStatusV2();
        final String message = pabStatusV2 ? PabContractStatusEnumV2.getMessage(auditStatus) : PabContractStatusEnum.getMessage(auditStatus);
        updateSubAndParentTaskResult(contractSubTask.getId(), originResult, "入网状态:" + message + ":" + auditRemark);
        if (pabStatusV2) {
            //0-审核中 1-审核通过但是没有支付宝和微信子商户号认为还是审核中   2-审核中-补件 3-审核拒绝 4-进件成功
            if (Lists.newArrayList(PabContractStatusEnumV2.WAIT.getCode(), PabContractStatusEnumV2.APPROVED.getCode()).contains(auditStatus)) {
                return createRetryResp("审核中");
            } else if (PabContractStatusEnumV2.FAIL.getCode().equals(auditStatus)) {
                return createFailResp(message + ":" + auditRemark);
            } else if (PabContractStatusEnumV2.DONE.getCode().equals(auditStatus)) {
                pabDirectBiz.recordViewProcess(apply, 41, new Date());
                return createSuccessResp(message + ":" + auditRemark);
            } else if (PabContractStatusEnumV2.PENDING_WITH_COMPLETION.getCode().equals(auditStatus)) {
                //添加需进行补件提示
                Map<String, Object> extraMap = apply.getExtraMap();
                //原有记录
                final String processStr = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.VIEW_PROCESS);
                final List<ViewProcess> viewProcess = JSONObject.parseArray(processStr, ViewProcess.class);
                final List<ViewProcess> processList = viewProcess.stream().map(view -> {
                    if (Objects.equals(view.getViewStatus(), 20) && !view.getRemark().contains(auditRemark)) {
                        String newRemark = view.getRemark() + "\n" + "银行审核中，需进行补件，" + auditRemark;
                        view.setRemark(newRemark);
                    }
                    return view;
                }).collect(Collectors.toList());
                extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, processList);
                apply.setExtra(JSONObject.toJSONString(extraMap));
                bankDirectApplyMapper.updateByPrimaryKeySelective(apply);
                return createRetryResp(PabContractStatusEnumV2.PENDING_WITH_COMPLETION.getMessage());
            } else {
                return createFailResp("入网审核失败");
            }
        } else {
            //"0:审核中 1:通过 2:拒绝";
            if (PabContractStatusEnum.WAIT.getCode().equals(auditStatus)) {
                return createRetryResp("审核中");
            } else if (PabContractStatusEnum.FAIL.getCode().equals(auditStatus)) {
                return createFailResp(message + ":" + auditRemark);
            } else if (PabContractStatusEnum.DONE.getCode().equals(auditStatus)) {
                pabDirectBiz.recordViewProcess(apply, 41, new Date());
                return createSuccessResp(message + ":" + auditRemark);
            } else {
                return createFailResp("入网审核失败");
            }
        }

    }

    /**
     * 创建成功响应。
     *
     * @param message 成功消息
     * @return 成功响应对象
     */
    private HandleQueryStatusResp createSuccessResp(String message) {
        return new HandleQueryStatusResp()
                .setSuccess(true)
                .setMessage(message);
    }

    /**
     * 更新子任务及其父任务的状态结果。
     *
     * @param id           子任务ID
     * @param originResult 原始结果
     * @param targetResult 目标结果
     */
    public void updateSubAndParentTaskResult(Long id, String originResult, String targetResult) {
        if (Objects.equals(originResult, targetResult)) {
            return;
        }

        ContractSubTask subTask = new ContractSubTask();
        subTask.setId(id);
        subTask.setResult(targetResult);
        contractSubTaskMapper.updateByPrimaryKey(subTask);

        final ContractSubTask updatedSubTask = contractSubTaskMapper.selectByPrimaryKey(id);
        final Long pTaskId = updatedSubTask.getP_task_id();
        final ContractTask task = contractTaskMapper.selectByPrimaryKey(pTaskId);

        if (Objects.equals(task.getResult(), targetResult)) {
            return;
        }

        if (Objects.equals(updatedSubTask.getStatus_influ_p_task(), 1)) {
            ContractTask contractTask = new ContractTask();
            contractTask.setId(pTaskId);
            contractTask.setResult(targetResult);
            contractTask.setPriority(task.getPriority());
            contractTaskMapper.updateByPrimaryKey(contractTask);
        }
    }


    /**
     * <AUTHOR>
     * @Description: 收钱吧app中通知老板, 告知微信实名
     * @time 17:13
     */
    public void notifyMerchant2WxAuth(ContractTask contractTask) {
        try {
            String merchantChannelCode = wechatQrCodeUtils.authorizationCodeUrl(Maps.newHashMap(), "pab-1040-3");
            final String imageUrl = HxbProvider.replaceHttp(merchantChannelCode);
            //给商户超管角色发送通知（收钱吧APP)
            //获取商户信息
            final String merchantSn = contractTask.getMerchant_sn();
            final Map merchant = merchantService.getMerchantBySn(merchantSn);
            final String merchantId = BeanUtil.getPropString(merchant, com.wosai.data.dao.DaoConstants.ID);
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            //生成二维码和文案
            if (userInfo != null) {
                final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
                sendModel.setDevCode(pabAppDevCode);
                sendModel.setTemplateCode(pabAppTemplateCode);
                sendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                sendModel.setTimestamp(System.currentTimeMillis());
                //需要传的数据
                sendModel.setData(CollectionUtil.hashMap("text1", WosaiMapUtils.getString(merchant, Merchant.NAME) + ",平安活动审核通过,请用微信扫描下方平安服务商授权码,完成微信实名认证",
                        "text2", "认证通过后,方可参加活动",
                        "qrCodeImg", imageUrl));
                log.info("平安给收钱吧app发送微信实名通知参数{},商户号:{}", JSONObject.toJSONString(sendModel), contractTask.getMerchant_sn());
                clientSideNoticeService.sendToMerchantUser(sendModel);
            }
        } catch (Exception e) {
            log.error("平安给收钱吧app发送微信实名通知异常id:{},异常信息{}", contractTask.getId(), e);
        }
    }

}
