package com.wosai.upay.job.refactor.model.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.Constants.OnlinePaymentConstant;
import com.wosai.upay.job.refactor.model.bo.*;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 子任务表表实体对象
 *
 * <AUTHOR>
 */
@TableName("open_online_payment_apply")
@Data
public class OpenOnlinePaymentApplyDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户sn 方便查询
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;

    @TableField(value = "payway")
    private Integer payway;

    @TableField(value = "acquirer")
    private String acquirer;
    /**
     * 申请单状态
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 申请单状态
     */
    @TableField(value = "process_status")
    private Integer processStatus;
    /**
     * 备注
     */
    @TableField(value = "result")
    private String result;
    /**
     * 开通过程
     */
    @TableField(value = "process")
    private String process;
    /**
     * 其他信息
     */
    @TableField(value = "extra")
    private String extra;

    @TableField(value = "priority")
    private Timestamp priority;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;


    public Boolean isAliApply() {
        return PaywayEnum.ALIPAY.getValue().equals(payway);
    }

    public Boolean isWeixinApply() {
        return PaywayEnum.WEIXIN.getValue().equals(payway);
    }

    public Boolean isWaitForAudit() {
        return OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT.equals(processStatus);
    }

    public Boolean isEffect() {
        if (!OnlinePaymentConstant.ApplyStatus.SUCCESS.equals(status)) {
            return false;
        }
        OnlinePaymentApplyExtraBO onlinePaymentApplyExtraBO = JSON.parseObject(extra, OnlinePaymentApplyExtraBO.class);
        if (Objects.isNull(onlinePaymentApplyExtraBO)) {
            return false;
        }
        return onlinePaymentApplyExtraBO.getEffect();
    }

    public String getPayMerchantId() {
        OnlinePaymentApplyExtraBO onlinePaymentApplyExtraBO = JSON.parseObject(extra, OnlinePaymentApplyExtraBO.class);
        if (Objects.isNull(onlinePaymentApplyExtraBO)) {
            return null;
        }
        if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
            OnlinePaymentApplyWeixinBO weixin = onlinePaymentApplyExtraBO.getWeixin();
            if (Objects.isNull(weixin)) {
                return null;
            }
            return weixin.getPayMerchantId();
        }
        if (PaywayEnum.ALIPAY.getValue().equals(payway)) {
            OnlinePaymentApplyAliBO ali = onlinePaymentApplyExtraBO.getAli();
            if (Objects.isNull(ali)) {
                return null;
            }
            return ali.getPayMerchantId();
        }
        return null;
    }

    public OnlinePaymentApplyComboDetailBO getOnlinePaymentComboDetail() {
        OnlinePaymentApplyExtraBO onlinePaymentApplyExtraBO = JSON.parseObject(extra, OnlinePaymentApplyExtraBO.class);
        if (Objects.isNull(onlinePaymentApplyExtraBO) || Objects.isNull(onlinePaymentApplyExtraBO.getCombo())) {
            return null;
        }
        return onlinePaymentApplyExtraBO.getCombo().getOnline();
    }

    public OnlinePaymentApplyComboDetailBO getCrossCityPaymentComboDetail() {
        OnlinePaymentApplyExtraBO onlinePaymentApplyExtraBO = JSON.parseObject(extra, OnlinePaymentApplyExtraBO.class);
        if (Objects.isNull(onlinePaymentApplyExtraBO) || Objects.isNull(onlinePaymentApplyExtraBO.getCombo())) {
            return null;
        }
        return onlinePaymentApplyExtraBO.getCombo().getCrossCity();
    }

    public static final List<OnlinePaymentApplyProcessBO> WEIXIN_INIT_PROCESS = Arrays.asList(
            new OnlinePaymentApplyProcessBO().setStage(OnlinePaymentApplyProcessBO.CONTRACTING).setStageName("微信子商户号报备").setMessage("当前微信收款商户号报备中，预计5分钟，请稍后刷新，如仍不能操作请咨询在线客服").setFinish(false),
            new OnlinePaymentApplyProcessBO().setStage(OnlinePaymentApplyProcessBO.AUTHING).setStageName("微信认证").setMessage("前往微信认证").setFinish(false),
            new OnlinePaymentApplyProcessBO().setStage(OnlinePaymentApplyProcessBO.SUCCESS).setStageName("开通成功").setFinish(false)
    );

    public static final List<OnlinePaymentApplyProcessBO> ALIPAY_INIT_PROCESS = Arrays.asList(
            new OnlinePaymentApplyProcessBO().setStage(OnlinePaymentApplyProcessBO.CONTRACTING).setStageName("支付宝子商户号报备").setMessage("当前支付宝收款商户号报备中，预计5分钟，请稍后刷新，如仍不能操作请咨询在线客服").setFinish(false),
            new OnlinePaymentApplyProcessBO().setStage(OnlinePaymentApplyProcessBO.AUTHING).setStageName("支付宝认证").setMessage("前往支付宝认证").setFinish(false),
            new OnlinePaymentApplyProcessBO().setStage(OnlinePaymentApplyProcessBO.AUDITING).setStageName("支付宝审核").setMessage("预计次日审核完成").setFinish(false),
            new OnlinePaymentApplyProcessBO().setStage(OnlinePaymentApplyProcessBO.SUCCESS).setStageName("开通成功").setFinish(false)
    );

    public static List<OnlinePaymentApplyProcessBO> initProcessList(Integer payway) {
        if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
            return WEIXIN_INIT_PROCESS;
        } else if (PaywayEnum.ALIPAY.getValue().equals(payway)) {
            return ALIPAY_INIT_PROCESS;
        }
        throw new ContractBizException("暂不支持该payway");
    }

    public List<OnlinePaymentApplyProcessBO> processList() {
        if (WosaiStringUtils.isEmpty(process)) {
            // 搞一个新对象，防止对同一个对象进行操作造成的问题
            if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
                return JSON.parseArray(JSON.toJSONString(WEIXIN_INIT_PROCESS), OnlinePaymentApplyProcessBO.class);
            }
            if (PaywayEnum.ALIPAY.getValue().equals(payway)) {
                return JSON.parseArray(JSON.toJSONString(ALIPAY_INIT_PROCESS), OnlinePaymentApplyProcessBO.class);
            }
        }
        return JSON.parseArray(process, OnlinePaymentApplyProcessBO.class);
    }
}

