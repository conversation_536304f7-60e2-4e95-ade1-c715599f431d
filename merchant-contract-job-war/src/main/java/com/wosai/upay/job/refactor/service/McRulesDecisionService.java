package com.wosai.upay.job.refactor.service;

import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRulesDecisionDO;
import com.wosai.upay.job.refactor.model.enums.NetInSceneEnum;
import com.wosai.upay.merchant.contract.model.Tuple2;

import java.util.Set;


/**
 * 进件规则决策表表Service层 {@link GroupRouteRulesDecisionDO}
 *
 * <AUTHOR>
 */
public interface McRulesDecisionService {


    /**
     * 根据商户号和所属推广组织获取进件通道组
     *
     * @param merchantSn     商户号
     * @param organizationId 组织id
     * @param netInScene     入网场景
     * @return 进件报备规则组策略组合detail列表 禁用原因
     */
    Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> chooseGroupBySnAndOrg(String merchantSn, String organizationId, NetInSceneEnum netInScene) ;


    /**
     * 根据商户特征获取进件通道组
     *
     * @param merchantFeature 商户特征
     * @return 进件报备规则组策略组合detail列表 禁用原因
     */
    Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> chooseGroupByMerchantFeature(MerchantFeatureBO merchantFeature);

    /**
     * 校验商户是否可以入指定的收单机构
     * 默认是切收单机构场景
     *
     * @param merchantSn       商户号
     * @param acquirer         收单机构
     * @param netInScene       入网场景
     * @return 校验结果
     */
    ContractGroupRuleVerifyResultBO checkMerchantEligibilityToAcquirer(String merchantSn, String acquirer, NetInSceneEnum netInScene);

    /**
     * 门店开通间连扫码，校验门店是否可以入指定的收单机构
     *
     * @param merchantSn       商户号
     * @param storeId          门店号
     * @param netInScene       入网场景
     * @return 校验结果
     */
    ContractGroupRuleVerifyResultBO checkStoreEligibilityToAcquirer(String merchantSn, String storeId, NetInSceneEnum netInScene);
}
