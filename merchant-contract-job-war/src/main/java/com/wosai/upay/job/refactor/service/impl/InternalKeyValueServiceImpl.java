package com.wosai.upay.job.refactor.service.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.refactor.dao.InternalKeyValueDAO;
import com.wosai.upay.job.refactor.model.entity.InternalKeyValueDO;
import com.wosai.upay.job.service.InternalKeyValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 内部key value表Service层 {@link InternalKeyValueDO}
 *
 * <AUTHOR>
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class InternalKeyValueServiceImpl implements InternalKeyValueService {

    @Resource
    private InternalKeyValueDAO internalKeyValueDAO;

    /**
     * 新增一条键值对,存在则更新
     *
     * @param key   键
     * @param value 值
     * @return effect rows
     */
    @Override
    public int insertOrUpdateKeyValue(String key, String value) {
        return internalKeyValueDAO.insertOrUpdateKeyValue(key, value);
    }

    /**
     * 根据键获取值
     *
     * @param key 键
     * @return 值
     */
    @Override
    public String getValueByKey(String key) {
        return internalKeyValueDAO.getValueByKey(key).orElse(null);
    }
}
