package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.aop.gateway.model.ClientSideNoticeSendModel;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.sales.core.model.User;
import com.wosai.sales.core.service.UserService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.ProviderTerminalIdBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.bankDirect.PsbcDirectBiz;
import com.wosai.upay.job.enume.PsbcStatusEnum;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import com.wosai.upay.merchant.contract.constant.ContractSubTaskTaskType;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.PsbcParam;
import com.wosai.upay.merchant.contract.model.psbc.bo.MerchantStatusCallBack;
import com.wosai.upay.merchant.contract.model.psbc.response.OutMerInfoStatusQueryResponse;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.service.PsbcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Description: 邮储银行处理类
 * <AUTHOR>
 * @Date 2021/4/6 15:35
 */
@Component(ProviderUtil.PSBC_CHANNEL)
@Slf4j
@Transactional
public class PsbcProvider extends AbstractProvider {

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private PsbcService psbcService;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantUserService merchantUserService;

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    PayWayConfigChangeMapper payWayConfigChangeMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    WechatQrCodeUtils wechatQrCodeUtils;
    @Lazy
    @Autowired
    PsbcDirectBiz psbcDirectBiz;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private ProviderTerminalIdBiz providerTerminalIdBiz;

    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;

    @Value("${psbc_template_code}")
    private String psbcTemplateCode;

    @Value("${psbc_bank_notice_dev_code}")
    public String bankNoticeDevCode;

    @Value("${psbc_business_dev_code}")
    public String psbcBusinessDevCode;

    @Value("${psbc_crm_template_code}")
    public String psbcCrmTemplateCode;

    @Value("${psbc_crm_dev_code}")
    public String psbcCrmDevCode;

    @Value("${psbc_app_template_code}")
    public String psbcAppTemplateCode;

    @Value("${psbc_app_dev_code}")
    public String psbcAppDevCode;
    /**
     * 营业执照是小微或者个体
     */
    public static final List<Integer> typeList = Lists.newArrayList(0, 1);

    @Autowired
    UserService userService;
    /**
     * 回调成功集合
     */
    public static final List<String> SUCCESS_LIST = Lists.newArrayList("01", "04");

    /**
     * 商户失败结合
     */
    public static final List<String> BAN_LIST = Lists.newArrayList("02", "03");

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        //邮储限制每次只能审批一次查询出商户目前状态为处理中的任务数量 若>0 则不进行处理
        List<ContractTask> pendingTasks = contractTaskMapper.selectTaskTodoByMerchantSn(merchantSn);
        if (WosaiCollectionUtils.isNotEmpty(pendingTasks) && pendingTasks.stream().anyMatch(task -> task.getRule_group_id().contains(McConstant.RULE_GROUP_PSBC))) {
            log.info("商户号sn:{},存在邮储未完成或处理中任务ids:{},不可以再次创建任务"
                    , merchantSn, pendingTasks.parallelStream().filter(task -> task.getRule_group_id().contains(McConstant.RULE_GROUP_PSBC)).map(ContractTask::getId).collect(Collectors.toList()));
            return null;
        }
        //只需要更新收单机构信息,支付宝和微信信息不更新
        if (!Objects.equals(contractRule.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
            return null;
        }
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        // 非当前收单机构，更新不影响总状态
        int influPtask = AcquirerTypeEnum.PSBC.getValue().equals(acquirer) ? contractRule.getUpdateInfluPtask() : 0;
        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.PSBC_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);

        Integer taskType = null;

        if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
            //TODO 需要更新银行卡
            Map requestParam = (Map) paramContext.get("cardRequestParam");
            if (!CollectionUtils.isEmpty(requestParam)) {
                //银行卡管理服务发起的变更(merchant_bank_account_pre)
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
            } else {
                //dts订阅直接变更(merchant_bank_account)
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
            }
        } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
            Map eventMsg = JSON.parseObject(event.getEvent_msg(), Map.class);
            // 独立行业变更邮储不要强制更新
            if (WosaiMapUtils.getBooleanValue(eventMsg, ContractEvent.FORCE_UPDATE)) {
                return null;
            }
            List<String> changeFileds = (List) eventMsg.get("msg");
            if (WosaiCollectionUtils.isNotEmpty(changeFileds) && ProviderUtil.hasStatus(changeFileds)) {
                //状态暂时不同步给邮储
//                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE;
                return null;
            } else if (WosaiCollectionUtils.isNotEmpty(changeFileds) && ProviderUtil.triggerPsbcUpdate(changeFileds)) {
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
            }
        } else if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type() && Objects.equals(contractRule.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
            // TODO 更新费率 等待三期
            return null;
        } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {//不支持
            return null;
//            String crmUpdate = BeanUtil.getPropString(paramContext, "crmUpdate");
//            if (!StringUtils.isEmpty(crmUpdate)) {
//                if ("0".equals(crmUpdate)) {
//                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
//                } else if ("1".equals(crmUpdate)) {//不支持crm更新账户
//                    taskType = null;
//                } else if ("2".equals(crmUpdate)) {//不支持crm同时更新账户和商户信息
//                    taskType = null;
//                } else {
//                    //do nothing
//                }
//            }
        } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
            //更新营业执照
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
        }
        if (taskType == null) {
            return null;
        }
        return subTask.setTask_type(taskType);
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        PsbcParam psbcParam = buildParam(contractChannel, sub, PsbcParam.class);
        if (payWay.equals(PaywayEnum.ACQUIRER.getValue())) {
            return psbcService.contractMerchant(contextParam, psbcParam);
        } else if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            final String merId = getMerId(sub);
            final ContractResponse contractResponse = psbcService.contractAlipayWithParams(contextParam, merId, sub, psbcParam);
            //支付源报备失败
            if (contractResponse.isBusinessFail()) {
                //注销在邮储的商户
                psbcService.outMerInfoStatusUpdate(contractTask.getMerchant_sn(), "02", null);
            }
            return contractResponse;
        } else if (payWay.equals(PaywayEnum.WEIXIN.getValue())) {
            final String merId = getMerId(sub);
            final ContractResponse contractResponse = psbcService.contractWXWithParams(contextParam, merId, sub, psbcParam);
            //进件微信成功
            if (contractResponse.isSuccess()) {
                //TODO 银行审核通过后,进入商户待实名认证状态时,在crm中通知销售,告知微信实名认证方法
                final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
                final String crmUserId = BeanUtil.getPropString(JSONObject.parseObject(bankDirectApply.getForm_body(), Map.class), BankDirectApplyConstant.CRM_UESRID);
                //通知crm和商户老板
                notifyCrmAndMerchant(crmUserId, contractTask);
            }
            if (contractResponse.isBusinessFail()) {
                //注销在邮储的商户
                psbcService.outMerInfoStatusUpdate(contractTask.getMerchant_sn(), "02", null);
            }
            return contractResponse;
        }
        return null;
    }

    /**
     * 获取邮储商户号
     */
    public String getMerId(ContractSubTask sub) {
        //进件收单机构的任务
        final Long scheduleDepTaskId = sub.getSchedule_dep_task_id();
        //支付源重新报备
        if (Objects.equals(scheduleDepTaskId, 0L)) {
            //获取之前进件邮储的信息
            MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.getPayMchIdByChannelAndPayWay(sub.getMerchant_sn(), "psbc", String.valueOf(PaywayEnum.ACQUIRER.getValue()));
            return merchantProviderParams.getPay_merchant_id();
        }
        final ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(scheduleDepTaskId);
        //报备返回信息主键
        final String providerParamsId = BeanUtil.getPropString(JSONObject.parseObject(contractSubTask.getResponse_body(), Map.class), "merchantProviderParamsId");
        final MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPrimaryKey(providerParamsId);
        //邮储商户号
        if (Objects.isNull(merchantProviderParams)) {
            return merchantProviderParamsMapper.getPayMchIdByChannelAndPayWay(sub.getMerchant_sn(), "psbc", String.valueOf(PaywayEnum.ACQUIRER.getValue())).getPay_merchant_id();
        }
        final String merchantId = merchantProviderParams.getPay_merchant_id();
        return merchantId;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        //上次进件邮储的,费率,使用bank_direct_apply表中存的信息
        final BankDirectApply apply = bankDirectApplyMapper.getApplyBySnAndDevCode(contractTask.getMerchant_sn(), psbcBusinessDevCode);
        final Long taskId = apply.getTask_id();
        final ContractTask task = contractTaskMapper.selectByPrimaryKey(taskId);
        //邮储进件完成
        if (!Objects.equals(TaskStatus.SUCCESS.getVal(), task.getStatus())) {
            final ContractResponse response = new ContractResponse().setCode(405).setMessage("商户在邮储还未完成进件");
            return response;
        }
        PsbcParam psbcParam = buildParam(contractChannel, sub, PsbcParam.class);
        if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
            //暂时 不同步状态更新
            if (ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE.equals(sub.getTask_type())) {
                return null;
            }
            //TODO 费率变更三期做
            if (ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(sub.getTask_type())) {
                return null;
            }//其他变更
            if (ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(sub.getTask_type()) || ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(sub.getTask_type())
                    || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(sub.getTask_type()) || ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(sub.getTask_type())) {
            }
            {
                log.info("sub_task_type:{},subId:{}", sub.getTask_type(), sub.getId());
                return psbcService.updateMerchant(contextParam, psbcParam);
            }
            //不支持更新
        } else if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            return null;
            //不支持更新
        } else if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            return null;
        }
        return null;
    }

    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        final PsbcParam psbcParam = contractParamsBiz.buildContractParamsByParams(merchantProviderParams, PsbcParam.class);
        return psbcService.weChatSubDevConfig(psbcParam, weixinConfig);
    }

    @Override
    protected ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        final ContractResponse contractResponse = psbcService.queryContractStatus(contractSubTask, buildPsbcParam(contractSubTask));
        //邮储银行业务处理
        if (contractResponse.isBusinessFail()) {
            //获取收单机构进件参数
            final String providerParamsId = BeanUtil.getPropString(JSONObject.parseObject(contractSubTask.getResponse_body(), Map.class), "merchantProviderParamsId");
            //删除merchantProviderParams中的交易参数(预存的交易参数)
            log.info("邮储审核失败逻辑删除merchantSn:{},providerParamsId:{}", contractSubTask.getMerchant_sn(), providerParamsId);
            setDel(providerParamsId);
        }
        return contractResponse;
    }

    @Override
    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        final OutMerInfoStatusQueryResponse statusQueryResponse = JSONObject.parseObject(JSONObject.toJSONString(response.getResponseParam()), OutMerInfoStatusQueryResponse.class);
        final String merSta = statusQueryResponse.getData().getMerSta();
        final String merchantSn = contractSubTask.getMerchant_sn();
        HandleQueryStatusResp resp = new HandleQueryStatusResp();
        final String originResult = contractSubTask.getResult();
        final Long id = contractSubTask.getId();
        //该状态对应的描述
        final String message = PsbcStatusEnum.getMessage(merSta);
        //商户被停用或者注销由回调完成
        if (BAN_LIST.contains(merSta)) {
            return resp.setFail(true)
                    .setMessage(StringUtils.isEmpty(message) ? "邮储主动关闭商户" : message);
        }
        //04-审批中
        //获取银行直连申请任务
        final Long pTaskId = contractSubTask.getP_task_id();
        final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(pTaskId);
        final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(pTaskId);
        final Map eventContext = contractTask.getEventContext();
        Map<String, Object> license = (Map) eventContext.get("merchantBusinessLicense");
        final Integer type = BeanUtil.getPropInt(license, "type");
        if (Objects.equals("04", merSta)) {
            updateSubAndParentTaskResult(id, originResult, "审批中");
            //对私商户提示商户已签约,待银行审核
            //对公商户商户提示商户已签约,待银行审核
            psbcDirectBiz.recordViewProcess(apply, Lists.newArrayList(0, 1).contains(type) ? 30 : 21, new Date());
            return resp.setRetry(true)
                    .setMessage("审批中");
        }
        //注意主动查询接口中的状态04-审批中,但是邮储回调中的04-审批通过
        final MerchantStatusCallBack callBack = MerchantStatusCallBack.builder().merId(statusQueryResponse.getData().getMerId()).merSta(merSta).outMerId(merchantSn).build();
        //审核通过或者审核不通过处理
        final HandleQueryStatusResp responseHandle = psbcResponseHandle(contractSubTask, callBack);
        if (responseHandle.isSuccess()) {
            //银行已审核,待商户微信实名认证
            psbcDirectBiz.recordViewProcess(apply, Lists.newArrayList(0, 1).contains(type) ? 40 : 41, new Date());
        }
        return responseHandle;
    }


    /**
     * @param contractSubTask        子任务
     * @param merchantStatusCallBack 查询结果
     * @return null
     * <AUTHOR>
     * @Description: 邮政进件成功或者修改信息成功处理
     * @time 10:16
     */

    public HandleQueryStatusResp psbcResponseHandle(ContractSubTask contractSubTask, MerchantStatusCallBack merchantStatusCallBack) {
        HandleQueryStatusResp resp = new HandleQueryStatusResp();
        resp.setRetry(true).setMessage("审批中");
        final Long id = contractSubTask.getId();
        final String originResult = contractSubTask.getResult();
        //主动查询当前任务状态
        final ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(id);
        //当前状态不是处理中则不做处理
        if (!Objects.equals(subTask.getStatus(), TaskStatus.PROGRESSING.getVal())) {
            return resp;
        }
        log.info("subTaskId :{},contractSubTask:{},merchantStatusCallBack:{}", id, JSONObject.toJSONString(contractSubTask), JSONObject.toJSONString(merchantStatusCallBack));
        //获取银行直连申请任务
        final Long pTaskId = contractSubTask.getP_task_id();
        final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(pTaskId);
        //触发签约通知
        if (Objects.equals(merchantStatusCallBack.getMerId(), "wateSign")) {
            updateSubAndParentTaskResult(id, originResult, "待商户签约");
            //已分配,待商户签约
            psbcDirectBiz.recordViewProcess(apply, 20, new Date());
            final String key = combineKey(contractSubTask);
            notifyMerchant(contractSubTask, key);
            return resp;
        }
        final String merSta = merchantStatusCallBack.getMerSta();
        if (StringUtils.isEmpty(merSta)) {
            return resp;
        }
        final String message = PsbcStatusEnum.getMessage(merSta);
        //邮储主动推送公商核查不通过通知
        if (Objects.equals("92", merSta)) {
            resp.setFail(Boolean.TRUE).setMessage(StringUtils.isEmpty(merchantStatusCallBack.getBackReason()) ? "公商核查不通过" : merchantStatusCallBack.getBackReason());
            return resp;
        }
        //邮储主动推送触发签约通知
        if (Objects.equals("91", merSta)) {
            updateSubAndParentTaskResult(id, originResult, "待商户签约");
            //已分配,待商户签约
            psbcDirectBiz.recordViewProcess(apply, 20, new Date());
            final String key = combineKey(contractSubTask);
            notifyMerchant(contractSubTask, key);
            return resp;
        }
        //06-待补充（没有补充签约协议）
        if (Objects.equals("06", merSta)) {
            updateSubAndParentTaskResult(id, originResult, message);
            //已分配,待商户签约
            psbcDirectBiz.recordViewProcess(apply, 20, new Date());
            //超过24小时再发送一次通知
            final String key = combineKey(contractSubTask);
            if (!redisTemplate.hasKey(key)) {
                //获取主任务
                final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(pTaskId);
                Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
                //从上下文中获取营业执照信息
                Map<String, Object> license = (Map) contextParam.get("merchantBusinessLicense");
                //收钱吧商户类型
                int type = BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE);
                if (typeList.contains(type)) {
                    //个体/小微系统向收钱吧App推送商户签约通知
                    notifyMerchant(contractSubTask, key);
                }
            }
            return resp;
        }
        //待银行分配
        if (Objects.equals("08", merSta)) {
            updateSubAndParentTaskResult(id, originResult, message);
            //已提交,待银行分配
            psbcDirectBiz.recordViewProcess(apply, 10, new Date());
            return resp;
        }

        //获取收单机构进件参数
        final String providerParamsId = BeanUtil.getPropString(JSONObject.parseObject(subTask.getResponse_body(), Map.class), "merchantProviderParamsId");
        final MerchantProviderParams providerParams = merchantProviderParamsMapper.selectByPrimaryKey(providerParamsId);
        //邮储审核
        Boolean isSuccess = null;
        if (SUCCESS_LIST.contains(merSta)) {
            isSuccess = Boolean.TRUE;
            //审批不通过
        } else if (Objects.equals(merSta, "05")) {
            //删除merchantProviderParams中的交易参数(预存的交易参数)
            log.info("邮储审核失败删除merchantSn:{},providerParamsId:{}", subTask.getMerchant_sn(), providerParamsId);
            //逻辑删除
            setDel(providerParamsId);
            return resp.setFail(Boolean.TRUE).setMessage("人工驳回原因:" + (StringUtils.isEmpty(merchantStatusCallBack.getBackReason()) ? PsbcStatusEnum.getMessage("05") : merchantStatusCallBack.getBackReason()));
        }
        //处于审批中流程中
        if (Objects.isNull(isSuccess) || !isSuccess) {
            updateSubAndParentTaskResult(id, originResult, message);
            //商户已签约,待银行审核
            psbcDirectBiz.recordViewProcess(apply, 30, new Date());
            return resp;
        }
        //子任务类型
        final Integer taskType = contractSubTask.getTask_type();
        //入网回调成功
        if (Objects.equals(taskType, ContractSubTaskTaskType.SUB_TASK_TASK_TYPE_CONTRACT) && isSuccess) {
            updateSubAndParentTaskResult(id, originResult, "银行已审核");
            final Map callbackMsg = JSONObject.parseObject(JSONObject.toJSONString(merchantStatusCallBack), Map.class);
            Map respBody = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
            callbackMsg.put("callback_time", System.currentTimeMillis());
            ArrayList<Map> callBack = Lists.newArrayList(callbackMsg);
            respBody.put("callback_msg", callBack);
            contractSubTaskMapper.updateByPrimaryKey(new ContractSubTask().setId(contractSubTask.getId()).setResponse_body(JSON.toJSONString(resp)));
            //邮储分配商户号
            final String merId = merchantStatusCallBack.getMerId();
            final Map merchant = merchantService.getMerchantByMerchantSn(merchantStatusCallBack.getOutMerId());
            // TODO 将邮储信息添加到payway有=null的params中
            final Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(BeanUtil.getPropString(merchant, DaoConstants.ID), null);
            final Map map = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS, Maps.newConcurrentMap());
            map.put(TransactionParam.PSBCBANK_TRADE_PARAMS, CollectionUtil.hashMap(TransactionParam.PSBCBANK_PROVIDER_MCH_ID, merId));
            //调用更新
            Map newMerchantConfig = CollectionUtil.hashMap(DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID), MerchantConfig.PARAMS, map);
            tradeConfigService.updateMerchantConfig(newMerchantConfig);
            log.info("merchantSn:{},providerParams", merchantStatusCallBack.getOutMerId(), JSONObject.toJSONString(providerParams));
            if (Objects.isNull(providerParams)) {
                return resp;
            }
            //将商户id写入provider_merchant_id和pay_merchant_id字段中
            final MerchantProviderParams params = providerParams;
            params.setProvider_merchant_id(merId).setPay_merchant_id(merId);
            //添加provider_merchant_id和provider_merchant_id
            merchantProviderParamsMapper.updateByPrimaryKeySelective(params);
            //关联收单机构终端ID
            if (Objects.equals(contractSubTask.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
                String providerTerminalId = providerTerminalIdBiz.getProviderTerminalIdByMerchantSn();
                if (!StringUtils.isEmpty(providerTerminalId)) {
                    providerTerminalBiz.merchantConnectionProviderTerminal(providerParams.getMerchant_sn(), providerTerminalId, merId, ProviderEnum.PROVIDER_PSBC.getValue());
                }
            }
        }
        return resp.setSuccess(Boolean.TRUE);
    }

    /**
     * 使用一些唯一性的参数组成key
     *
     * @param contractSubTask
     * @return
     */
    public String combineKey(ContractSubTask contractSubTask) {
        return String.format("%s:%s:%s", contractSubTask.getMerchant_sn(), contractSubTask.getContract_id(), psbcTemplateCode);
    }

    public void notifyMerchant(ContractSubTask contractSubTask, String key) {
        final String merchantSn = contractSubTask.getMerchant_sn();
        //获取商户信息
        final Map merchant = merchantService.getMerchantBySn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, com.wosai.data.dao.DaoConstants.ID);
        // 1:给商户超管角色发送通知（收钱吧APP)
        try {
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            if (userInfo != null) {
                final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
                sendModel.setDevCode(bankNoticeDevCode);
                //TODO 待提供模板templateCode
                sendModel.setTemplateCode(psbcTemplateCode);
                sendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                sendModel.setTimestamp(System.currentTimeMillis());
                final String contractId = StringUtils.isEmpty(contractSubTask.getContract_id()) ? null : contractSubTask.getContract_id();
                sendModel.setData(CollectionUtil.hashMap("merId", merchantSn, "temMerId", contractId));
                log.info("merchantSn:{},消息内容:{}", merchantSn, JSONObject.toJSONString(sendModel));
                clientSideNoticeService.sendToMerchantUser(sendModel);
                redisTemplate.opsForValue().set(key, psbcTemplateCode, 1L, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error("给商户超管角色发送通知异常id:{},异常信息{}", merchantId, e);
        }
    }

    /**
     * <AUTHOR>
     * @Description: 在crm和收钱吧app中通知销售, 告知微信实名认证方法
     * @time 17:13
     */
    public void notifyCrmAndMerchant(String userId, ContractTask contractTask) {
        try {
            String merchantChannelCode = wechatQrCodeUtils.authorizationCodeUrl(Maps.newHashMap(), "psbc-1023-3");
            final String imageUrl = replaceHttp(merchantChannelCode);
            //获取商户信息
            final String merchantSn = contractTask.getMerchant_sn();
            final Map merchant = merchantService.getMerchantBySn(merchantSn);
            if (!StringUtils.isEmpty(userId)) {
                //crm
                final ClientSideNoticeSendModel crmSendModel = new ClientSideNoticeSendModel();
                //产品开发标识
                crmSendModel.setDevCode(psbcCrmDevCode);
                //短信通知模板标识
                crmSendModel.setTemplateCode(psbcCrmTemplateCode);
                //账户Id
                crmSendModel.setAccountId(userId);
                //需要传的数据
                crmSendModel.setData(CollectionUtil.hashMap(
                        "ycbank", "邮储银行",
                        "mername", WosaiMapUtils.getString(merchant, Merchant.NAME),
                        "text1", WosaiMapUtils.getString(merchant, Merchant.NAME) + ",邮储入网审核已通过,请联系商户完成微信实名认证",
                        "text2", "请商户通过微信扫描邮储服务商授权码,完成微信实名认证",
                        "qrCodeImg", imageUrl));
                crmSendModel.setClientSides(Lists.newArrayList("TERMINALCRM"));
                //请求时间戳
                crmSendModel.setTimestamp(System.currentTimeMillis());
                log.info("邮储给crm发送通知参数{},商户号:{}", JSONObject.toJSONString(crmSendModel), contractTask.getMerchant_sn());
                clientSideNoticeService.send(crmSendModel);
            }
            //给商户超管角色发送通知（收钱吧APP)
            final String merchantId = BeanUtil.getPropString(merchant, com.wosai.data.dao.DaoConstants.ID);
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            //生成二维码和文案
            if (userInfo != null) {
                final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
                sendModel.setDevCode(psbcAppDevCode);
                sendModel.setTemplateCode(psbcAppTemplateCode);
                sendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                sendModel.setTimestamp(System.currentTimeMillis());
                //需要传的数据
                sendModel.setData(CollectionUtil.hashMap("text1", WosaiMapUtils.getString(merchant, Merchant.NAME) + ",邮储活动审核通过,请用微信扫描下方邮储服务商授权码,完成微信实名认证",
                        "text2", "认证通过后,方可参加活动",
                        "qrCodeImg", imageUrl));
                log.info("邮储给收钱吧app发送通知参数{},商户号:{}", JSONObject.toJSONString(sendModel), contractTask.getMerchant_sn());
                clientSideNoticeService.sendToMerchantUser(sendModel);
            }
        } catch (Exception e) {
            log.error("邮储通知发送通知异常id:{},异常信息{}", contractTask.getId(), e);
        }
    }


    public PsbcParam buildPsbcParam(ContractSubTask contractSubTask) {
        final ContractChannel contractChannel = ruleContext.getContractRule(contractSubTask.getContract_rule()).getContractChannel();
        return buildParam(contractChannel, contractSubTask, PsbcParam.class);
    }

    /**
     * <AUTHOR>
     * @Description: 同时更新sub_task和task中的result
     * @time 17:24
     */
    public void updateSubAndParentTaskResult(Long id, String originResult, String targetResult) {
        if (Objects.equals(originResult, targetResult)) {
            return;
        }
        ContractSubTask subTask = new ContractSubTask();
        subTask.setId(id);
        subTask.setResult(targetResult);
        contractSubTaskMapper.updateByPrimaryKey(subTask);
        //为了保持contractTask同步;
        final ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(id);
        final Long pTaskId = contractSubTask.getP_task_id();
        final ContractTask task = contractTaskMapper.selectByPrimaryKey(pTaskId);
        //机器人推送 包含字段有：城市，商户号，推广人|手机号，商户进件状态
        dingTalkRemind(task, targetResult);
        if (Objects.equals(task.getResult(), targetResult)) {
            return;
        }
        //只有影响主任务的时候才会去更新主任务的中的result
        if (Objects.equals(contractSubTask.getStatus_influ_p_task(), 1)) {
            final ContractTask contractTask = new ContractTask();
            contractTask.setId(pTaskId);
            contractTask.setResult(targetResult);
            contractTask.setPriority(task.getPriority());
            contractTaskMapper.updateByPrimaryKey(contractTask);
        }
    }

    public static String replaceHttp(String url) {
        if (com.wosai.mpay.util.StringUtils.isEmpty(url)) {
            return url;
        }
        if (url.startsWith("http:")) {
            url = url.replace("http:", "https:");
        }
        try {
            return URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("replaceHttp EncodingException :{}", e);
        }
        return url;
    }

    /**
     * @param task
     * @param targetResult
     * <AUTHOR>
     * @Description: 城市，商户号，推广人|手机号，商户进件状态 异步发送钉钉提醒
     * @time 16:46
     */
    private void dingTalkRemind(ContractTask task, String targetResult) {
        try {
            final Map merchant = merchantService.getMerchantByMerchantSn(task.getMerchant_sn());
            //城市
            final String city = BeanUtil.getPropString(merchant, Merchant.CITY);
            //商户号
            final String merchantSn = task.getMerchant_sn();
            //推广人|手机号
            final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(task.getId());
            final String crmUserId = BeanUtil.getPropString(JSONObject.parseObject(bankDirectApply.getForm_body(), Map.class), BankDirectApplyConstant.CRM_UESRID);
            final Map user = userService.getUser(crmUserId);
            final String cellphone = BeanUtil.getPropString(user, User.CELLPHONE);
            final String linkman = BeanUtil.getPropString(user, User.LINKMAN);
            final String join = Joiner.on("|").skipNulls().join(Lists.newArrayList(linkman, cellphone));
            final String message = String.format("邮储进件状态更新提醒 %s, %s, %s, %s", city, merchantSn, join, targetResult);
            log.info("邮储状态更新提醒:{}", message);
        } catch (Exception exception) {
            log.error("邮储状态更新提醒异常:{}", exception);
        }
    }


    /**
     * 获取主任务对应的主键Id
     */
    public String getMerchantProviderParamsId(ContractSubTask sub) {
        //进件收单机构的任务
        final Long scheduleDepTaskId = sub.getSchedule_dep_task_id();
        //支付源重新报备
        if (Objects.equals(scheduleDepTaskId, 0L)) {
            //获取之前进件邮储的信息
            final String providerParamsId = BeanUtil.getPropString(JSONObject.parseObject(sub.getResponse_body(), Map.class), "merchantProviderParamsId");
            return providerParamsId;
        }
        final ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(scheduleDepTaskId);
        //报备返回信息主键
        final String providerParamsId = BeanUtil.getPropString(JSONObject.parseObject(contractSubTask.getResponse_body(), Map.class), "merchantProviderParamsId");
        return providerParamsId;
    }


    /**
     * 逻辑删除
     *
     * @param providerParamsId
     */
    public void setDel(String providerParamsId) {
        MerchantProviderParams params = new MerchantProviderParams();
        params.setId(providerParamsId);
        params.setDeleted(Boolean.TRUE);
        merchantProviderParamsMapper.updateByPrimaryKeySelective(params);
    }

    @Override
    public Boolean doCheck(ContractSubTask contractSubTask, ContractResponse contractResponse) {
        final OutMerInfoStatusQueryResponse statusQueryResponse = JSONObject.parseObject(JSONObject.toJSONString(contractResponse.getResponseParam()), OutMerInfoStatusQueryResponse.class);
        final String merSta = statusQueryResponse.getData().getMerSta();
        //06-待补充（没有补充签约协议）
        if (Objects.equals("06", merSta)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO addTermInfoDTO, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            PsbcParam psbcParam = contractParamsBiz.buildContractParams("psbc-1023-2", PsbcParam.class);
            response = psbcService.addAliTermInfo(addTermInfoDTO, psbcParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            PsbcParam psbcParam = contractParamsBiz.buildContractParams("psbc-1023-3", PsbcParam.class);
            response = psbcService.addWxTermInfo(addTermInfoDTO, psbcParam);
        }
        return response;
    }
}

