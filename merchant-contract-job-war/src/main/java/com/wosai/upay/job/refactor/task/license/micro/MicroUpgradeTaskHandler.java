package com.wosai.upay.job.refactor.task.license.micro;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.shouqianba.model.dto.request.MerchantTradeParamsDisableReasonManageReqDTO;
import com.shouqianba.model.enums.TradeParamsDisableReasonAccessSideEnum;
import com.shouqianba.model.enums.TradeParamsDisableReasonOperateTypeEnum;
import com.shouqianba.service.MerchantContractService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.utils.PhotoUtils;
import com.wosai.shouqianba.withdrawservice.service.WithdrawService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.avro.MicroUpgradeSuccessDTO;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.WxUseType;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.acquirer.CheckChangeAcquirerResp;
import com.wosai.upay.job.model.dto.request.BusinessLicenseUpdateDTO;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.LklV3AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.model.LklV3ContractResultDTO;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.biz.provider.McProviderBiz;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.InternalScheduleSubTaskDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerChangeDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.task.HaikeUnionPayContractResultTask;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseAuditApplyDTO;
import com.wosai.upay.job.refactor.model.dto.NewMerchantContractResultRspDTO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV2Task;
import com.wosai.upay.job.refactor.task.license.account.ChangeAccountWithLicenseUpdate;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV2MainTaskContext;
import com.wosai.upay.job.service.*;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ThreadLocalUtil;
import com.wosai.upay.lkl.service.PayNotifyService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.wallet.service.WalletService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 小微升级任务处理
 * 小微升级和更新营业执照，对银行卡状态管理有区别
 * 小微升级，进件前，不会把银行卡状态改为默认中，因为如果改为默认中，无法做强制提现
 *
 * <AUTHOR>
 * @date 2025/2/10 11:48
 */
@Slf4j
@Component
public class MicroUpgradeTaskHandler {

    public static final String MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON = "小微升级";

    public static String MICRO_UPGRADE_SUCCESS_TOPIC = "events_CUA_micro_upgrade_success";

    public static final String WAIT_ALL_RE_CONTRACT_FINISHED_MARK = "等待进件结束";

    public static final String WAIT_FEW_MINUTES_MARK = "等待几分钟";

    public static final String WAIT_CHANGE_ACQUIRER_FINISHED_MARK = "等待切收单机构任务结束";

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Resource
    private MerchantContractService merchantContractService;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    @Resource
    private AcquirerFacade acquirerFacade;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private McProviderBiz mcProviderBiz;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private McAcquirerChangeDAO mcAcquirerChangeDAO;

    @Resource
    private MerchantProviderParamsServiceImpl merchantProviderParamsService;

    @Resource
    private AcquirerServiceImpl acquirerService;

    @Autowired
    private WalletService walletServcie;

    @Autowired
    private WithdrawService withdrawService;

    @Autowired
    protected BusinessLogBiz businessLogBiz;

    @Resource
    private T9ServiceImpl t9Service;

    @Resource
    private SubBizParamsBiz subBizParamsBiz;

    @Resource(name = "aliKafkaTemplate")
    private KafkaTemplate<String, Object> aliKafkaTemplate;

    @Resource
    private ContractStatusDAO contractStatusDAO;

    @Resource
    private ChatBotUtil chatBotUtil;

    @Resource
    protected InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    @Resource
    private TradeConfigService tradeConfigService;

    @Resource
    private TradeConfigClient tradeConfigClient;

    @Resource
    private PayNotifyService payNotifyService;

    @Resource
    private ChangeAccountWithLicenseUpdate changeAccountWithLicenseUpdate;


    public InternalScheduleSubTaskProcessResultBO handleMicroUpgrade(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (isReContractSubTask(subTaskDO)) {
            return handleReContractSubTask(mainTaskDO, subTaskDO);
        }
        InternalScheduleSubTaskProcessResultBO resultBO = handleChangeParamsSubTask(mainTaskDO, subTaskDO);
        if (resultBO.processStatusIsSuccess() ) {
            updateClearanceAndCacheAfterTaskFinished(mainTaskDO.getMerchantSn());
            processMicroUpgradeSuccess(mainTaskDO);
        }
        if (resultBO.processStatusIsFail()) {
            updateClearanceAndCacheAfterTaskFinished(mainTaskDO.getMerchantSn());
        }
        return resultBO;
    }

    protected void updateClearanceAndCacheAfterTaskFinished(String merchantSn) {
        try {
            Optional<ContractStatusDO> statusDOOpt = contractStatusDAO.getByMerchantSn(merchantSn);
            if (!statusDOOpt.isPresent()) {
                return;
            }
            Optional<String> merchantIdOpt = merchantBasicInfoBiz.getMerchantIdByMerchantSn(merchantSn);
            if (!merchantIdOpt.isPresent()) {
                return;
            }
            ContractStatusDO contractStatusDO = statusDOOpt.get();
            String acquirer = contractStatusDO.getAcquirer();
            clearLklCache(merchantIdOpt.get());
            tradeConfigClient.updateClearProviderByAcquirer(merchantIdOpt.get(), acquirer);
        } catch (Exception ex) {
            log.warn("小微升级更新清算标识或者缓存失败, merchantSn:{}", merchantSn, ex);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级更新清算标识或者缓存失败, merchantSn:" + merchantSn + ", message:" + ex.getMessage());
        }
    }

    private void clearLklCache(String merchantId) {
        try {
            log.info("小微升级清除拉卡拉缓存, merchantId:{}", merchantId);
            payNotifyService.clearMchCacheByMerchantId(merchantId);
        } catch (Exception e) {
            log.warn("小微升级清除拉卡拉缓存失败, merchantId:{}", merchantId, e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级清除拉卡拉缓存失败, merchantId:" + merchantId + ", message:" + e.getMessage());
        }
    }

    private boolean isReContractSubTask(InternalScheduleSubTaskDO subTaskDO) {
        return StringUtils.equals(subTaskDO.getTaskType(), BusinessLicenceCertificationV2Task.SUB_TASK_TYPE_RE_CONTRACT);
    }

    private InternalScheduleSubTaskProcessResultBO handleReContractSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess()) {
            return reContract(mainTaskDO, subTaskDO);
        } else if (subTaskDO.isWaitExternalResult()) {
            return queryContractResult(mainTaskDO, subTaskDO);
        }
        log.error("不支持的任务状态， subTaskId: {}", subTaskDO.getId());
        return InternalScheduleSubTaskProcessResultBO.fail("系统异常");
    }

    @Resource
    private HaikeUnionPayContractResultTask haikeUnionPayContractResultTask;

    public void processMicroUpgradeSuccess(InternalScheduleMainTaskDO mainTaskDO) {
        try {
            updateDefaultAccountInfo(mainTaskDO);
            sendMicroUpgradeSuccessMsgToKafka(mainTaskDO);
            // 目前的逻辑下，小微升级成功，富友肯定不会参与重新入网，所以富友的参数一定要禁用
            disableOriginalFuYouParams(mainTaskDO.getMerchantSn());
            // 如果haike和lkl都重新入网，如果haike没有成功，需要禁用haike的以前参数，否则商户信息及营业执照类型收钱吧和收单机构不一致
            disableReContractFailedAndUpdateParamsAcquirerParams(mainTaskDO);
            deleteFuYouAndLklIntegratedCardSwipeRecord(mainTaskDO.getMerchantSn());
            insertHaikeUnionPayContractResultTaskIfNeeded(mainTaskDO);
        } catch (Exception e) {
            log.warn("商户重新入网成功，已经变更交易参数，已经更新营业执照,后续操作出现异常, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
        }
    }
    
    /**
     * 如果海科升级成功，插入海科云闪付报备结果轮询任务
     * 
     * @param mainTaskDO 主任务
     */
    private void insertHaikeUnionPayContractResultTaskIfNeeded(InternalScheduleMainTaskDO mainTaskDO) {
        try {
            BusinessLicenseCertificationV2MainTaskContext mainTaskContext =
                    JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class);
            if (Objects.isNull(mainTaskContext)) {
                return;
            }
            List<String> successReContractAndUpdateParamsAcquirers =
                    mainTaskContext.getSuccessReContractAndUpdateParamsAcquirers();
            if (CollectionUtils.isEmpty(successReContractAndUpdateParamsAcquirers)) {
                return;
            }
            String haikeAcquirer = AcquirerTypeEnum.HAI_KE.getValue();
            if (successReContractAndUpdateParamsAcquirers.contains(haikeAcquirer)) {
                haikeUnionPayContractResultTask.insertTaskWithDelay(mainTaskDO.getMerchantSn(), 5);
                
            }
        } catch (Exception e) {
            log.error("插入海科云闪付报备结果轮询任务失败, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("插入海科云闪付报备结果轮询任务失败, merchantSn:" + mainTaskDO.getMerchantSn() + ", message:" + e.getMessage());
        }
    }

    private void updateDefaultAccountInfo(InternalScheduleMainTaskDO mainTaskDO) {
        try {
            String merchantId = merchantBasicInfoBiz.getMerchantInfoBySn(mainTaskDO.getMerchantSn()).getId();
            BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner =
                    JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class);
            BusinessLicenseAuditApplyDTO auditApplyDTO = mainTaskContextBOInner.getAuditApplyDTO();
            if (mainTaskContextBOInner.getNeedUpdateBankAccount()) {
                // 小微升级在进件前，不会把银行卡的状态改为验证中，因为小微升级中间涉及强制提现
                changeAccountWithLicenseUpdate.updateAccountWhenContractSuccess(mainTaskContextBOInner,
                        merchantId, auditApplyDTO);
            }
        } catch (Exception e) {
            log.warn("商户小微升级成功，更新默认结算账户信息失败, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("商户小微升级成功，更新默认结算账户信息失败, merchantSn=" + mainTaskDO.getMerchantSn() + "异常: " + e.getMessage());
        }
    }

    private void deleteFuYouAndLklIntegratedCardSwipeRecord(String merchantSn) {
        log.info("商户小微升级成功，删除富友和拉卡拉刷卡一体化开通记录, merchantSn:{}", merchantSn);
        doDeleteIntegratedCardSwipeRecordCatchException(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
        doDeleteIntegratedCardSwipeRecordCatchException(merchantSn, AcquirerTypeEnum.FU_YOU.getValue());
        doDeleteIntegratedForeignCardCatchException(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
        doDeleteIntegratedForeignCardCatchException(merchantSn, AcquirerTypeEnum.FU_YOU.getValue());
        doDeleteIntegratedForeignCardCatchException(merchantSn, AcquirerTypeEnum.TONG_LIAN_V2.getValue());
    }

    private void doDeleteIntegratedForeignCardCatchException(String merchantSn, String acquirer) {
        try {
            t9Service.deactivateCrmAndCuaForeignCard(merchantSn, acquirer);
        } catch (Exception e) {
            log.warn("商户删除外卡开通记录失败, merchantSn:{}, acquirer:{}, message:{}", merchantSn, acquirer, e.getMessage(), e);
        }
    }

    private void doDeleteIntegratedCardSwipeRecordCatchException(String merchantSn, String acquirer) {
        try {
            if (t9Service.isT9PosOpen(merchantSn, acquirer)) {
                t9Service.deactivateCrmAndCuaIntegratedCardSwiping(merchantSn, acquirer);
            }
        } catch (Exception e) {
            log.warn("商户删除刷卡一体化开通记录失败, merchantSn:{}, acquirer:{}, message:{}", merchantSn, acquirer, e.getMessage());
        }
    }

    private void sendMicroUpgradeSuccessMsgToKafka(InternalScheduleMainTaskDO mainTaskDO) {
        MicroUpgradeSuccessDTO microUpgradeSuccessDTO = new MicroUpgradeSuccessDTO();
        microUpgradeSuccessDTO.setMerchantSn(mainTaskDO.getMerchantSn());
        microUpgradeSuccessDTO.setOriginalAcquirer(mainTaskDO.getAcquirer());
        microUpgradeSuccessDTO.setSuccessTimeMillis(System.currentTimeMillis());
        BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class);
        try {
            microUpgradeSuccessDTO.setNewAcquirer(mainTaskContextBOInner.getNewAcquirer());
            merchantTradeParamsBiz.getAcquirerMerchantId(mainTaskDO.getMerchantSn(), mainTaskContextBOInner.getNewAcquirer()).ifPresent(microUpgradeSuccessDTO::setNewAcquirerMerchantId);
            microUpgradeSuccessDTO.setOriginalAcquirer(mainTaskContextBOInner.getOldInUseAcquirer());
            microUpgradeSuccessDTO.setOriginalAcquirerMerchantId(mainTaskContextBOInner.getOldInUseAcquirerMerchantId());
            log.info("发送小微升级成功消息到kafka, msg:{}", microUpgradeSuccessDTO);
            aliKafkaTemplate.send(MICRO_UPGRADE_SUCCESS_TOPIC, microUpgradeSuccessDTO);
        } catch (Exception e) {
            log.warn("发送小微升级成功消息到kafka失败, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("发送小微升级成功消息到kafka失败, merchantSn:" + mainTaskDO.getMerchantSn() + ", message:" + e.getMessage());
        }
    }

    private void disableReContractFailedAndUpdateParamsAcquirerParams(InternalScheduleMainTaskDO mainTaskDO) {
        BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class);
        if (Objects.isNull(mainTaskContextBOInner)) {
            return;
        }
        List<String> allContractedAcquirers = merchantTradeParamsBiz.listParamsByMerchantSn(mainTaskDO.getMerchantSn()).stream()
                .map(t -> StringExtensionUtils.toSafeString(mcProviderBiz.getAcquirerByProvider(t.getProvider().toString())))
                .distinct()
                .collect(Collectors.toList());
        List<String> successReContractAndUpdateParamsAcquirers = mainTaskContextBOInner.getSuccessReContractAndUpdateParamsAcquirers();
        disableDeletedSuccessReContractAcquirerMerchantId(mainTaskDO.getMerchantSn(), successReContractAndUpdateParamsAcquirers);
        List<String> needDeleteAcquirers = Lists.newArrayList(
                Sets.difference(Sets.newHashSet(allContractedAcquirers), Sets.newHashSet(successReContractAndUpdateParamsAcquirers))
        );
        for (String needDeleteAcquirer : needDeleteAcquirers) {
            try {
                MerchantTradeParamsDisableReasonManageReqDTO req = getMerchantTradeParamsDisableReasonManageReqDTO(mainTaskDO.getMerchantSn());
                req.setDisableReasonDetail("小微升级成功后，禁用未升级成功的收单机构商户号");
                merchantTradeParamsBiz.disableMerchantAcquirerMerchantId(mainTaskDO.getMerchantSn(), needDeleteAcquirer, req);
            } catch (Exception e) {
                log.warn("商户重新入网成功，已经变更交易参数，但是禁用收单机构交易参数失败，商户号:{}, 收单机构:{}", mainTaskDO.getMerchantSn(), needDeleteAcquirer, e);
            }
        }
    }

    private void disableDeletedSuccessReContractAcquirerMerchantId(String merchantSn, List<String> successReContractAndUpdateParamsAcquirers) {
        for (String acquirer : successReContractAndUpdateParamsAcquirers) {
            try {
                Optional<MerchantProviderParamsDO> lastedDeletedAcquirerMerchantParams = merchantTradeParamsBiz.getLastedDeletedAcquirerMerchantId(merchantSn, acquirer);
                if (!lastedDeletedAcquirerMerchantParams.isPresent()) {
                    return;
                }
                MerchantTradeParamsDisableReasonManageReqDTO req = getMerchantTradeParamsDisableReasonManageReqDTO(merchantSn);
                req.setDisableReasonDetail("小微升级成功后，禁用已删除的收单机构商户号");
                merchantTradeParamsBiz.disableAcquirerMerchantId(lastedDeletedAcquirerMerchantParams.get().getProviderMerchantId(), req);
            } catch (Exception e) {
                log.warn("商户重新入网成功，已经变更交易参数，但是禁用收单机构交易参数失败，商户号:{}, 收单机构:{}", merchantSn, acquirer, e);
            }
        }
    }

    public MerchantTradeParamsDisableReasonManageReqDTO getMerchantTradeParamsDisableReasonManageReqDTO(String merchantSn) {
        MerchantTradeParamsDisableReasonManageReqDTO req = new MerchantTradeParamsDisableReasonManageReqDTO();
        req.setMerchantSn(merchantSn);
        req.setOperateType(TradeParamsDisableReasonOperateTypeEnum.ADD);
        req.setAccessSide(TradeParamsDisableReasonAccessSideEnum.CUA);
        req.setOperator("merchant-contract-job");
        req.setDisableReason(MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON);
        return req;
    }


    private void disableOriginalFuYouParams(String merchantSn) {
        try {
            MerchantTradeParamsDisableReasonManageReqDTO req = getMerchantTradeParamsDisableReasonManageReqDTO(merchantSn);
            req.setDisableReasonDetail("小微升级成功后，禁用富友原收单机构商户号");
            merchantTradeParamsBiz.disableMerchantAcquirerMerchantId(merchantSn, AcquirerTypeEnum.FU_YOU.getValue(), req);
        } catch (Exception e) {
            log.warn("商户重新入网成功，已经变更交易参数，但是禁用富友交易参数失败, merchantSn:{}", merchantSn, e);
        }
    }

    private InternalScheduleSubTaskProcessResultBO reContract(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        String acquirer = subTaskDO.getAcquirer();
        Optional<AcquirerSharedAbility> sharedAbilityByAcquirer = acquirerFacade.getSharedAbilityByAcquirer(acquirer);
        if (!sharedAbilityByAcquirer.isPresent()) {
            return InternalScheduleSubTaskProcessResultBO.fail("收单机构" + acquirer + "不支持");
        }
        Map<String, Object> contextMap = buildContractContext(mainTaskDO);
        NewMerchantContractResultRspDTO contractResultRspDTO = sharedAbilityByAcquirer.get().contractToAcquirer(mainTaskDO.getMerchantSn(), contextMap);
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        BusinessLicenceCertificationV2Task.SubTaskRequestDTOInner subTaskRequestDTOInner = new BusinessLicenceCertificationV2Task.SubTaskRequestDTOInner(contractResultRspDTO.getRequest());
        BusinessLicenceCertificationV2Task.SubTaskResponseDTOInner subTaskResponseDTOInner = new BusinessLicenceCertificationV2Task.SubTaskResponseDTOInner(contractResultRspDTO.getResponse());
        if (contractResultRspDTO.isProcessing()) {
            subTaskResponseDTOInner.setContractId(contractResultRspDTO.getContractId());
            resultBO.updateStatusWaitExternalResult("进件成功，等待回调查询进件结果");
        }  else if (contractResultRspDTO.isSuccess()) {
            try {
                subTaskResponseDTOInner.setNewUnionMerchantId(contractResultRspDTO.getUnionMerchantId());
                subTaskResponseDTOInner.setNewTermId(contractResultRspDTO.getTermId());
                subTaskResponseDTOInner.setNewAcquirerMerchantId(contractResultRspDTO.getAcquirerMerchantId());
                subTaskDO.setResponseMessage(JSON.toJSONString(subTaskResponseDTOInner));
                resultBO.updateStatusSuccess("进件成功");
            } catch (Exception e) {
                resultBO.setRequestMsg(JSON.toJSONString(subTaskRequestDTOInner));
                resultBO.setResponseMsg(JSON.toJSONString(subTaskResponseDTOInner));
                resultBO.updateStatusFail(e.getMessage());
            }
        } else {
            String failMsg = StringUtils.isBlank(contractResultRspDTO.getMessage()) ? "进件失败" : contractResultRspDTO.getMessage();
            resultBO.updateStatusFail(failMsg);
        }
        resultBO.setRequestMsg(JSON.toJSONString(subTaskRequestDTOInner));
        resultBO.setResponseMsg(JSON.toJSONString(subTaskResponseDTOInner));
        return resultBO;
    }

    @NotNull
    private Map<String, Object> buildContractContext(InternalScheduleMainTaskDO mainTaskDO) {
        // todo 费率？
        Map<String, Object> contextMap = new HashMap<>();
        BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(),
                BusinessLicenseCertificationV2MainTaskContext.class);
        contextMap.put(ParamContextBiz.KEY_MERCHANT, mainTaskContextBOInner.getMerchant());
        contextMap.put(ParamContextBiz.KEY_BUSINESS_LICENCE, mainTaskContextBOInner.getMerchantBusinessLicense());
        contextMap.put(ParamContextBiz.KEY_BANK_INFO, mainTaskContextBOInner.getBankInfo());
        contextMap.put(ParamContextBiz.KEY_BANK_ACCOUNT, mainTaskContextBOInner.getBankAccount());
        return contextMap;
    }

    /**
     * 重新入网，此时营业执照和账户信息还没有更新入库，需要根据请求中的结算信息更新
     */
    private void updateMerchantBankAccount(Map<String, Object> contextMap, BusinessLicenseUpdateDTO businessLicenseReqDTO) {
        if (contextMap.containsKey(ParamContextBiz.KEY_BANK_ACCOUNT)) {
            Map<String, Object> originalBankAccountMap = (Map<String, Object>) contextMap.get(ParamContextBiz.KEY_BANK_ACCOUNT);
            originalBankAccountMap.remove("change_extra");
            originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.LETTER_OF_AUTHORIZATION, businessLicenseReqDTO.getLetterOfAuthorization());
            if (StringUtils.equals(businessLicenseReqDTO.getLegalPersonIdNumber(),
                    MapUtils.getString(originalBankAccountMap, com.wosai.upay.core.model.MerchantBankAccount.IDENTITY))) {
                originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, PhotoUtils.baseUrl(businessLicenseReqDTO.getLegalPersonIdCardFrontPhoto()));
                originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.HOLDER_ID_BACK_PHOTO, PhotoUtils.baseUrl(businessLicenseReqDTO.getLegalPersonIdCardBackPhoto()));
                originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.ID_VALIDITY, businessLicenseReqDTO.getIdValidity());
                if (BankAccountTypeEnum.isPersonal(MapUtils.getIntValue(originalBankAccountMap, com.wosai.upay.core.model.MerchantBankAccount.TYPE))
                        && !StringUtils.equals(businessLicenseReqDTO.getLegalPersonName(), MapUtils.getString(originalBankAccountMap, com.wosai.upay.core.model.MerchantBankAccount.HOLDER))) {
                    originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.HOLDER, businessLicenseReqDTO.getLegalPersonName());

                }
            }
            contextMap.put(ParamContextBiz.KEY_BANK_ACCOUNT, originalBankAccountMap);
        }
    }

    /**
     * 目前只有lkl需要主动查询回调结果
     */
    private InternalScheduleSubTaskProcessResultBO queryContractResult(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (!StringUtils.equals(subTaskDO.getAcquirer(), AcquirerTypeEnum.LKL_V3.getValue())) {
            return InternalScheduleSubTaskProcessResultBO.fail("收单机构" + subTaskDO.getAcquirer() + "不支持");
        }
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        BusinessLicenceCertificationV2Task.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationV2Task.SubTaskResponseDTOInner.class);
        LklV3ContractResultDTO lklV3ContractResultDTO = acquirerFacade.getUniqueAbilityAcquirer(AcquirerTypeEnum.LKL_V3, LklV3AcquirerFacade.class)
                .queryContractResult(subTaskResponseDTOInner.getContractId());
        BusinessLicenceCertificationV2Task.SubTaskRequestDTOInner subTaskRequestDTOInner = JSON.parseObject(subTaskDO.getRequestMessage(), BusinessLicenceCertificationV2Task.SubTaskRequestDTOInner.class);
        subTaskRequestDTOInner.setQueryResultReqMap(lklV3ContractResultDTO.getRequestMap());
        subTaskResponseDTOInner.setQueryResultRsqMap(lklV3ContractResultDTO.getResponseMap());
        if (lklV3ContractResultDTO.isNotFinished()) {
            resultBO.updateStatusWaitExternalResult("进件尚未完成，轮询进件结果中");
        } else if (lklV3ContractResultDTO.isContractSuccess()) {
            try {
                subTaskResponseDTOInner.setNewAcquirerMerchantId(lklV3ContractResultDTO.getAcquirerMerchantId());
                subTaskResponseDTOInner.setNewUnionMerchantId(lklV3ContractResultDTO.getUnionMerchantId());
                subTaskResponseDTOInner.setNewTermId(lklV3ContractResultDTO.getTermId());
                subTaskResponseDTOInner.setNewShopId(lklV3ContractResultDTO.getShopId());
                subTaskDO.setResponseMessage(JSON.toJSONString(subTaskResponseDTOInner));
                resultBO.updateStatusSuccess("进件成功");
            } catch (Exception e) {
                resultBO.setRequestMsg(JSON.toJSONString(subTaskRequestDTOInner));
                resultBO.setResponseMsg(JSON.toJSONString(subTaskResponseDTOInner));
                resultBO.updateStatusFail(e.getMessage());
            }
        } else if (lklV3ContractResultDTO.isContractFailed()) {
            resultBO.updateStatusFail("进件失败");
        }
        resultBO.setRequestMsg(JSON.toJSONString(subTaskRequestDTOInner));
        resultBO.setResponseMsg(JSON.toJSONString(subTaskResponseDTOInner));
        return resultBO;
    }

    private boolean isReContractSubTasksAllFinished(Long mainId) {
        List<InternalScheduleSubTaskDO> reContractSubTasks = internalScheduleSubTaskDAO.listByMainIds(Lists.newArrayList(mainId)).stream()
                .filter(t -> StringUtils.equals(t.getTaskType(), BusinessLicenceCertificationV2Task.SUB_TASK_TYPE_RE_CONTRACT)).collect(Collectors.toList());
        long notSuccessReContractTaskNum = reContractSubTasks.stream()
                .filter(t -> !(t.isProcessSuccess() || t.isProcessFail()))
                .count();
        return notSuccessReContractTaskNum == 0;
    }

    private InternalScheduleSubTaskProcessResultBO handleChangeParamsSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess()) {
            if (!isReContractSubTasksAllFinished(mainTaskDO.getId())) {
                subTaskDO.setStatusMark(WAIT_ALL_RE_CONTRACT_FINISHED_MARK);
                InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
                resultBO.updateStatusWaitExternalResult("等待重新进件子任务全部执行结束");
                return resultBO;
            }
            return doChangePrams(mainTaskDO,subTaskDO);
        } else if (subTaskDO.isWaitExternalResult()) {
            if (StringUtils.equals(WAIT_ALL_RE_CONTRACT_FINISHED_MARK, subTaskDO.getStatusMark())) {
                if (!isReContractSubTasksAllFinished(mainTaskDO.getId())) {
                    subTaskDO.setStatusMark(WAIT_ALL_RE_CONTRACT_FINISHED_MARK);
                    InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
                    resultBO.updateStatusWaitExternalResult("等待重新进件子任务全部执行结束");
                    return resultBO;
                }
                return doChangePrams(mainTaskDO,subTaskDO);
            } else if (StringUtils.equals(WAIT_FEW_MINUTES_MARK, subTaskDO.getStatusMark())) {
                return doChangePrams(mainTaskDO,subTaskDO);
            } else if (StringUtils.equals(WAIT_CHANGE_ACQUIRER_FINISHED_MARK, subTaskDO.getStatusMark())) {
                return processWaitChangeAcquirer(mainTaskDO, subTaskDO);
            }
        }
        log.error("不支持的任务状态,subTaskId:{}", subTaskDO.getId());
        throw new ContractBizException("系统异常");
    }

    private InternalScheduleSubTaskProcessResultBO processWaitChangeAcquirer(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        String targetAcquirer = subTaskDO.getAcquirer();
        Optional<McAcquirerChangeDO> changeTaskOpt = mcAcquirerChangeDAO.getByPrimaryKey(Integer.valueOf(subTaskDO.getContext()));
        if (!changeTaskOpt.isPresent()) {
            return InternalScheduleSubTaskProcessResultBO.fail("切换收单机构任务不存在");
        }
        McAcquirerChangeDO mcAcquirerChangeDO = changeTaskOpt.get();
        if (Objects.equals(mcAcquirerChangeDO.getStatus(), AcquirerChangeStatus.SUCCESS)) {
            recordMainTaskContextNewAcquirer(mainTaskDO, targetAcquirer);
            return InternalScheduleSubTaskProcessResultBO.success("切换成功,目标收单机构: " + targetAcquirer);
        } else if (Objects.equals(mcAcquirerChangeDO.getStatus(), AcquirerChangeStatus.FAIL)) {
            return InternalScheduleSubTaskProcessResultBO.fail("切换失败,目标收单机构：" + targetAcquirer + "。失败原因：" + mcAcquirerChangeDO.getMemo());
        }
        return InternalScheduleSubTaskProcessResultBO.waitExternalResult("切换任务已经提交,等待切换结果");
    }

    private Timestamp getMinutesLater(Integer waitMinutes) {
        long fiveMinutesLaterMillis = System.currentTimeMillis() + waitMinutes * 60 * 1000;
        return new Timestamp(fiveMinutesLaterMillis);
    }

    private void withdraw(String merchantId) {
        long balance = walletServcie.getBalance(merchantId);
        if (balance > 0) {
            List<Map<String, Object>> withdraws = withdrawService.changeAcquirerWithdraw(merchantId);
            List<String> withdrawIds = withdraws.stream()
                    .map(withdraw -> BeanUtil.getPropString(withdraw, DaoConstants.ID))
                    .collect(Collectors.toList());
            businessLogBiz.sendWithdrawLog(merchantId, balance, "小微升级，强制结算");
            log.info("{} 发起强制提现 {}", merchantId, JSON.toJSONString(withdrawIds));
        }
    }

    /**
     * 商户是否没有关闭交易状态
     * 这里enableScheduledTime为空说明商户交易状态没有关闭
     *
     * @param mainTaskDO 主任务
     * @return 是否已经关闭交易状态 true-没有关闭
     */
    private boolean isMerchantHasNotCloseTradeStatus(InternalScheduleMainTaskDO mainTaskDO) {
        return Objects.isNull(mainTaskDO.getEnableScheduledTime());
    }

    // 测试环境 00:00-23:59 生产环境 01:00-06:59:59
    public boolean isTimeInRange(LocalTime time) {
        LocalTime start = LocalTime.of(applicationApolloConfig.getMicroUpgradeBeginHourV2(), 0, 0);
        LocalTime end = LocalTime.of(applicationApolloConfig.getMicroUpgradeEndHourV2(), 59, 59);
        return time.isAfter(start) && time.isBefore(end);
    }

    private InternalScheduleSubTaskProcessResultBO doChangePrams(InternalScheduleMainTaskDO mainTaskDO,InternalScheduleSubTaskDO subTaskDO) {
        String merchantId = merchantBasicInfoBiz.getMerchantInfoBySn(mainTaskDO.getMerchantSn()).getId();
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        List<InternalScheduleSubTaskDO> reContractSubTasks = internalScheduleSubTaskDAO.listByMainIds(Lists.newArrayList(mainTaskDO.getId())).stream()
                .filter(t -> StringUtils.equals(t.getTaskType(), BusinessLicenceCertificationV2Task.SUB_TASK_TYPE_RE_CONTRACT)).collect(Collectors.toList());
        List<InternalScheduleSubTaskDO> successReContractTasks = reContractSubTasks.stream().filter(InternalScheduleSubTaskDO::isProcessSuccess).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(successReContractTasks)) {
            resultBO.updateStatusFail("没有重新进件成功的收单机构");
            return resultBO;
        }
        if (!isTimeInRange(LocalTime.now()) && isMerchantHasNotCloseTradeStatus(mainTaskDO)) {
            return InternalScheduleSubTaskProcessResultBO.waitExternalResult("非切参数时间段");
        }
        if (isMerchantHasNotCloseTradeStatus(mainTaskDO)) {
            return sleepAndCloseTradeStatusBeforeUpdateParams(mainTaskDO, subTaskDO, merchantId);
        }
        try {
            withdraw(merchantId);
        } catch (Exception e) {
            log.error("商户强制提现失败，merchantSn:{}", merchantId, e);
            return InternalScheduleSubTaskProcessResultBO.fail("强制提现失败");
        }
        log.info("小微升级,已经关闭商户交易状态，且已经等待{}分钟,已经做了强制提现,现在开始更新交易参数, merchantSn:{}", applicationApolloConfig.getMicroUpgradeWaitMinutes(), mainTaskDO.getMerchantSn());
        Map<String, InternalScheduleSubTaskDO> orderedSuccessContractSubTaskMap = getSortedSuccessContractSubTaskMap(successReContractTasks);
        List<String> reContractAndUpdateParamsSuccessAcquirers = new ArrayList<>();
        Map<String, String> acquirerUpdateParamsFailMsgMap = Maps.newHashMap();
        for (Map.Entry<String, InternalScheduleSubTaskDO> successReContractSubTaskEntry : orderedSuccessContractSubTaskMap.entrySet()) {
            String acquirer = successReContractSubTaskEntry.getKey();
            try {
                updateParamsConfigWhenReContractSubTaskSuccess(mainTaskDO, successReContractSubTaskEntry.getValue());
                reContractAndUpdateParamsSuccessAcquirers.add(acquirer);
            } catch (Exception e) {
                acquirerUpdateParamsFailMsgMap.put(acquirer, e.getMessage());
                log.warn("小微升级,重新入网成功,变更交易参数失败,商户{},收单机构{}", mainTaskDO.getMerchantSn(),acquirer, e);
            }
        }
        setMainTaskContextReContractAndUpdateParamsSuccessAcquirers(mainTaskDO,
                reContractSubTasks.stream().map(InternalScheduleSubTaskDO::getAcquirer).collect(Collectors.toSet()),
                orderedSuccessContractSubTaskMap.keySet(),
                reContractAndUpdateParamsSuccessAcquirers);
        if (CollectionUtils.isEmpty(reContractAndUpdateParamsSuccessAcquirers)) {
            log.error("小微升级商户更新交易参数失败,没有变更交易参数成功的收单机构可供选择,merchantSn:{}, failMsgMap:{}", mainTaskDO.getMerchantSn(), acquirerUpdateParamsFailMsgMap);
            String failReason = acquirerUpdateParamsFailMsgMap.entrySet().stream().map(entry -> entry.getKey() + "：" + entry.getValue()).collect(Collectors.joining("；"));
            return InternalScheduleSubTaskProcessResultBO.fail("没有变更交易参数成功的收单机构可供选择："  + failReason);
        }
        // 如果当前收单机构可以小微升级，重新进件后，直接更新交易侧参数即可。否则需要切收单机构
        String inUseChangeFailReason = mainTaskDO.getAcquirer() + "收单机构不支持小微升级";
        if (reContractAndUpdateParamsSuccessAcquirers.contains(mainTaskDO.getAcquirer())) {
            try {
                reUpdateTradeSideParamsWhenInUseAcquirer(mainTaskDO, subTaskDO, orderedSuccessContractSubTaskMap);
                updateClearanceProviderWhenSameAcquirer(mainTaskDO.getAcquirer(), merchantId);
                recordMainTaskContextNewAcquirer(mainTaskDO, mainTaskDO.getAcquirer());
                return InternalScheduleSubTaskProcessResultBO.success("更新交易参数成功");
            } catch (Exception e) {
                log.warn("小微升级商户更新在用的收单机构交易参数失败,尝试切收单机构,merchantSn:{},inUseAcquirer:{}", mainTaskDO.getMerchantSn(), mainTaskDO.getAcquirer(),e);
                inUseChangeFailReason = e.getMessage();
            }
        }
        return changeAcquirer(mainTaskDO, subTaskDO,
                orderedSuccessContractSubTaskMap.entrySet().stream()
                        .filter(t -> reContractAndUpdateParamsSuccessAcquirers.contains(t.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (existing, replacement) -> existing, LinkedHashMap::new)), inUseChangeFailReason);
    }

    private void updateClearanceProviderWhenSameAcquirer(String acquirer, String merchantId) {
        tradeConfigClient.updateClearanceProviderAndSwitchTime(merchantId, acquirer);
    }

    private void reUpdateTradeSideParamsWhenInUseAcquirer(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO, Map<String, InternalScheduleSubTaskDO> orderedSuccessContractSubTaskMap) {
        InternalScheduleSubTaskDO contractSubTask = orderedSuccessContractSubTaskMap.get(mainTaskDO.getAcquirer());
        BusinessLicenceCertificationV2Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(contractSubTask.getContext(), BusinessLicenceCertificationV2Task.SubTaskContextBOInner.class);
        List<String> newParamsIds = subTaskContextBOInner.getNewParamsIdList();
        List<MerchantProviderParamsDO> newProviderParamsDOS = merchantProviderParamsDAO.listByIds(newParamsIds)
                .stream()
                .filter(t -> !Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .sorted(Comparator.comparingInt(MerchantProviderParamsDO::getPayway)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newProviderParamsDOS)) {
            log.warn("小微升级,当前收单机构不存在在用的交易参数, merchantSn:{}", mainTaskDO.getMerchantSn());
            throw new ContractBizException("当前收单机构不存在在用的交易参数");
        }
        // 因为是当前在用的收单机构，看到存在status=1的
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(mainTaskDO.getMerchantSn());
        for (MerchantProviderParamsDO newProviderParamsDO : newProviderParamsDOS) {
            try {
                ThreadLocalUtil.setRefreshTradeParamsToPay(true);
                // status=1肯定是当前在用的移动支付业务
                if (Objects.equals(newProviderParamsDO.getStatus(), UseStatusEnum.IN_USE.getValue())) {
                    refreshDefaultParamsToPay(newProviderParamsDO, merchantInfo, mainTaskDO.getAcquirer());
                }
                // 对于无忧收款，线上，线上收款 根据params的wx_use_type来判断，需要单独再更新merchantAppConfig
                if (Objects.nonNull(newProviderParamsDO.getWxUseType()) &&
                        Sets.newHashSet(WxUseType.ONLINE_PAYMENT.getCode(), WxUseType.SCENARIO_PAYMENT.getCode()).contains(newProviderParamsDO.getWxUseType())) {
                    updateAppConfigInSpecialConditionWithCatchException(newProviderParamsDO, merchantInfo);
                }
            } catch (Exception e) {
                if (Objects.equals(newProviderParamsDO.getPayway(), PaywayEnum.WEIXIN.getValue())
                        || Objects.equals(newProviderParamsDO.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                    log.warn("小微升级更新AT交易参数到支付侧失败,payWay:{}, merchantSn:{}, provider:{}",newProviderParamsDO.getPayway(), subTaskDO.getMerchantSn(), newProviderParamsDO.getProvider(),e);
                    chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级更新AT交易参数到支付侧失败, merchantSn:" + subTaskDO.getMerchantSn()
                            + ", payWay:" + newProviderParamsDO.getPayway() + ", provider" + newProviderParamsDO.getProvider());
                    throw new ContractBizException("更新交易参数失败", e);
                }
                log.warn("更新交易参数失败,payWay:{}, merchantSn:{}",newProviderParamsDO.getPayway(), subTaskDO.getMerchantSn(), e);
            } finally {
                ThreadLocalUtil.removeRefreshTradeParamsToPay();
            }
        }
        subTaskDO.setAcquirer(mainTaskDO.getAcquirer());
    }

    private void refreshDefaultParamsToPay(MerchantProviderParamsDO merchantProviderParamsDO, MerchantInfo merchantInfo, String acquirer) {
        try {
            merchantProviderParamsService.setDefaultMerchantProviderParams(merchantProviderParamsDO.getId(), null, "小微升级更新交易参数");
        } catch (Exception e) {
            if (Sets.newHashSet(PaywayEnum.WEIXIN.getValue(), PaywayEnum.ALIPAY.getValue()).contains(merchantProviderParamsDO.getPayway())) {
                log.warn("小微升级更新AT交易参数失败,开始直接变更交易侧交易参数,merchantSn:{}, provider:{}, payWay:{}, paramsId:{}",
                        merchantProviderParamsDO.getMerchantSn(),merchantProviderParamsDO.getProvider(), merchantProviderParamsDO.getPayway(), merchantProviderParamsDO.getId(), e);
                getLicenceCertificationAcquirerHandler(acquirer).updatePayMerchantConfigAndAppConfigParams(merchantInfo, merchantProviderParamsDO);
            } else {
                log.info("小微升级删除交易侧交易参数, merchantSn:{}, provider:{}, payWay:{}", merchantInfo.getSn(), merchantProviderParamsDO.getProvider(), merchantProviderParamsDO.getPayway());
                deletePaySideParams(merchantInfo.getId(), merchantProviderParamsDO);
            }
        }
    }

    public void updateAppConfigInSpecialConditionWithCatchException(MerchantProviderParamsDO newProviderParamsDO, MerchantInfo merchantInfo) {
        // 如果商户开通了线上收款或者跨城收款，需要单独更新交易参数
        try {
            Map appConfigParams = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantInfo.getId(), newProviderParamsDO.getPayway(), subBizParamsBiz.getOnlinePaymentTradeAppId());
            if (MapUtils.isNotEmpty(appConfigParams) && Objects.equals(MapUtils.getInteger(appConfigParams, CommonModel.PROVIDER), newProviderParamsDO.getProvider())) {
                log.info("小微升级后更新线上收款交易参数到交易侧, merchantSn:{}, paramsId:{}, appId:{}", newProviderParamsDO.getMerchantSn(),newProviderParamsDO.getId(), subBizParamsBiz.getOnlinePaymentTradeAppId());
                merchantProviderParamsService.setDefaultMerchantProviderParamsByTradeApp(newProviderParamsDO.getId(), null, "小微升级更新交易参数", subBizParamsBiz.getOnlinePaymentTradeAppId());
            }
        } catch (Exception e) {
            log.warn("小微升级后更新线上收款交易参数到交易侧失败, merchantSn:{}, paramsId:{}, appId:{}", newProviderParamsDO.getMerchantSn(),newProviderParamsDO.getId(), subBizParamsBiz.getOnlinePaymentTradeAppId(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级后更新线上收款交易参数到交易侧失败, merchantSn:" + newProviderParamsDO.getMerchantSn());
        }
        try {
            Map appConfigParamsOnline = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantInfo.getId(), newProviderParamsDO.getPayway(), subBizParamsBiz.getCrossCityPaymentTradeAppId());
            if (MapUtils.isNotEmpty(appConfigParamsOnline) && Objects.equals(MapUtils.getInteger(appConfigParamsOnline, CommonModel.PROVIDER), newProviderParamsDO.getProvider())) {
                log.info("小微升级后更新跨城收款交易参数到交易侧, merchantSn:{}, paramsId:{}, appId:{}", newProviderParamsDO.getMerchantSn(),newProviderParamsDO.getId(), subBizParamsBiz.getCrossCityPaymentTradeAppId());
                merchantProviderParamsService.setDefaultMerchantProviderParamsByTradeApp(newProviderParamsDO.getId(), null, "小微升级更新交易参数", subBizParamsBiz.getCrossCityPaymentTradeAppId());
            }
        } catch (Exception e) {
            log.warn("小微升级后更新跨城收款交易参数到交易侧失败, merchantSn:{}, paramsId:{}, appId:{}", newProviderParamsDO.getMerchantSn(),newProviderParamsDO.getId(), subBizParamsBiz.getCrossCityPaymentTradeAppId(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级后更新跨城收款交易参数到交易侧失败, merchantSn:" + newProviderParamsDO.getMerchantSn());
        }
    }

    private void deletePaySideParams(String merchantId, MerchantProviderParamsDO newProviderParamsDO) {
        merchantTradeParamsBiz.deletePaySideMerchantConfigParams(merchantId, newProviderParamsDO.getPayway(), newProviderParamsDO.getProvider());
        merchantTradeParamsBiz.deletePaySideMerchantAppConfigParams(merchantId, newProviderParamsDO.getPayway(), newProviderParamsDO.getProvider());
    }

    /**
     * 核心逻辑 更新参数配置
     */
    public void updateParamsConfigWhenReContractSubTaskSuccess(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        getLicenceCertificationAcquirerHandler(subTaskDO.getAcquirer()).updateParamsAfterContractSuccess(mainTaskDO, subTaskDO);
    }

    private AbstractUpdateTradeParamsTemplate getLicenceCertificationAcquirerHandler(String acquirer) {
        Optional<AbstractUpdateTradeParamsTemplate> handlerOpt = applicationContext.getBeansOfType(AbstractUpdateTradeParamsTemplate.class).values().stream()
                .filter(handler -> StringUtils.equals(handler.getAcquirer(), acquirer))
                .findFirst();
        if (!handlerOpt.isPresent()) {
            throw new ContractBizException("收单机构" + acquirer + "不支持");
        }
        return handlerOpt.get();
    }

    private void setMainTaskContextReContractAndUpdateParamsSuccessAcquirers(InternalScheduleMainTaskDO mainTaskDO,
                                                                             Set<String> reContractAcquires,
                                                                             Set<String> reContractSuccessAcquires,
                                                                             List<String> reContractAndUpdateParamsSuccessAcquirers) {
        String context = mainTaskDO.getContext();
        if (StringUtils.isBlank(context)) {
            return;
        }
        BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = JSON.parseObject(context, BusinessLicenseCertificationV2MainTaskContext.class);
        if (Objects.isNull(mainTaskContextBOInner)) {
            return;
        }
        mainTaskContextBOInner.setReContractAcquirers(new ArrayList<>(reContractAcquires));
        mainTaskContextBOInner.setSuccessReContractAcquirers(new ArrayList<>(reContractSuccessAcquires));
        mainTaskContextBOInner.setSuccessReContractAndUpdateParamsAcquirers(reContractAndUpdateParamsSuccessAcquirers);
        mainTaskDO.setContext(JSON.toJSONString(mainTaskContextBOInner));
    }

    private InternalScheduleSubTaskProcessResultBO changeAcquirer(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO,
                                                                  Map<String, InternalScheduleSubTaskDO> orderedSuccessContractAndUpdateParamsSubTaskMap,
                                                                  String inUseChangeFailReason) {
        orderedSuccessContractAndUpdateParamsSubTaskMap.remove(mainTaskDO.getAcquirer()); // 排除当前收单机构
        String inUseAcquirerChangeFailReason = "原收单机构：" + mainTaskDO.getAcquirer() + ": " + inUseChangeFailReason;
        if (MapUtils.isEmpty(orderedSuccessContractAndUpdateParamsSubTaskMap)) {
            log.error("小微升级商户没有可切换的收单机构,merchantSn:{}, 原收单机构切换失败原因: {}", mainTaskDO.getMerchantSn(), inUseChangeFailReason);
            return InternalScheduleSubTaskProcessResultBO.fail("原收单机构无法完成小微升级,且没有可切换的收单机构。" + inUseAcquirerChangeFailReason);
        }
        StringBuilder changeFailReason = new StringBuilder();
        for (Map.Entry<String, InternalScheduleSubTaskDO> acquirerContractSubTaskEntry : orderedSuccessContractAndUpdateParamsSubTaskMap.entrySet()) {
            String targetAcquirer = acquirerContractSubTaskEntry.getKey();
            try {
                boolean enableTrade = merchantContractService.isMerchantContractSuccessAndEnableTrade(mainTaskDO.getMerchantSn(), targetAcquirer);
                if (!enableTrade) {
                    log.warn("小微升级切收单机构，目标收单机构未完成授权验证。merchantSn:{}, targetAcquirer:{}", mainTaskDO.getMerchantSn(), targetAcquirer);
                    changeFailReason.append(targetAcquirer).append(":").append("存在微信或者支付宝未完成授权验证").append(";");
                    continue;
                }
                ThreadLocalUtil.setCheckMicroUpgrade(true);
                CheckChangeAcquirerResp checkResult = acquirerService.checkChangeAcquirer(mainTaskDO.getMerchantSn(), targetAcquirer);
                if (!checkResult.isCan_change()) {
                    log.warn("小微升级切换收单机构校验未通过,商户:{},目标收单机构:{}, 原因:{}", mainTaskDO.getMerchantSn(), targetAcquirer, checkResult.getMsg());
                    changeFailReason.append(targetAcquirer).append(":").append(checkResult.getMsg()).append(";");
                    continue;
                }
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                // 小微升级变更结算信息场景，此时银行卡信息还没有落库，同步银行卡导致卡信息不一致
                // 而且刚刚做了重新入网，不需要同步银行卡
                ThreadLocalUtil.setAcquirerChangeSkipSyncBankAccount(true);
                acquirerService.applyChangeAcquirer(mainTaskDO.getMerchantSn(), targetAcquirer, true);
                Optional<McAcquirerChangeDO> changeTaskOpt = mcAcquirerChangeDAO.getLastedByMerchantSnAndTargetAcquirer(mainTaskDO.getMerchantSn(), targetAcquirer, timestamp);
                if (!changeTaskOpt.isPresent()) {
                    throw new ContractBizException("切换收单机构任务提交成功，但是没有生成对应的切换任务");
                }
                subTaskDO.setContext(changeTaskOpt.get().getId().toString());
                subTaskDO.setStatusMark(WAIT_CHANGE_ACQUIRER_FINISHED_MARK);
                subTaskDO.setAcquirer(targetAcquirer);
                return InternalScheduleSubTaskProcessResultBO.waitExternalResult("切换收单机构任务已经提交");
            } catch (Exception e) {
                log.warn("小微升级商户切换收单机构失败, 商户:{}, 收单机构:{}", mainTaskDO.getMerchantSn(), targetAcquirer, e);
                changeFailReason.append(targetAcquirer).append(":").append(e.getMessage()).append(";");
            } finally {
                doWithCatchException(t -> ThreadLocalUtil.removeCheckMicroUpgrade());
                doWithCatchException(t -> ThreadLocalUtil.removeAcquirerChangeSkipSyncBankAccount());
            }
        }
        log.error("小微升级未选择到可切换的收单机构, 商户:{}, reason:{}", mainTaskDO.getMerchantSn(), changeFailReason);
        return InternalScheduleSubTaskProcessResultBO.fail("原收单机构无法完成小微升级,且未选择到可切换的收单机构 " +  inUseAcquirerChangeFailReason + ", " + changeFailReason);
    }

    private void doWithCatchException(Consumer<Object> consumer) {
        try {
            consumer.accept(null);
        } catch (Exception e) {
            log.error("doWithCatchException error", e);
        }
    }

    private Map<String, InternalScheduleSubTaskDO> getSortedSuccessContractSubTaskMap(List<InternalScheduleSubTaskDO> successReContractTasks) {
        return successReContractTasks.stream()
                .collect(Collectors.toMap(InternalScheduleSubTaskDO::getAcquirer, Function.identity(), (k1, k2) -> k1, LinkedHashMap::new))
                .entrySet().stream()
                .sorted(Comparator.comparingInt(entry -> {
                    String acquirer = entry.getKey();
                    if (AcquirerTypeEnum.LKL_V3.getValue().equals(acquirer)) {
                        return 1;
                    } else if (AcquirerTypeEnum.HAI_KE.getValue().equals(acquirer)) {
                        return 2;
                    } else {
                        return 3;
                    }
                }))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (existing, replacement) -> existing,LinkedHashMap::new));
    }

    private InternalScheduleSubTaskProcessResultBO sleepAndCloseTradeStatusBeforeUpdateParams(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO, String merchantId) {
        log.info("小微升级变更交易参数前关闭商户交易状态，{}分钟后开始更新交易参数, merchantSn:{}", applicationApolloConfig.getMicroUpgradeWaitMinutes(), mainTaskDO.getMerchantSn());
        tradeConfigClient.updateClearanceProviderToSwitch(merchantId);
        mainTaskDO.setEnableScheduledTime(getMinutesLater(applicationApolloConfig.getMicroUpgradeWaitMinutes()));
        subTaskDO.setStatusMark(WAIT_FEW_MINUTES_MARK);
        return InternalScheduleSubTaskProcessResultBO.waitExternalResult("已关闭商户交易状态,等待" + applicationApolloConfig.getMicroUpgradeWaitMinutes() + "分钟后更新交易参数");
    }

    private void recordMainTaskContextNewAcquirer(InternalScheduleMainTaskDO mainTaskDO, String acquirer) {
        String context = mainTaskDO.getContext();
        if (StringUtils.isBlank(context)) {
            return;
        }
        BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class);
        if (Objects.isNull(mainTaskContextBOInner)) {
            return;
        }
        mainTaskContextBOInner.setNewAcquirer(acquirer);
        mainTaskDO.setContext(JSON.toJSONString(mainTaskContextBOInner));
    }

}
