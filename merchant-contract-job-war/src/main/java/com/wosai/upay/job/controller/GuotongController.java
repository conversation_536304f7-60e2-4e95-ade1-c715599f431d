package com.wosai.upay.job.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.guotong.GuotongAuditCallbackModel;
import com.wosai.upay.job.model.guotong.GuotongReportCallbackModel;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.guotong.GuotongSignUtil;
import com.wosai.upay.merchant.contract.model.provider.GuotongParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/2/5
 */
@RestController
@RequestMapping("/guotong")
@Slf4j
public class GuotongController {

    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private SubTaskHandlerContext subTaskHandlerContext;
    @Autowired
    private MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private QueryContractStatusHandler queryContractStatusHandler;

    private static final String AGET_ID = "agetId";
    private static final String P_DATA = "pdata";
    private static final String RSP_MSG = "rspMsg";
    private static final String SUCCESS = "success";


    @PostMapping(path = "/contractCallback", produces = {"application/json;charset=utf-8"})
    @ResponseBody
    public Map contractCallBack(@RequestBody Map<String, Object> params) throws Exception {
        String data = JSON.toJSONString(params);
        GuotongParam guotongParam = contractParamsBiz.buildContractParams(ChannelEnum.GUOTONG.getValue(), GuotongParam.class);
        boolean checkSign = GuotongSignUtil.checkSign(data, guotongParam.getPublicKey());
        if (!checkSign) {
            log.warn("国通回调错误 验签失败");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        return contractCallBackNoCheck(params);
    }

    @PostMapping(path = "/contractCallbackNoCheck", produces = {"application/json;charset=utf-8"})
    @ResponseBody
    public Map contractCallBackNoCheck(@RequestBody Map<String, Object> params) throws Exception {
        GuotongReportCallbackModel reportCallbackModel = JSON.parseObject(JSON.toJSONString(params), GuotongReportCallbackModel.class);
        Optional<MerchantAcquirerInfoDO> merchantAcquirerInfoDO = merchantAcquirerInfoDAO.getByAcquirerMerchantId(reportCallbackModel.getCustId());
        if (!merchantAcquirerInfoDO.isPresent()) {
            log.warn("国通回调错误 未找到对应商户");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        ContractTask contractTask = contractTaskMapper.getBySnAndTypeByCreateDesc(merchantAcquirerInfoDO.get().getMerchantSn(), ProviderUtil.CONTRACT_TYPE_INSERT);
        if (Objects.isNull(contractTask)) {
            log.warn("国通回调错误 未找到对应任务");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        PaywayEnum paywayEnum = reportCallbackModel.transferPayway();
        if (Objects.isNull(paywayEnum)) {
            log.warn("国通回调错误 支付方式错误");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        if (TaskStatus.isFinish(contractTask.getStatus()) && !PaywayEnum.UNIONPAY.equals(paywayEnum)) {
            log.warn("国通回调错误 任务已完成");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        Optional<ContractSubTask> subTask = contractSubTaskMapper.selectByPTaskId(contractTask.getId()).stream().filter(r -> paywayEnum.getValue().equals(r.getPayway())).findFirst();
        if (!subTask.isPresent()) {
            log.warn("国通回调错误 未找到对应子任务");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        if (SubTaskStatus.isFinish(subTask.get().getStatus())) {
            log.warn("国通回调错误 子任务已完成");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        subTaskHandlerContext.handle(contractTask, subTask.get());
        return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
    }

    /**
     * 国通审核回调 解密+验签
     *
     * @param params 回调信息
     * @return 回调结果
     * @throws Exception 解密
     */
    @PostMapping(path = "/auditCallback", produces = {"application/json;charset=utf-8"})
    @ResponseBody
    public Map auditCallBack(@RequestBody Map<String, Object> params) {
        String agetId = WosaiMapUtils.getString(params, AGET_ID);
        String pData = WosaiMapUtils.getString(params, P_DATA);
        GuotongParam guotongParam = contractParamsBiz.buildContractParams(ChannelEnum.GUOTONG.getValue(), GuotongParam.class);
        if (!Objects.equals(agetId, guotongParam.getAgetId())) {
            log.warn("国通回调错误 机构号错误");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        String data;
        try {
            data = GuotongSignUtil.publicKeyDecrypt(guotongParam.getPublicKey(), pData);
            boolean checkSign = GuotongSignUtil.checkSign(data, guotongParam.getPublicKey());
            if (!checkSign) {
                log.warn("国通回调错误 验签失败");
                return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
            }
        } catch (Exception e) {
            log.warn("国通回调错误 解密失败 {}", e.getMessage());
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        Map<String, Object> guotongAuditCallbackModel = JSON.parseObject(data, new TypeReference<Map<String, Object>>() {
        });
        return auditCallBackNoCheck(guotongAuditCallbackModel);
    }

    /**
     * 给测试提供一个不需要解密和验签的接口
     *
     * @param params 回调信息
     * @return 回调结果
     */
    @PostMapping(path = "/auditCallbackNoCheck", produces = {"application/json;charset=utf-8"})
    @ResponseBody
    public Map auditCallBackNoCheck(@RequestBody Map<String, Object> params) {
        GuotongAuditCallbackModel guotongAuditCallbackModel = JSON.parseObject(JSON.toJSONString(params), GuotongAuditCallbackModel.class);
        if (Objects.isNull(guotongAuditCallbackModel)) {
            log.warn("国通回调错误 返回信息为空");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        if (WosaiStringUtils.isEmpty(guotongAuditCallbackModel.getCustId()) || Objects.isNull(guotongAuditCallbackModel.getAuthDate())) {
            log.warn("国通回调错误 商户号或审核时间为空");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        // 解密,找到对应的 contract_sub_task，找到任务，然后调用那个方法去处理就行
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByContractId(guotongAuditCallbackModel.getCustId());
        if (Objects.isNull(contractSubTask)) {
            log.warn("国通回调错误 未找到对应子任务");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        if (SubTaskStatus.isFinish(contractSubTask.getStatus())) {
            log.warn("国通回调错误 任务已结束");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        if (contractSubTask.getCreate_at().after(guotongAuditCallbackModel.getAuthDate())) {
            log.warn("国通回调错误 审核时间在任务创建时间之前");
            return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
        }
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
        queryContractStatusHandler.doHandle(contractTask, contractSubTask);
        return CollectionUtil.hashMap(RSP_MSG, SUCCESS);
    }
}
