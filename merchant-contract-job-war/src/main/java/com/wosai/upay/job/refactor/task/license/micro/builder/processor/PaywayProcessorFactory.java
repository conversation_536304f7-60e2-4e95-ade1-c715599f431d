package com.wosai.upay.job.refactor.task.license.micro.builder.processor;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 支付方式处理器工厂
 * 负责根据支付方式和收单机构类型获取对应的处理器
 * 
 * <AUTHOR>
 */
@Slf4j
public class PaywayProcessorFactory {
    
    /**
     * 处理器缓存
     * key: acquirerType + "_" + payway
     */
    private static final Map<String, PaywayProcessor> PROCESSOR_CACHE = Maps.newConcurrentMap();
    
    /**
     * 注册处理器
     */
    public static void registerProcessor(PaywayProcessor processor) {
        String key = buildKey(processor.getSupportedAcquirerType(), processor.getSupportedPayway());
        PROCESSOR_CACHE.put(key, processor);
        log.info("Registered PaywayProcessor: {} for acquirer: {}, payway: {}", 
                processor.getClass().getSimpleName(), 
                processor.getSupportedAcquirerType(), 
                processor.getSupportedPayway());
    }
    
    /**
     * 获取处理器
     */
    public static PaywayProcessor getProcessor(Integer payway, String acquirerType) {
        String key = buildKey(acquirerType, payway);
        PaywayProcessor processor = PROCESSOR_CACHE.get(key);
        
        if (processor == null) {
            log.warn("No PaywayProcessor found for acquirer: {}, payway: {}", acquirerType, payway);
        }
        
        return processor;
    }
    
    /**
     * 构建缓存key
     */
    private static String buildKey(String acquirerType, Integer payway) {
        return acquirerType + "_" + payway;
    }
    
    /**
     * 清空缓存（主要用于测试）
     */
    public static void clearCache() {
        PROCESSOR_CACHE.clear();
    }
    
    /**
     * 获取所有已注册的处理器
     */
    public static Map<String, PaywayProcessor> getAllProcessors() {
        return Maps.newHashMap(PROCESSOR_CACHE);
    }
}
