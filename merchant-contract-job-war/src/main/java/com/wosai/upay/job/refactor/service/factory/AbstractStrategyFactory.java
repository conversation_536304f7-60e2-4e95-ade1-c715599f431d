package com.wosai.upay.job.refactor.service.factory;

import com.google.common.collect.Maps;
import com.shouqianba.cua.annotation.ITextValueEnum;
import com.wosai.upay.job.refactor.service.strategy.Strategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.Optional;
/**
 * 抽象的策略工厂
 *
 * <AUTHOR>
 * @date 2023/12/18 17:49
 */
@Slf4j
public abstract class AbstractStrategyFactory<T extends Strategy<? extends ITextValueEnum<V>, V>, V> implements ApplicationRunner {

    private final Map<V, T> strategyMap = Maps.newHashMap();

    private Class<T> strategyClass;

    @Resource
    protected ApplicationContext applicationContext;

    @SuppressWarnings("unchecked")
    protected AbstractStrategyFactory() {
        Type genericSuperclass = getClass().getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            Type[] types = ((ParameterizedType) genericSuperclass).getActualTypeArguments();
            if (types.length > 0 && types[0] instanceof Class) {
                this.strategyClass = (Class<T>) types[0];
            }
        }
    }

    @Override
    public void run(ApplicationArguments args) {
        applicationContext.getBeansOfType(strategyClass)
                .values().forEach(strategy -> strategyMap.put(strategy.getTypeValue(), strategy));
    }

    /**
     * 获取策略
     *
     * @param key 策略key
     * @return 策略
     */
    public Optional<T> getStrategy(V key) {
        if (!strategyMap.containsKey(key)) {
            log.warn("获取策略失败,Key: {}", key);
            return Optional.empty();
        }
        return Optional.of(strategyMap.get(key));
    }

    /**
     * 获取策略
     *
     * @param key           策略key
     * @param strategyClass 策略类型
     * @param <U>           策略类型
     * @return 策略
     */
    public <U extends T> Optional<U> getStrategy(V key, Class<U> strategyClass) {
        T strategy = strategyMap.get(key);
        if (strategyClass.isInstance(strategy)) {
            return Optional.of(strategyClass.cast(strategy));
        }
        log.warn("The strategy for key: {} is not an instance of the class: {}", key, strategyClass.getName());
        return Optional.empty();
    }
}
