package com.wosai.upay.job.refactor.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 内部调度结果返回dto
 *
 * <AUTHOR>
 * @date 2024/6/17 09:48
 */
@AllArgsConstructor
@Data
public class InternalScheduleTaskResultRspDTO {

    public InternalScheduleTaskResultRspDTO(String message) {
        this.message = message;
    }

    public InternalScheduleTaskResultRspDTO(Integer processSuccessNum, Integer processFailNum, String message) {
        this.processSuccessNum = processSuccessNum;
        this.processFailNum = processFailNum;
        this.message = message;
    }

    private Integer scheduleSuccessMark = 1;

    private Integer processSuccessNum;

    private Integer processFailNum;

    private String message;

}
