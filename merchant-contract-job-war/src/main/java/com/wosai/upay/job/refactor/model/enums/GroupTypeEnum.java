package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 报备规则组类型枚举
 *
 * <AUTHOR>
 */
public enum GroupTypeEnum implements ITextValueEnum<Integer> {

    PRIMARY(1, "主规则组"),

    STANDBY(2, "备用规则组");

    private final Integer value;
    private final String text;

    GroupTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
