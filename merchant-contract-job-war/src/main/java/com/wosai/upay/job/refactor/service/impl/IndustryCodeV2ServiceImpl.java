package com.wosai.upay.job.refactor.service.impl;

import com.wosai.upay.job.refactor.dao.IndustryCodeV2DAO;
import com.wosai.upay.job.refactor.service.IndustryCodeV2Service;
import com.wosai.upay.job.refactor.model.dto.IndustryCodeV2DTO;
import com.wosai.upay.job.refactor.model.entity.IndustryCodeV2DO;
import com.wosai.upay.job.refactor.utils.BeanCopyUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;


/**
 * industry_code_v2表Service层 {@link IndustryCodeV2DO}
 *
 * <AUTHOR>
 */
@Service
public class IndustryCodeV2ServiceImpl implements IndustryCodeV2Service {

    @Resource
    private IndustryCodeV2DAO industryCodeV2DAO;


    /**
     * 根据行业id查询
     *
     * @param industryId {@link IndustryCodeV2DTO#getIndustryId()} ()}
     * @return Optional<IndustryCodeV2DTO> {@link IndustryCodeV2DTO}
     */
    @Override
    public Optional<IndustryCodeV2DTO> getIndustryCodeV2ByIndustryId(String industryId) {
        return industryCodeV2DAO.getIndustryCodeV2ByIndustryId(industryId)
                .map(industryCodeV2DO -> BeanCopyUtils.copyProperties(industryCodeV2DO, IndustryCodeV2DTO.class));
    }

    /**
     * 获取所有的二级映射行业(数据量较少,直接获取所有)
     *
     * @return 所有的二级映射行业
     */
    @Override
    public List<IndustryCodeV2DTO> listAllIndustryCodeV2s() {
        return BeanCopyUtils.copyList(industryCodeV2DAO.listAllIndustryCodeV2s(), IndustryCodeV2DTO.class);
    }

}
