package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 直连申请状态表表实体对象
 *
 * <AUTHOR>
 */
@TableName("direct_status")
@Data
public class DirectStatusDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 直连应用类型
     */
    @TableField(value = "dev_code")
    private String devCode;
    /**
     * 0待处理 1.处理中 2处理成功  3处理失败
     */
    @TableField(value = "status")
    private Integer status;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;


}

