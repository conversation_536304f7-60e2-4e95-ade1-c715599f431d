package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 支持类型枚举
 *
 * <AUTHOR>
 */
public enum SupportTypeEnum implements ITextValueEnum<Integer> {

    NO(0, "不支持"),

    YES(1, "支持");


    private final Integer value;
    private final String text;

    SupportTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
