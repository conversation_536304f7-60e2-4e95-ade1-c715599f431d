package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 报备总状态表表实体对象
 *
 * <AUTHOR>
 */
@TableName("contract_status")
@Data
public class ContractStatusDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户sn
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 0待处理 1.处理中 2处理成功  3处理失败
     */
    @TableField(value = "status")
    private Integer status;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;
    /**
     * 版本号 每次数据有更新+1
     */
    @TableField(value = "version")
    private Long version;
    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;


}

