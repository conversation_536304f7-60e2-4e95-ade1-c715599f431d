package com.wosai.upay.job.refactor.task;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.contract.ScheduleStatusEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.*;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

import org.apache.commons.collections4.CollectionUtils;

/**
 * 海科云闪付报备结果任务
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Component
@Slf4j
public class HaikeUnionPayContractResultTask extends AbstractInternalScheduleTaskHandleTemplate {

    @Resource
    private InterScheduleTaskServiceImpl interactionTaskService;

    @Autowired
    private HaikeService haikeService;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private ContractStatusService contractStatusService;

    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    @Resource
    protected TradeConfigService tradeConfigService;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;


    @Override
    public InternalScheduleTaskTypeEnum getTaskType() {
        return InternalScheduleTaskTypeEnum.HAIKE_UNION_PAY_CONTRACT_RESULT;
    }

    @Override
    protected ScheduleTaskExecutePropertyBO getTaskExecuteProperty() {
        ScheduleTaskExecutePropertyBO executeProperty = new ScheduleTaskExecutePropertyBO();
        executeProperty.setBatchGetPatternType(BatchGetScheduleTaskPatternTypeEnum.PENDING_AND_WAITING_EXT_TASKS);
        executeProperty.setSupportParallel(true);
        return executeProperty;
    }

    @Override
    protected InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess() || subTaskDO.isWaitExternalResult()) {
            return queryContractResult(mainTaskDO, subTaskDO);
        }
        return InternalScheduleSubTaskProcessResultBO.fail("未知状态");
    }

    /**
     * 查询海科云闪付报备结果
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     * @return 处理结果
     */
    private InternalScheduleSubTaskProcessResultBO queryContractResult(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        try {
            String merchantSn = subTaskDO.getMerchantSn();
            ContractQueryContext context = validateAndPrepareContext(merchantSn);
            ContractResponse contractResponse = queryHaikeContractResult(context.getAcquirerMerchantId());
            return processContractResponse(merchantSn, contractResponse, context);
        } catch (Exception e) {
            log.error("处理海科云闪付报备结果任务失败, 商户号: {}, 子任务ID: {}", subTaskDO.getMerchantSn(), subTaskDO.getId(), e);
            return InternalScheduleSubTaskProcessResultBO.failWithTrace("处理失败: " + e.getMessage(), null, null);
        }
    }
    
    /**
     * 验证数据并准备上下文
     */
    private ContractQueryContext validateAndPrepareContext(String merchantSn) {
        Optional<MerchantProviderParamsDO> acquirerParamsOpt = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(
                merchantSn, ProviderEnum.PROVIDER_HAIKE.getValue(), PaywayEnum.ACQUIRER.getValue());
        if (!acquirerParamsOpt.isPresent()) {
            throw new ContractBizException("收单机构商户号不存在");
        }

        Optional<MerchantProviderParamsDO> unionPayParamsOpt = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(
                merchantSn, ProviderEnum.PROVIDER_HAIKE.getValue(), PaywayEnum.UNIONPAY.getValue());
        if (!unionPayParamsOpt.isPresent()) {
            throw new ContractBizException("merchant_provider_params缺少海科云闪付报备参数记录");
        }

        ContractStatus contractStatus = contractStatusService.selectByMerchantSn(merchantSn);
        boolean inUseHaike = Objects.equals(contractStatus.getAcquirer(), AcquirerTypeEnum.HAI_KE.getValue());
        
        return new ContractQueryContext(
                acquirerParamsOpt.get(),
                unionPayParamsOpt.get(),
                inUseHaike
        );
    }
    
    /**
     * 调用海科服务查询报备结果
     */
    private ContractResponse queryHaikeContractResult(String acquirerMerchantId) {
        HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
        return haikeService.queryMerchantContractResult(acquirerMerchantId, haikeParam);
    }
    

    private InternalScheduleSubTaskProcessResultBO processContractResponse(String merchantSn, ContractResponse contractResponse, ContractQueryContext context) {
        HashMap<String, Object> reqMap = new HashMap<>();
        reqMap.put("acquirerMerchantId", context.getAcquirerMerchantId());
        reqMap.put("haikeParam", contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class));
        
        String requestMsg = JSON.toJSONString(reqMap);
        String responseMsg = JSON.toJSONString(contractResponse);

        if (contractResponse.isSuccess()) {
            InternalScheduleSubTaskProcessResultBO result = updateWhenContractSuccess(
                    merchantSn, contractResponse, context.isInUseHaike(), 
                    context.getAcquirerParams(), context.getUnionPayParams());
            // 确保记录报文
            result.setRequestMsg(requestMsg);
            result.setResponseMsg(responseMsg);
            return result;
        }
        
        if (isWaitingForResult(contractResponse)) {
            return InternalScheduleSubTaskProcessResultBO.waitExternalResultWithTrace("等待云闪付报备结果", requestMsg, responseMsg);
        }
        
        if (isContractFailed(contractResponse)) {
            handleContractFailure(merchantSn, context.getUnionPayParams(), context.isInUseHaike());
            return InternalScheduleSubTaskProcessResultBO.successWithTrace("云闪付报备失败，message: " + contractResponse.getMessage(), requestMsg, responseMsg);
        }
        
        return InternalScheduleSubTaskProcessResultBO.failWithTrace("获取云闪付开通状态异常: " + contractResponse.getMessage(), requestMsg, responseMsg);
    }
    
    /**
     * 判断是否等待结果
     */
    private boolean isWaitingForResult(ContractResponse contractResponse) {
        return Objects.equals(contractResponse.getCode(), Constant.RESULT_CODE_SYSTEM_EXCEPTION) &&
                StringUtils.containsIgnoreCase(contractResponse.getMessage(), "重试查询");
    }
    
    /**
     * 判断是否报备失败
     */
    private boolean isContractFailed(ContractResponse contractResponse) {
        return Objects.equals(contractResponse.getCode(), Constant.RESULT_CODE_BIZ_EXCEPTION);
    }
    

    private void handleContractFailure(String merchantSn, MerchantProviderParamsDO unionPayParams, boolean inUseHaike) {
        merchantProviderParamsDAO.logicDeleteByIds(Lists.newArrayList(unionPayParams.getId()));
        if (inUseHaike) {
            try {
                String merchantId = merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn).getId();
                Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.UNIONPAY.getValue());
                if (MapUtils.isNotEmpty(merchantConfig)) {
                    Map<String, Object> updateMap = new HashMap<>();
                    updateMap.put(DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID));
                    updateMap.put(MerchantConfig.PARAMS, "");
                    tradeConfigService.updateMerchantConfig(updateMap);
                }
            } catch (Exception e) {
                log.warn("清空交易配置失败, 商户号: {}", merchantSn, e);
            }
        }
    }
    

    
    /**
     * 上下文
     */
    private static class ContractQueryContext {
        private final MerchantProviderParamsDO acquirerParams;
        private final MerchantProviderParamsDO unionPayParams;
        private final boolean inUseHaike;
        
        public ContractQueryContext(MerchantProviderParamsDO acquirerParams, MerchantProviderParamsDO unionPayParams, boolean inUseHaike) {
            this.acquirerParams = acquirerParams;
            this.unionPayParams = unionPayParams;
            this.inUseHaike = inUseHaike;
        }
        
        public MerchantProviderParamsDO getAcquirerParams() {
            return acquirerParams;
        }
        
        public MerchantProviderParamsDO getUnionPayParams() {
            return unionPayParams;
        }
        
        public boolean isInUseHaike() {
            return inUseHaike;
        }
        
        public String getAcquirerMerchantId() {
            return acquirerParams.getPayMerchantId();
        }
    }

    private InternalScheduleSubTaskProcessResultBO updateWhenContractSuccess(String merchantSn, ContractResponse contractResponse, boolean inUseHaike,
                                                                             MerchantProviderParamsDO acquirerParams, MerchantProviderParamsDO unionParams) {
        try {
            String payMerchantId = BeanUtil.getPropString(contractResponse.getResponseParam(), "bank_biz_info.bank_merch_no");
            if (StringUtils.equals(unionParams.getPayMerchantId(), payMerchantId)) {
                return InternalScheduleSubTaskProcessResultBO.success("银联商户号一致，无需更新");
            }
            
            Map<String, Object> unionExtraMap = StringUtils.isBlank(unionParams.getExtra()) ? Maps.newHashMap() : CommonUtil.string2Map(unionParams.getExtra());
            Map unionTradeParamsMap = MapUtils.getMap(unionExtraMap, "tradeParams");
            if (unionTradeParamsMap == null) {
                unionTradeParamsMap = Maps.newHashMap();
            }
            unionTradeParamsMap.put("bankMerchNo", payMerchantId);
            unionExtraMap.put("tradeParams", unionTradeParamsMap);
            
            MerchantProviderParamsDO unionUpdateParamsDO = new MerchantProviderParamsDO();
            unionUpdateParamsDO.setId(unionParams.getId());
            unionUpdateParamsDO.setPayMerchantId(payMerchantId);
            unionUpdateParamsDO.setExtra(CommonUtil.map2String(unionExtraMap));
            merchantProviderParamsDAO.updateByPrimaryKey(unionUpdateParamsDO);

            Map<String, Object> acquirerExtraMap = StringUtils.isBlank(acquirerParams.getExtra()) ? Maps.newHashMap() : CommonUtil.string2Map(acquirerParams.getExtra());
            Map acquirerTradeParamsMap = MapUtils.getMap(acquirerExtraMap, "tradeParams");
            if (acquirerTradeParamsMap == null) {
                acquirerTradeParamsMap = Maps.newHashMap();
            }
            acquirerTradeParamsMap.put("bankMerchNo", payMerchantId);
            acquirerExtraMap.put("tradeParams", acquirerTradeParamsMap);
            
            MerchantProviderParamsDO acquirerUpdateParamsDO = new MerchantProviderParamsDO();
            acquirerUpdateParamsDO.setId(acquirerParams.getId());
            acquirerUpdateParamsDO.setExtra(CommonUtil.map2String(acquirerExtraMap));
            merchantProviderParamsDAO.updateByPrimaryKey(acquirerUpdateParamsDO);
            
            if (inUseHaike && Objects.equals(unionParams.getStatus(), UseStatusEnum.IN_USE.getValue())) {
                merchantProviderParamsService.setDefaultMerchantProviderParams(unionParams.getId(), null, "云闪付报备成功， 更新云闪付参数");
            }
            return InternalScheduleSubTaskProcessResultBO.success("云闪付报备成功，更新云闪付参数");
        } catch (Exception e) {
            log.error("更新海科云闪付报备结果失败, 商户号: {}, 错误信息: {}", merchantSn, e.getMessage(), e);
            return InternalScheduleSubTaskProcessResultBO.fail("更新失败: " + e.getMessage());
        }
    }


    /**
     * 新增海科云闪付报备结果任务
     *
     * @param merchantSn 商户号
     * @param enableScheduledTime 可调度时间
     */
    public void insertTask(String merchantSn, Timestamp enableScheduledTime) {
        try {
            Map<InternalScheduleMainTaskDO, List<InternalScheduleSubTaskDO>> tasksMap = Maps.newHashMap();
            InternalScheduleMainTaskDO mainTask = buildMainTask(merchantSn);
            
            // 设置可调度时间
            if (Objects.nonNull(enableScheduledTime)) {
                mainTask.setEnableScheduledTime(enableScheduledTime);
            }
            
            InternalScheduleSubTaskDO subTask = buildSubTask(merchantSn);
            tasksMap.put(mainTask, Lists.newArrayList(subTask));
            
            interactionTaskService.batchInsertTasks(tasksMap);
            log.info("成功创建海科云闪付报备结果任务, 商户号: {}, 可调度时间: {}", merchantSn, enableScheduledTime);
        } catch (Exception e) {
            log.error("创建海科云闪付报备结果任务失败, 商户号: {}", merchantSn, e);
            throw new ContractBizException("创建任务失败", e);
        }
    }

    /**
     * 新增海科云闪付报备结果任务（立即调度）
     *
     * @param merchantSn 商户号
     */
    public void insertTask(String merchantSn) {
        insertTask(merchantSn, null);
    }

    /**
     * 新增海科云闪付报备结果任务（延迟调度）
     *
     * @param merchantSn 商户号
     * @param delayMinutes 延迟分钟数
     */
    public void insertTaskWithDelay(String merchantSn, int delayMinutes) {
        if (delayMinutes <= 0) {
            throw new ContractBizException("延迟时间必须大于0");
        }
        
        Timestamp enableScheduledTime = new Timestamp(System.currentTimeMillis() + delayMinutes * 60 * 1000L);
        insertTask(merchantSn, enableScheduledTime);
    }

    /**
     * 批量新增海科云闪付报备结果任务
     *
     * @param merchantSns 商户号列表
     * @param enableScheduledTime 可调度时间
     */
    public void batchInsertTasks(List<String> merchantSns, Timestamp enableScheduledTime) {
        if (CollectionUtils.isEmpty(merchantSns)) {
            log.warn("商户号列表为空，跳过批量创建任务");
            return;
        }
        
        try {
            Map<InternalScheduleMainTaskDO, List<InternalScheduleSubTaskDO>> tasksMap = Maps.newHashMap();
            
            for (String merchantSn : merchantSns) {
                InternalScheduleMainTaskDO mainTask = buildMainTask(merchantSn);
                if (Objects.nonNull(enableScheduledTime)) {
                    mainTask.setEnableScheduledTime(enableScheduledTime);
                }
                
                InternalScheduleSubTaskDO subTask = buildSubTask(merchantSn);
                tasksMap.put(mainTask, Lists.newArrayList(subTask));
            }
            
            interactionTaskService.batchInsertTasks(tasksMap);
            log.info("成功批量创建海科云闪付报备结果任务, 商户数量: {}, 可调度时间: {}",
                    merchantSns.size(), enableScheduledTime);
        } catch (Exception e) {
            log.error("批量创建海科云闪付报备结果任务失败, 商户数量: {}", merchantSns.size(), e);
            throw new RuntimeException("批量创建任务失败", e);
        }
    }


    /**
     * 构建子任务
     *
     * @param merchantSn 商户号
     * @return 子任务
     */
    private InternalScheduleSubTaskDO buildSubTask(String merchantSn) {
        InternalScheduleSubTaskDO subTaskDO = new InternalScheduleSubTaskDO();
        subTaskDO.setType(getTaskType().getValue());
        subTaskDO.setTaskType("HAIKE_UNION_PAY_CONTRACT_RESULT");
        subTaskDO.setMerchantSn(merchantSn);
        subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue());
        subTaskDO.setPriority(1);
        subTaskDO.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        subTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        return subTaskDO;
    }

    /**
     * 构建主任务
     *
     * @param merchantSn 商户号
     * @return 主任务
     */
    private InternalScheduleMainTaskDO buildMainTask(String merchantSn) {
        InternalScheduleMainTaskDO internalScheduleMainTaskDO = new InternalScheduleMainTaskDO();
        internalScheduleMainTaskDO.setMerchantSn(merchantSn);
        internalScheduleMainTaskDO.setType(getTaskType().getValue());
        internalScheduleMainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.WAIT_PROCESS.getValue());
        internalScheduleMainTaskDO.setAffectStatusSubTaskNum(1);
        internalScheduleMainTaskDO.setAlreadySuccessSubTaskNum(0);
        internalScheduleMainTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        internalScheduleMainTaskDO.setResult("待处理");
        internalScheduleMainTaskDO.setRemark("海科云闪付报备结果查询任务");

        internalScheduleMainTaskDO.setLastScheduledTime(new Timestamp(System.currentTimeMillis()));
        return internalScheduleMainTaskDO;
    }

} 