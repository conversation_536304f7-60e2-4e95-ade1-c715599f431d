package com.wosai.upay.job.refactor.task.rotational.entity;

import com.alibaba.fastjson.JSON;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 轮询任务上下文
 *
 * <AUTHOR>
 * @date 2025/4/23 16:11
 */
@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RotationalTaskContext {

    /**
     * 商户号
     * 必填字段
     */
    private String merchantSn;

    /**
     * 任务ID
     * 当belongToContractTask为true时必填
     */
    private Long contractTaskId;

    /**
     * 子任务ID
     * 当belongToContractTask为true时必填
     */
    private Long contractSubTaskId;

    /**
     * 轮询id
     */
    private String rotationId;

    private RotationalSubTaskTypeEnum subTaskTypeEnum;

    /**
     * 是否属于contractTask任务
     * 必填字段，用于标识该轮询任务是否属于contractTask任务
     */
    private Boolean belongToContractTask;

    /**
     * 参数上下文
     * 非必填字段，用于存放一些额外的参数
     */
    private Map<String, Object> paramContext;

    public Object getParamContextValueByKey(String key) {
        return MapUtils.getObject(paramContext, key);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     * 创建RotationalTaskContext的Builder
     *
     * @return Builder实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * RotationalTaskContext的Builder类
     * 确保rotationId、subTaskTypeEnum和merchantSn是必须输入的字段
     */
    public static class Builder {
        private final RotationalTaskContext context;

        private Builder() {
            context = new RotationalTaskContext();
            // 初始化paramContext为空的HashMap，避免空指针异常
            context.paramContext = new HashMap<>();
        }

        /**
         * 设置商户号（必填）
         *
         * @param merchantSn 商户号
         * @return Builder实例
         */
        public Builder merchantSn(String merchantSn) {
            context.merchantSn = merchantSn;
            return this;
        }

        /**
         * 设置任务ID
         * 当belongToContractTask为true时必填，否则选填
         *
         * @param contractTaskId 任务ID
         * @return Builder实例
         */
        public Builder contractTaskId(Long contractTaskId) {
            context.contractTaskId = contractTaskId;
            return this;
        }

        /**
         * 设置子任务ID
         * 当belongToContractTask为true时必填，否则选填
         *
         * @param contractSubTaskId 子任务ID
         * @return Builder实例
         */
        public Builder contractSubTaskId(Long contractSubTaskId) {
            context.contractSubTaskId = contractSubTaskId;
            return this;
        }

        /**
         * 设置轮询ID（必填）
         *
         * @param rotationId 轮询ID
         * @return Builder实例
         */
        public Builder rotationId(String rotationId) {
            context.rotationId = rotationId;
            return this;
        }

        /**
         * 设置子任务类型枚举（必填）
         *
         * @param subTaskTypeEnum 子任务类型枚举
         * @return Builder实例
         */
        public Builder subTaskTypeEnum(RotationalSubTaskTypeEnum subTaskTypeEnum) {
            context.subTaskTypeEnum = subTaskTypeEnum;
            return this;
        }

        /**
         * 设置是否属于contractTask任务（必填）
         *
         * @param belongToContractTask 是否属于contractTask任务
         * @return Builder实例
         */
        public Builder belongToContractTask(Boolean belongToContractTask) {
            context.belongToContractTask = belongToContractTask;
            return this;
        }

        /**
         * 设置参数上下文（非必填）
         *
         * @param paramContext 参数上下文
         * @return Builder实例
         */
        public Builder paramContext(Map<String, Object> paramContext) {
            if (paramContext != null) {
                context.paramContext.putAll(paramContext);
            }
            return this;
        }

        /**
         * 添加单个参数到参数上下文（非必填）
         *
         * @param key 参数名
         * @param value 参数值
         * @return Builder实例
         */
        public Builder addParam(String key, Object value) {
            if (key != null) {
                context.paramContext.put(key, value);
            }
            return this;
        }

        /**
         * 构建RotationalTaskContext实例
         * 验证必填字段：rotationId、subTaskTypeEnum、merchantSn和belongToContractTask
         * 当belongToContractTask为true时，contractTaskId和contractSubTaskId也是必填字段
         *
         * @return RotationalTaskContext实例
         * @throws IllegalStateException 如果缺少必填字段
         */
        public RotationalTaskContext build() {
            if (Objects.isNull(context.merchantSn) || context.merchantSn.isEmpty()) {
                throw new IllegalStateException("merchantSn是必填字段");
            }
            if (Objects.isNull(context.rotationId) || context.rotationId.isEmpty()) {
                throw new IllegalStateException("rotationId是必填字段");
            }
            if (Objects.isNull(context.subTaskTypeEnum)) {
                throw new IllegalStateException("subTaskTypeEnum是必填字段");
            }
            if (Objects.isNull(context.belongToContractTask)) {
                throw new IllegalStateException("belongToContractTask是必填字段");
            }
            if (Boolean.TRUE.equals(context.belongToContractTask)) {
                if (Objects.isNull(context.contractTaskId)) {
                    throw new IllegalStateException("belongToContractTask为true时，contractTaskId是必填字段");
                }
                if (Objects.isNull(context.contractSubTaskId)) {
                    throw new IllegalStateException("belongToContractTask为true时，contractSubTaskId是必填字段");
                }
            }
            return context;
        }
    }
}
