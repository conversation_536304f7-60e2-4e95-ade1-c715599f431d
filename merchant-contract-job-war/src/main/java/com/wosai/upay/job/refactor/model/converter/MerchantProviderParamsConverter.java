package com.wosai.upay.job.refactor.model.converter;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.CommonUtil;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/6
 */
public class MerchantProviderParamsConverter {

    public static MerchantProviderParams merchantProviderParamsDOToMerchantProviderParams(MerchantProviderParamsDO merchantProviderParamsDO) {
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams();
        merchantProviderParams.setId(merchantProviderParamsDO.getId());
        merchantProviderParams.setMerchant_sn(merchantProviderParamsDO.getMerchantSn());
        merchantProviderParams.setOut_merchant_sn(merchantProviderParamsDO.getOutMerchantSn());
        merchantProviderParams.setChannel_no(merchantProviderParamsDO.getChannelNo());
        merchantProviderParams.setParent_merchant_id(merchantProviderParamsDO.getParentMerchantId());
        merchantProviderParams.setProvider(merchantProviderParamsDO.getProvider());
        merchantProviderParams.setProvider_merchant_id(merchantProviderParamsDO.getProviderMerchantId());
        merchantProviderParams.setPayway(merchantProviderParamsDO.getPayway());
        merchantProviderParams.setParams_config_status(merchantProviderParamsDO.getParamsConfigStatus());
        merchantProviderParams.setPay_merchant_id(merchantProviderParamsDO.getPayMerchantId());
        merchantProviderParams.setWeixin_sub_appid(merchantProviderParamsDO.getWeixinSubAppid());
        merchantProviderParams.setWeixin_subscribe_appid(merchantProviderParamsDO.getWeixinSubscribeAppid());
        merchantProviderParams.setWeixin_sub_mini_appid(merchantProviderParamsDO.getWeixinSubMiniAppid());
        merchantProviderParams.setWeixin_receipt_appid(merchantProviderParamsDO.getWeixinReceiptAppid());
        merchantProviderParams.setStatus(merchantProviderParamsDO.getStatus());
        merchantProviderParams.setCtime(merchantProviderParamsDO.getCtime());
        merchantProviderParams.setMtime(merchantProviderParamsDO.getMtime());
        merchantProviderParams.setDeleted(Objects.equals(merchantProviderParamsDO.getDeleted(), 1));
        merchantProviderParams.setVersion(merchantProviderParamsDO.getVersion());
        merchantProviderParams.setContract_rule(merchantProviderParamsDO.getContractRule());
        merchantProviderParams.setRule_group_id(merchantProviderParamsDO.getRuleGroupId());
        merchantProviderParams.setUpdate_status(merchantProviderParamsDO.getUpdateStatus());
        if (Objects.nonNull(merchantProviderParamsDO.getExtra())) {
            merchantProviderParams.setExtra(merchantProviderParamsDO.getExtra().getBytes(StandardCharsets.UTF_8));
        }
        merchantProviderParams.setAuth_status(merchantProviderParamsDO.getAuthStatus());
        merchantProviderParams.setWx_settlement_id(merchantProviderParamsDO.getWxSettlementId());
        merchantProviderParams.setWx_use_type(merchantProviderParamsDO.getWxUseType());
        merchantProviderParams.setAli_mcc(merchantProviderParamsDO.getAliMcc());
        merchantProviderParams.setMerchant_state(merchantProviderParamsDO.getMerchantState());
        return merchantProviderParams;
    }
}
