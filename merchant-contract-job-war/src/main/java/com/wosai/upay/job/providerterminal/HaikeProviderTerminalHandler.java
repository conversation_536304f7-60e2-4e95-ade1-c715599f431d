package com.wosai.upay.job.providerterminal;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.enume.ProviderTerminalIdTypeEnum;
import com.wosai.upay.job.providerterminal.model.ProviderTerminalAddContext;
import com.wosai.upay.job.util.ScaleConverterUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Component
public class HaikeProviderTerminalHandler extends AbstractProviderTerminalHandler {

    @Override
    protected List<Integer> getSupportPaywayList() {
        return ALIPAY_WEIXIN_LIST;
    }

    @Override
    public ProviderEnum getProvider() {
        return ProviderEnum.PROVIDER_HAIKE;
    }

    @Override
    protected String contractProviderTerminalId(ProviderTerminalAddContext request) {
        ProviderTerminalIdTypeEnum type = isProdEnvironment ?
                ProviderTerminalIdTypeEnum.GENERAL_PREFIX : ProviderTerminalIdTypeEnum.GENERAL_TEST_PREFIX;
        String prefix = type.getCode();
        String nextTerminalId = ScaleConverterUtil.encode36(Long.parseLong(snGenerator.nextProviderTerminalId()));
        if (StringUtils.isNotBlank(prefix)) {
            return prefix + nextTerminalId;
        }
        return nextTerminalId;
    }
}
