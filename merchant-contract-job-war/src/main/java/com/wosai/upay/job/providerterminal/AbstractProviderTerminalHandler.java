package com.wosai.upay.job.providerterminal;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.core.service.SnGenerator;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.enume.ProviderTerminalBindLevel;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.ProviderTerminal;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalAcquirerMerchantInfo;
import com.wosai.upay.job.providerterminal.model.ProviderTerminalAddContext;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 抽象终端处理器
 * 处理不同级别的终端绑定（商户级、门店级、终端级）
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
public abstract class AbstractProviderTerminalHandler implements ProviderTerminalHandler {

    // 静态变量定义常用的支付方式列表
    protected static final List<Integer> ALIPAY_WEIXIN_LIST = Arrays.asList(
            PaywayEnum.ALIPAY.getValue(),
            PaywayEnum.WEIXIN.getValue()
    );

    protected static final List<Integer> ALIPAY_WEIXIN_UNIONPAY_LIST = Arrays.asList(
            PaywayEnum.ALIPAY.getValue(),
            PaywayEnum.WEIXIN.getValue(),
            PaywayEnum.UNIONPAY.getValue()
    );

    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private StoreService storeService;
    @Autowired
    protected SnGenerator snGenerator;
    @Autowired
    protected Environment environment;
    @Autowired
    protected ContractTaskBiz contractTaskBiz;
    @Autowired
    protected ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private ProviderTerminalTaskRepository providerTerminalTaskRepository;
    @Autowired
    private Validator validator;
    protected boolean isProdEnvironment;

    @Autowired
    public void setEnvironment(Environment environment) {
        this.environment = environment;
        // 在初始化时确定环境，避免每次调用都进行判断
        this.isProdEnvironment = Arrays.asList(environment.getActiveProfiles()).contains("prod");
    }

    @Override
    public String addProviderTerminal(ProviderTerminalAddContext context) {
        validator.validate(context);

        ProviderTerminalBindLevel bindLevel = context.getBindLevel();
        
        switch (bindLevel) {
            case MERCHANT:
                return processMerchantLevelBind(context);
            case STORE:
                return processStoreLevelBind(context);
            case TERMINAL:
                return processTerminalLevelBind(context);
            default:
                throw new ContractBizException("不支持的绑定级别");
        }
    }

    /**
     * 处理商户级绑定
     */
    private String processMerchantLevelBind(ProviderTerminalAddContext context) {
        ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo = context.getAcquirerMerchantInfo();
        
        // 检查是否已存在终端
        ProviderTerminal existingTerminal = providerTerminalBiz.existMerchantProviderTerminal(
                acquirerMerchantInfo.getProvider().getValue(),
                context.getMerchantSn(), 
                acquirerMerchantInfo.getAcquirerMerchantId());

        // 获取或创建终端ID
        String providerTerminalId = getOrCreateProviderTerminalId(context, existingTerminal);
        if (WosaiStringUtils.isEmpty(providerTerminalId)) {
            return null;
        }
        
        // 如果不存在终端且成功创建，则建立连接
        if (Objects.isNull(existingTerminal)) {
            providerTerminalBiz.merchantConnectionProviderTerminal(
                    context.getMerchantSn(),
                    providerTerminalId,
                    acquirerMerchantInfo.getAcquirerMerchantId(),
                    acquirerMerchantInfo.getProvider().getValue());
        }
        
        // 创建绑定任务
        createBindTasks(context, acquirerMerchantInfo, existingTerminal, providerTerminalId, null, null);
        
        return providerTerminalId;
    }

    /**
     * 处理门店级绑定
     */
    private String processStoreLevelBind(ProviderTerminalAddContext context) {
        ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo = context.getAcquirerMerchantInfo();
        if (!checkNeedAddProviderTerminal(context)) {
            return null;
        }
        
        // 检查是否已存在终端
        ProviderTerminal existingTerminal = providerTerminalBiz.existStoreProviderTerminal(
                acquirerMerchantInfo.getProvider().getValue(),
                context.getStoreSn(),
                context.getMerchantSn(), 
                acquirerMerchantInfo.getAcquirerMerchantId());
        
        // 获取或创建终端ID
        String providerTerminalId = getOrCreateProviderTerminalId(context, existingTerminal);
        if (WosaiStringUtils.isEmpty(providerTerminalId)) {
            return null;
        }
        // 如果不存在终端且成功创建，则建立连接
        if (Objects.isNull(existingTerminal)) {
            providerTerminalBiz.sqbStoreTerminalConnectionProviderTerminal(
                    context.getMerchantSn(),
                    providerTerminalId,
                    acquirerMerchantInfo.getAcquirerMerchantId(),
                    acquirerMerchantInfo.getProvider().getValue(),
                    context.getStoreSn());
        }
        
        // 创建绑定任务
        createBindTasks(context, acquirerMerchantInfo, existingTerminal, providerTerminalId, context.getStoreSn(), null);
        
        return providerTerminalId;
    }

    /**
     * 处理终端级绑定
     */
    private String processTerminalLevelBind(ProviderTerminalAddContext context) {
        ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo = context.getAcquirerMerchantInfo();
        if (!checkNeedAddProviderTerminal(context)) {
            return null;
        }
        
        // 获取终端信息
        Map terminalInfo = terminalService.getTerminalByTerminalSn(context.getTerminalSn());
        String storeSn = WosaiMapUtils.getString(terminalInfo, "store_sn");
        String vendorAppAppid = WosaiMapUtils.getString(terminalInfo, "vendor_app_appid");

        // 检查是否已存在终端
        ProviderTerminal existingTerminal = providerTerminalBiz.existProviderTerminal(
                acquirerMerchantInfo.getProvider().getValue(),
                storeSn, 
                context.getTerminalSn(),
                context.getMerchantSn(), 
                acquirerMerchantInfo.getAcquirerMerchantId());
        
        // 获取或创建终端ID
        String providerTerminalId = getOrCreateProviderTerminalId(context, existingTerminal);
        if (WosaiStringUtils.isEmpty(providerTerminalId)) {
            return null;
        }
        // 如果不存在终端且成功创建，则建立连接
        if (Objects.isNull(existingTerminal)) {
            providerTerminalBiz.sqbTerminalConnectionProviderTerminal(
                    context.getMerchantSn(),
                    providerTerminalId,
                    acquirerMerchantInfo.getAcquirerMerchantId(),
                    acquirerMerchantInfo.getProvider().getValue(),
                    vendorAppAppid,
                    context.getTerminalSn(),
                    storeSn);
        }
        
        // 创建绑定任务
        createBindTasks(context, acquirerMerchantInfo, existingTerminal, providerTerminalId, storeSn, context.getTerminalSn());
        
        return providerTerminalId;
    }

    /**
     * 校验是否需要添加终端  如果该通道支持绑定子商户号&子商户号都是空的话，则不添加终端
     * @param context 请求上下文
     * @return true：需要添加终端，false：不需要添加终端
     */
    private boolean checkNeedAddProviderTerminal(ProviderTerminalAddContext context) {
        if (WosaiCollectionUtils.isNotEmpty(getSupportPaywayList())) {
            List<MerchantProviderParams> allSubMerchantParams = getAllSubMerchantParams(context.getAcquirerMerchantInfo());
            if (WosaiCollectionUtils.isEmpty(allSubMerchantParams)) {
                log.info("该通道支持绑定子商户号，但未找到需要绑定的子商户号，不生成终端和绑定任务，上下文：{}", JSON.toJSONString(context));
                return false;
            }
        }
        return true;
    }

    /**
     * 获取或创建终端ID
     */
    private String getOrCreateProviderTerminalId(ProviderTerminalAddContext context, ProviderTerminal existingTerminal) {
        if (Objects.nonNull(existingTerminal)) {
            log.info("终端已经存在ID: {}, 上下文：{}", existingTerminal.getProvider_terminal_id(), JSON.toJSONString(context));
            return existingTerminal.getProvider_terminal_id();
        }
        if (WosaiStringUtils.isNotEmpty(context.getProviderTerminalId())) {
            return context.getProviderTerminalId();
        }
        String newTerminalId = contractProviderTerminalId(context);
        if (WosaiStringUtils.isNotEmpty(newTerminalId)) {
            log.info("创建新的终端ID: {}, 上下文: {}", newTerminalId, JSON.toJSONString(context));
        }
        return newTerminalId;
    }

    /**
     * 创建绑定任务
     */
    private void createBindTasks(ProviderTerminalAddContext context, 
                               ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo,
                               ProviderTerminal existingTerminal, 
                               String providerTerminalId, 
                               String storeSn, 
                               String terminalSn) {
        if (Objects.isNull(providerTerminalId)) {
            return;
        }

        List<MerchantProviderParams> needBindParams = getNeedBindParams(acquirerMerchantInfo, existingTerminal);
        
        if (WosaiCollectionUtils.isNotEmpty(needBindParams)) {
            ProviderTerminalTaskTypeEnum taskType = determineTaskType(context.getBindLevel());
            
            needBindParams.forEach(param -> 
                providerTerminalTaskRepository.addBoundTerminalTask(
                        context.getMerchantSn(),
                        param.getPay_merchant_id(),
                        param.getProvider(),
                        param.getPayway(),
                        taskType.getType(),
                        providerTerminalId,
                        storeSn,
                        terminalSn,
                        acquirerMerchantInfo.getMerchantSn()));
            
            log.info("创建绑定任务完成, 上下文: {}, 交易参数: {}", JSON.toJSONString(context), JSON.toJSONString(needBindParams));
        }
    }

    /**
     * 确定任务类型
     */
    private ProviderTerminalTaskTypeEnum determineTaskType(ProviderTerminalBindLevel bindLevel) {
        switch (bindLevel) {
            case MERCHANT:
                return ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL;
            case STORE:
                return ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH;
            case TERMINAL:
                return ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH;
            default:
                return ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH;
        }
    }

    /**
     * 获取需要绑定的参数列表
     */
    private List<MerchantProviderParams> getNeedBindParams(ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo, 
                                                          ProviderTerminal providerTerminal) {
        // 获取收单机构参数
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.selectByPayMerchantIdAndMerchantSn(
                acquirerMerchantInfo.getAcquirerMerchantId(), 
                acquirerMerchantInfo.getMerchantSn());
        
        if (Objects.isNull(acquirerParams)) {
            return Collections.emptyList();
        }

        // 获取支持的支付方式列表
        List<Integer> supportPaywayList = getSupportPaywayList();
        if (WosaiCollectionUtils.isEmpty(supportPaywayList)) {
            return Collections.emptyList();
        }

        // 查询需要绑定的参数
        List<MerchantProviderParams> params = getAllSubMerchantParams(acquirerMerchantInfo);
        
        // 如果存在终端，过滤掉已同步的参数
        if (Objects.nonNull(providerTerminal)) {
            return providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        } else {
            return params;
        }
    }

    protected List<MerchantProviderParams> getAllSubMerchantParams(ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(acquirerMerchantInfo.getMerchantSn())
                .andPaywayIn(getSupportPaywayList())
                .andProviderEqualTo(getProvider().getValue())
                .andDeletedEqualTo(false);

        return merchantProviderParamsMapper.selectByExample(example);
    }

    /**
     * 获取支持的支付方式列表
     */
    protected abstract List<Integer> getSupportPaywayList();

    /**
     * 创建终端ID
     */
    abstract String contractProviderTerminalId(ProviderTerminalAddContext request);
}
