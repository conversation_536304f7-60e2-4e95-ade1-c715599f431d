package com.wosai.upay.job.providers;

import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component(ProviderUtil.ZJTLCB_CHANNEL)
public class ZjtlcbProvider extends AbstractProvider {

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        return null;
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        return null;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        return null;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        throw new ContractBizException(getProviderBeanName() + "暂不支持配置微信appid");
    }
}
