jsonrpc.merchant_contract=http://merchant-contract
jsonrpc.core_business=http://app-core-business
jsonrpc.notice_service=http://notice-service
jsonrpc.app_push_service=http://app-push-service
jsonrpc.app_backend_service=http://app-backend-service
jsonrpc.business_log=http://business-log
jsonrpc.business_log_stash.server=http://business-logstash
jsonrpc.opr_merchant_activity=http://opr-merchant-activity
jsonrpc.shouqianba_merchant.server=http://shouqianba-merchant-service
jsonrpc.crm=http://sales-system-service
jsonrpc.sales-system-poi=http://sales-system-poi
jsonrpc.remit.order.server=http://remit-gateway
jsonrpc.upay.side.server=http://upay-side
jsonrpc.bank-info.server=http://bank-info-service
jsonrpc.merchant-bank.server=http://merchant-bank-service
jsonrpc.merchant-audit.server=http://merchant-audit-service
jsonrpc.merchant-enrolment.server=http://merchant-enrolment
jsonrpc.risk-disposal.server=http://risk-disposal
jsonrpc.trade_manage=http://trade-manage-service
jsonrpc.upay-wallet=http://upay-wallet.vpc.shouqianba.com
jsonrpc.withdraw-service=http://withdraw-service.internal.shouqianba.com
jsonrpc.finance-backend=http://finance-backend
jsonrpc.alipay=http://alipay-authinto
jsonrpc.profit-sharing=http://profit-sharing
jsonrpc.merchant_user_service=http://merchant-user-service
jsonrpc.aop_gate=http://aop-gateway
jsonrpc.sp-workflow-service=http://sp-workflow-service
jsonrpc.merchant-center=http://merchant-center
jsonrpc.crow=http://crow-server.internal.shouqianba.com
jsonrpc.qrcode=http://upay-qrcode-internal.shouqianba.com
jsonrpc.crm-customer-relation=http://crm-customer-relation
jsonrpc.upay-transaction=http://upay-transaction-export.internal.shouqianba.com
jsonrpc.pay-business-open=http://pay-business-open
jsonrpc.core-crypto=http://core-crypto
jsonprc.authcode-service=http://authcode
jsonrpc.sp-task=http://sp-task
jsonrpc.contract-activity=http://merchant-contract-activity
jsonrpc.merchant_level=http://merchant-level
jsonrpc.boss-circle-user=http://boss-circle-user
jsonrpc.agreement-manage=http://agreement-manage
jsonrpc.merchant-business-open = http://merchant-business-open
jsonrpc.credit-pay-backend= http://credit-pay-backend
jsonrpc.sales-system-backend=http://sales-system-backend
jsonrpc.crm-todo-api=http://crm-todo
jsonrpc.risk-service=http://shouqianba-risk-service
jsonrpc.merchant-contract-access=http://merchant-contract-access
jsonrpc.brand-business=http://brand-business
jsonrpc.partner-payment-hub: http://partner-payment-hub
jsonrpc.lakala-proxy=http://lakala-proxy
jsonrpc.dc-service=http://dc-service
jsonrpc.jupiter=http://jupiter

##连接池配置
spring.datasource.url=pk-merchant-contract-job-merchant_contract-8469?useUnicode=yes&socketTimeout=15000
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.connection-test-query=select 1 from dual
# 拉卡拉图片上传时间
contract.lakala.upload.time=10000
# 拉卡拉轮询查询时间
contract.lakala.task.query.time=180000
#银联开放平台轮询查询时间
contract.union.open.query.time=180000
# redis
spring.redis.database=1
spring.redis.host=r-bp19d650a497a5c4879.redis.rds.aliyuncs.com
spring.redis.port=6379
spring.redis.password=vJfjOW8eiQYTJS68xVP72aXD
spring.redis.pool.max-active=8
spring.redis.pool.max-wait=-1
spring.redis.pool.max-idle=8
spring.redis.pool.min-idle=0
spring.redis.timeout=30000
#富友提交对公凭证审批模板id
fuyou.approval.templateId=2629
spring.application.name=merchant-contract-job
app.h5_url=https://subscribe-wechat-app.shouqianba.com/#/status
contract.type.lakala=WSJG
#kafka相关配置
bank.message.send.topic=events.upay.merchant-contract-bank-status
spring.kafka.bootstrap-servers=conflunt-kafka-01.shouqianba.com:9092,conflunt-kafka-02.shouqianba.com:9092,conflunt-kafka-03.shouqianba.com:9092 ,conflunt-kafka-04.shouqianba.com:9092,conflunt-kafka-05.shouqianba.com:9092
spring.kafka.registry-servers=http://conflunt-schema-01.shouqianba.com:8081,http://conflunt-schema-02.shouqianba.com:8081,http://conflunt-schema-03.shouqianba.com:8081,http://conflunt-schema-04.shouqianba.com:8081,http://conflunt-schema-05.shouqianba.com:8081
#设置一个默认组
spring.kafka.consumer.group-id=merchant-contract-job
spring.kafka.producer.group-id=merchant-contract-job
spring.kafka.topics.merchant-message=events.upay.merchant-contract-job.merchant-message
shence.topic.wxApply=events.merchant-contract-job.wx_apply
sensor.topic.bind_bank=events.upay.merchant-bind-bank
#阿里kafka相关配置
spring.kafka.ali.bootstrap-servers=aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
spring.kafka.ali.registry-servers=http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
spring.kafka.ali.consumer.group-id=merchant-contract-job

#间连扫码进件
sensor.topic.contract_action=events.merchant-contract-job.contract-action
#dts.params中 '|'为完整dts订阅通道参数分隔符
dts.params=gw2_data_sync,LTAIILAOJ1Zcd9df,SSM9OVuyiGWGYnhLazh0YJR87l6ODr,dtst4w6ud9p1zdu
dts.init=true
schedule.init=true
mail.init=true
databus.consumer.topic=databus.event.merchant.basic.allin,databus.event.merchant.config.allin
databus.consumer.store.topic=databus.event.store.basic.allin
databus.consumer.terminal.topic=databus.event.terminal.basic.allin
databus.consumer.brokers=172.16.12.175:9092,172.16.11.238:9092,172.16.11.237:9092,172.16.10.85:9092,172.16.10.86:9092
databus.consumer.group-id=merchant-contract-job
databus.consumer.schema-registry-url=http://172.16.12.175:8081,http://172.16.11.238:8081,http://172.16.11.237:8081,http://172.16.10.85:8081,http://172.16.10.86:8081
#微信二维码生成的文件目录
wechat_applet_code.build.path=/tmp/
weixin.apply.query.time=5000
weixin.auth.query.today.time=10000
lkl.channelno=WSJG
lkl.v3.channelno=247587
tl.channelno=7895
tl.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCm9OV6zH5DYH/ZnAVYHscEELdCNfNTHGuBv1nYYEY9FrOzE0/4kLl9f7Y9dkWHlc2ocDwbrFSm0Vqz0q2rJPxXUYBCQl5yW3jzuKSXif7q1yOwkFVtJXvuhf5WRy+1X5FOFoMvS7538No0RpnLzmNi3ktmiqmhpcY/1pmt20FHQQIDAQAB
acquirer-change-day-cron=8 * * * * ?
feerate-activity-cron=5 0 0 * * ?
indirect-pay.dev_code=OEBIBCAY2K1H
#微信直连相关的配置
weixin.direct.online=ZQ5TCRD9REPF
weixin.direct.offline=CW2DQZJXG4YT
weixin.direct.apply.query.time=300000
#支付宝直连相关的配置
ali.direct=0T2XYW1LQTFL
direct.apply.topic=events.merchant-contract-job.direct.apply

#接收阿里推送消息
ant.target.serve=openchannel.alipay.com
mail-gateway=http://mail-gw-internal.shouqianba.com/email

BLUESEA_TAG=a2185803-936f-4fe3-bc67-f90f9b276597
KX_TAG=878e7c02-12af-4cd0-809f-107b47c64953
tradeAppId=2
#线下教培邮件发送时间
offline_edu_train=0 0 1 * * ?
#Ep101通知标识
clientSide.notice.devCode=OEBIBCAY2K1H
#小微商户升级crm通知
clientSide.crm.devCode=OEBIBCAY2K1H
#小微商户升级app通知
clientSide.app.devCode=OEBIBCAY2K1H
#小微商户升级crm通知
clientSide.crm.templateCode=H6U7MDNZCJBM
#小微商户升级失败crm通知
clientSide.crm.failTemplateCode=RIYPEC23GPZE
#小微商户升级app通知
clientSide.app.templateCode=YTG5FQXXRX8T

#银商定时查询时间
contract.ums.task.query.time=300000

#邮储签约通知code
psbc_template_code=D9BQGNTI7892
#银行合作开发通知code
psbc_bank_notice_dev_code=HSNRPQMTVK8Q
#邮储业务标识
psbc_business_dev_code=HSNRPQMTVK8Q

#银行直连微信商家认证查询频率 5分钟
process_status_wxAuth=0 0/5 * * * ?
#银行创建切换任务查询频率
process_bank_changeAcquire=300000
#实名成功切换收单机构 每日凌晨
process_status_changeAcquire=0 0 2 1/1 * ?
#广发
cgb_dev_code=1WDKRPLTXUHN


#高校食堂标签Id
schoolcanteen_tag=018ffbb4-08be-4962-8a3c-fda31b7120e0

shutdown.sleep=30000
#蚂蚁门店查询时间
ant.shop.query.time=300000

# 银行进件轮询间隔
contract.bank.time=60000

#切换收单机构银行卡前置校验延长时间
bank_delay_minute_time=30

#邮储通知crm协助商户完成实名,模板code
psbc_crm_template_code = APSFDEZRKSHF
#邮储通知crm协助商户完成实名,开发标识
psbc_crm_dev_code = SQLKC6OKJK77

#邮储通知商户完成实名,模板code
psbc_app_template_code = IQDV3GBNXZWL
#邮储通知商户完成实名,开发标识
psbc_app_dev_code = HSNRPQMTVK8Q

#银商入网中 切换 拉卡拉
contract.ums.event.ing.time=600000

#建行应用标识
ccb_dev_code=WGJFC1VVM8VA
#建行通知商户app签约模版code
ccb_sign_template_code=HMLQEF5PDMDU
#建行通知crm协助商户完成实名,模板code和开发code
ccb_crm_template_code=APSFDEZRKSHF
ccb_crm_dev_code=SQLKC6OKJK77
#建行通知app商户完成实名,模板code和开发code
ccb_app_template_code=IQDV3GBNXZWL
ccb_app_dev_code=WGJFC1VVM8VA
indirect_crm_customer_relation=5PPoi00qcj
kabu_organization_path=00069
query_ccb_contract_status_cron=0 0/10 * * * ?
#维护人code
maintain.devCode=bfXUKgs5th
#小微营业执照升级模板
upgrade.applyId=467
#小微营业执照升级模板
upgrade.authApplyId=473

#客服作业平台进入详情地址
customer_platform_address_url1=https://spa-upay-api.shouqianba.com/subtaskStatus/getLacarraSubtaskStatus?id=%s&merchant_sn=%s
customer_platform_address_url2=https://spa-upay-api.shouqianba.com/subtaskStatus/getWeChatAndAlipaySubtaskStatus?id=%s&merchant_sn=%s&p_task_id=%s

#app开通数字钱包跳转地址
ccb_decp_wait_open=https://side-page.shouqianba.com/side-page/CCBdigitalmoneyOpen/index.html?env=prod&token=:token
ccb_decp_process_open=https://side-page.shouqianba.com/side-page/CCBdigitalmoneyOpening.html
ccb_decp_success_open=https://side-page.shouqianba.com/side-page/CCBdigitalmoneySuccess/index.html?env=prod&token=:token
ccb_decp_fail_open=https://side-page.shouqianba.com/side-page/CCBdigitalmoneyFail.html?env=prod&token=:token
merchant-contract.access_id=5661bd1b-d34d-4fa1-af39-87fbf42d30aa
merchant-contract.access_secret=6f038091aefe4818b459d30a87046378
ccb_decp.template_id=ELXR1VDVRJ10
ccb_decp_success_notice_code=VSEWINDHQVUM
ccb_decp_success_push_code=VU078SDJQQX2
#银行直连报表统计邮件发送时间
send_success_mail=0 3 6 * * ?
#银行活动商户微信实名认证派工
wx_task_template_id=281
#银行活动商户协议签约派工
sign_task_template_id=282

#泸州银行应用标识
lzb_dev_code=PJNIX0PR4D0I
lzb_trade_combo_id=532

#华夏应用标识
hxb_dev_code=SGJWMX7JFINP
#华夏通知crm协助商户完成实名,模板code和开发code
hxb_crm_template_code=YM5ZXKDOGTCG
hxb_crm_dev_code=SGJWMX7JFINP
#华夏通知收钱吧App协助商户完成实名,模板code和开发code
hxb_app_template_code=H7XEDWO6MARZ
hxb_app_dev_code=SGJWMX7JFINP
#华夏状态变更钉钉提醒
#工商银行应用标识
icbc_dev_code=IF5FDZN3HZCZ
#民生银行应用标识
cmbc_dev_code=GYESQTY4JFXP
#民生银行套餐ID
cmbc_combo_id = 564
#平安银行应用标识
pab_dev_code=G1ACMVIK2VAN
pab_trade_combo_id=457
zjtlcb_trade_combo_id=510
#平安签约通知code
pab_template_code=A99KIIXQW9L9
#平安签约dev_code
pab_sign_dev_code=G1ACMVIK2VAN
#平安微信实名app通知模板
pab_app_template_code=BVLBDFGVUUDD
#平安微信实名app通知业务标识
pab_app_dev_code=G1ACMVIK2VAN

#支付网关预下单
upay_gateway_precreate=http://upay-gateway.vpc.shouqianba.com/upay/v2/precreate

# 银行合作
bank_biz.devCode=XTISBCRB2WXW
# 第一次切换收单机构通知BD模板
bank_biz.first_change_acquirer_crm_template_code=JSUQR613YGHV
# 后续切换收单机构通知BD模板
bank_biz.change_acquirer_crm_template_code=IHSUKMEN4RLW
hxb_check_tag_1=13cef25f-6281-41b0-a40a-0e4b250c2f9c
#hxb_check_value_1=华夏额度包
crow.merchant_entry_id=dd0cd0fb-0659-4cc5-b7c2-f72637fde27c


#crm应用标识
hxb_app_id=417a881a-a3e7-4e46-860a-55d9a8347347
icbc_app_id=dbd99592-6d28-4b04-a8ad-0811fffb6998
pab_app_id=9cd5c988-1a92-4e2c-b29f-39579bf988f7
#建行批量导入
ccb_trade_combo_id=128
ccb_tag=ec4df296-762a-4b15-b887-207ff7cdfe93
ccb_app_id=8e21dd52-4b66-4b13-a7a0-93d50b60743e
ccb_user_id=eaab0059-2696-4d3e-a16e-602abc7475ae

#消息消费开关
consumer.init = true

# redisson
redisson.redis.host=r-bp19d650a497a5c4879.redis.rds.aliyuncs.com
redisson.redis.port=6379
redisson.redis.database=1
redisson.redis.password=vJfjOW8eiQYTJS68xVP72aXD
#华夏银行多业务套餐
hxb_multi_trade=440



#先享后付通知配置
pay_later.combId=454
pay_later.devCode=I0UDXL7CVVCT
pay_later.zftTemplateCode=AACBH6XMDXQK
pay_later.zhiMaTemplateCode=9JGPUY4ARTE2
#直付通状态查询
queryZftApply=0 0/20 * * * ?

#火山相关配置
spring.kafka.bootstrap-servers-data-center=aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
topic.kafka.data-center=analytics_data_volcengine_push

#芝麻商户审核是否超时,3个小时运行一次
zhiMa_merchant_timeout=********

lkl_pso_dev_code=GF7GNKL9O48N
fy_pso_dev_code=EK6NH2M5ZFQA

# 银行自动回切三方
bank_error_change_to_third_task_template_id=519
bank.cooperation.notice.app.devCode=XTISBCRB2WXW
bank.cooperation.notice.crm.devCode=XTISBCRB2WXW
bank.auto.change.third.notify.template_id=0UEO8BU1XILD
third.auto.change.bank.notify.template_id=LC3T1L8QPLLA
close.trade.protection.notify.template_id=ZVGXSX7DPX8B
open.trade.protection.notify.template_id=JZUXGLCGJETK
bank.trade.protection.close.template_id=UDO5VJUKLRNB
bank.trade.protection.not.close.template_id=TBY27NW2R6AZ
bank.trade.protection.recover.template_id=8BXOJ9BHBQLA
bank.trade.protection.not.recover.template_id=1L68BGJ8EEVN
bank.trade.protection.log.template.code=6QVNUUVO4N2V
third.auto.change.bank.notify.devCode=XTISBCRB2WXW


#富友一体化外卡
fyForeignCard.devCode = EDNGVEMIL2SI
#拉卡拉一体化外卡
lklForeignCard.devCode=OYLIAHFL3SEK
#拉卡拉手机pos外卡
lklMobilePos.devCode=FEKUON3QNHPJ

#拉卡拉手机pos业务在crm的应用主键
lklMobilePos.appId=c79062e0-cfc0-4318-8666-e829420213b5
lkl_open.contract_rule=lkl_open-1034-17-247587
##对接错误码管理平台
spring.scene-manage-service.url: http://scene-manage-service/
spring.scene-manage-service.project_name: merchant-contract
spring.scene-manage-service.synonym_map:{"payway":[[1,2],[3,4,7]]} 

#京东钱包套餐
jd.combId=507

#b2b的appid
b2b.appid=34

#b2b活动费率套餐的模板
trade.comboId=542

#标准活动费率套餐的模板
trade.baseComboId=545

wx_complaint_task_template_id=568

tag.auth.layer.wx.contract=c9f14fd3-3ef1-422c-87dc-74f160a9127a
tag.auth.layer.ali.contract=bbd9a591-ae56-428f-95e2-ed0562a349ea

#通道预授权
lkl.preAuth.devCode = lkl
fy.preAuth.devCode = IGS3ZUGTRHNB
#应用ID
fy.preAuth.appId = 3feb7e18-0870-42d9-9ed6-501d9164fb6a


#拉卡拉一体化刷卡待办跳转
lkl.pos.todo.appUrl=
#拉卡拉一体化刷卡appid
lkl.pos.appId=23a818de-609f-4d12-82e5-f8d998ebd8c6
#拉卡拉一体化外卡待办跳转
lkl.foreign.todo.appUrl=
#拉卡拉一体化外卡appid
lkl.foreign.appId=7f5967e3-0900-4e91-aa0f-8adb77411cea

#中投科信应用标识
umb_dev_code=VTP5BF4XDINP
umb_platmer_id_1=umb-CF2002323280
umb_platmer_id_2=umb-CF2002323296
umb_trade_combo_id=544
umb.tag=daf44817-7e41-415c-bf08-b837fd447532


## start 业务方名称和业务方应用ID的对应关系

# 扫码点单
scan_order=2
# 支付业务
payment=1
# 刷卡收款
card_payment=6
# 银行合作
bank_cooperation=3
# 线上
online=22
# 跨城收款
cross_city_payment=17
# 手机POS
mobile_pos=28
# 校园外卖
campus_food_delivery=4

## end 业务方名称和业务方应用ID的对应关系

# 通联刷卡
tl_pos_devCode= FJYKBDP5YLFQ
#通联一体化外卡
tl_foreignCard_devCode = ULO2JKRDDUHA
#通联预授权
tl_preAuth_devCode = MFAD2VNCGWT2
#通联预授权appid
tl.preAuth.appId = 6c47834d-184b-4499-b67b-db775f8234b6

bcs_dev_code=NDDG9DXZ9W0P
bcs_trade_combo_id=580
bcs_sm2_private_key=cb206ca0c6550c9846b0467b1119b1bd61ab84508775880fba60bc03eaf63d23
bcs_sm2_public_key=0435F399F1ACAA1E62A175FF43BF975E43296520E22BD68C97E5CE2FBE71740C43FF7635969DF514D137C989835D17EA9558424A4EA322D03E0B7F506BF8CD0114
bcs_app_template_code=O143YXFJ7MFJ
