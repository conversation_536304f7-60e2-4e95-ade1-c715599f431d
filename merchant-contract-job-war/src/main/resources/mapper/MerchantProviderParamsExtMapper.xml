<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.MerchantProviderParamsExtMapper">
    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.DO.MerchantProviderParamsExt"
            parameterType="java.lang.Long">
    select * from merchant_provider_params_ext
    where id = #{id,jdbcType=BIGINT}
  </select>

    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.MerchantProviderParamsExt"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into merchant_provider_params_ext
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="param_id != null">
                param_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="ext_field_1 != null">
                ext_field_1,
            </if>
            <if test="ext_field_2 != null">
                ext_field_2,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="extra != null">
                extra,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="param_id != null">
                #{param_id,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="ext_field_1 != null">
                #{ext_field_1,jdbcType=VARCHAR},
            </if>
            <if test="ext_field_2 != null">
                #{ext_field_2,jdbcType=VARCHAR},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="extra != null">
                #{extra,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.MerchantProviderParamsExt">
        update merchant_provider_params_ext
        <set>
            <if test="param_id != null">
                param_id = #{param_id,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="ext_field_1 != null">
                ext_field_1 = #{ext_field_1,jdbcType=VARCHAR},
            </if>
            <if test="ext_field_2 != null">
                ext_field_2 = #{ext_field_2,jdbcType=VARCHAR},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="extra != null">
                extra = #{extra,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>