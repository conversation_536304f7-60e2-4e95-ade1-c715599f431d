<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.CcbConfigMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.ccbConfig.CcbConfig" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="district_code" property="district_code" jdbcType="VARCHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="private_min_price" property="private_min_price" jdbcType="VARCHAR" />
    <result column="public_min_price" property="public_min_price" jdbcType="VARCHAR" />
    <result column="support_select_ins_no" property="support_select_ins_no" jdbcType="BIT" />
    <result column="ins_no" property="ins_no" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="is_auto_change" property="is_auto_change" jdbcType="BIT" />
    <result column="delay_day" property="delay_day" jdbcType="INTEGER" />
    <result column="ctime" property="ctime" jdbcType="BIGINT" />
    <result column="mtime" property="mtime" jdbcType="BIGINT" />
    <result column="deleted" property="deleted" jdbcType="BIT" />
    <result column="version" property="version" jdbcType="BIGINT" />
    <result column="micro_info" property="micro_info" jdbcType="BIT" />
    <result column="black_mcc" property="black_mcc" jdbcType="VARCHAR" />
    <result column="apply_unionpay" property="apply_unionpay" jdbcType="BIT" />
    <result column="apply_decp" property="apply_decp" jdbcType="BIT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.ccbConfig.CcbConfig" extends="BaseResultMap" >
    <result column="filter_rules" property="filter_rules" jdbcType="LONGVARCHAR" />
    <result column="ins_no_list" property="ins_no_list" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, district_code, province, city, private_min_price, public_min_price, support_select_ins_no, ins_no, account,
    is_auto_change, delay_day, ctime, mtime, deleted, version, micro_info, black_mcc , apply_unionpay, apply_decp
  </sql>
  <sql id="Blob_Column_List" >
    filter_rules,ins_no_list
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.wosai.upay.job.model.DO.CcbConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ccb_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.wosai.upay.job.model.DO.CcbConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from ccb_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ccb_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ccb_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfig" >
    insert into ccb_config (id, district_code, province, 
      city, private_min_price, public_min_price, support_select_ins_no,
      ins_no, ins_no_list, account, is_auto_change,
      delay_day, ctime, mtime, 
      deleted, version, micro_info, 
      black_mcc, filter_rules, apply_unionpay, apply_decp)
    values (#{id,jdbcType=BIGINT}, #{district_code,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{private_min_price,jdbcType=VARCHAR}, #{public_min_price,jdbcType=VARCHAR}, #{support_select_ins_no,jdbcType=BIT},
      #{ins_no,jdbcType=VARCHAR}, #{ins_no_list,jdbcType=LONGVARCHAR}, #{account,jdbcType=VARCHAR}, #{is_auto_change,jdbcType=BIT},
      #{delay_day,jdbcType=INTEGER}, #{ctime,jdbcType=BIGINT}, #{mtime,jdbcType=BIGINT}, 
      #{deleted,jdbcType=BIT}, #{version,jdbcType=BIGINT}, #{micro_info,jdbcType=BIT}, 
      #{black_mcc,jdbcType=VARCHAR}, #{filter_rules,jdbcType=LONGVARCHAR},
      #{apply_unionpay,jdbcType=BIT}, #{apply_decp,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfig" keyProperty="id" useGeneratedKeys="true">
    insert into ccb_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="district_code != null" >
        district_code,
      </if>
      <if test="province != null" >
        province,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="private_min_price != null" >
        private_min_price,
      </if>
      <if test="public_min_price != null" >
        public_min_price,
      </if>
      <if test="support_select_ins_no != null" >
        support_select_ins_no,
      </if>
      <if test="ins_no != null" >
        ins_no,
      </if>
      <if test="ins_no_list != null" >
        ins_no_list,
      </if>
      <if test="account != null" >
        account,
      </if>
      <if test="is_auto_change != null" >
        is_auto_change,
      </if>
      <if test="delay_day != null" >
        delay_day,
      </if>
      <if test="ctime != null" >
        ctime,
      </if>
      <if test="mtime != null" >
        mtime,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="micro_info != null" >
        micro_info,
      </if>
      <if test="black_mcc != null" >
        black_mcc,
      </if>
      <if test="filter_rules != null" >
        filter_rules,
      </if>
      <if test="apply_unionpay != null" >
        apply_unionpay,
      </if>
      <if test="apply_decp != null" >
        apply_decp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="district_code != null" >
        #{district_code,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="private_min_price != null" >
        #{private_min_price,jdbcType=VARCHAR},
      </if>
      <if test="public_min_price != null" >
        #{public_min_price,jdbcType=VARCHAR},
      </if>
      <if test="support_select_ins_no != null" >
        #{support_select_ins_no,jdbcType=BIT},
      </if>
      <if test="ins_no != null" >
        #{ins_no,jdbcType=VARCHAR},
      </if>
      <if test="ins_no_list != null" >
        #{ins_no_list,jdbcType=LONGVARCHAR},
      </if>
      <if test="account != null" >
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="is_auto_change != null" >
        #{is_auto_change,jdbcType=BIT},
      </if>
      <if test="delay_day != null" >
        #{delay_day,jdbcType=INTEGER},
      </if>
      <if test="ctime != null" >
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        #{mtime,jdbcType=BIGINT},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=BIT},
      </if>
      <if test="version != null" >
        #{version,jdbcType=BIGINT},
      </if>
      <if test="micro_info != null" >
        #{micro_info,jdbcType=BIT},
      </if>
      <if test="black_mcc != null" >
        #{black_mcc,jdbcType=VARCHAR},
      </if>
      <if test="filter_rules != null" >
        #{filter_rules,jdbcType=LONGVARCHAR},
      </if>
      <if test="apply_unionpay != null" >
        #{apply_unionpay,jdbcType=BIT},
      </if>
      <if test="apply_decp != null" >
        #{apply_decp,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfig" >
    update ccb_config
    <set >
      <if test="district_code != null" >
        district_code = #{district_code,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="private_min_price != null" >
        private_min_price = #{private_min_price,jdbcType=VARCHAR},
      </if>
      <if test="public_min_price != null" >
        public_min_price = #{public_min_price,jdbcType=VARCHAR},
      </if>
      <if test="support_select_ins_no != null" >
        support_select_ins_no = #{support_select_ins_no,jdbcType=BIT},
      </if>
      <if test="ins_no != null" >
        ins_no = #{ins_no,jdbcType=VARCHAR},
      </if>
      <if test="ins_no_list != null" >
        ins_no_list = #{ins_no_list,jdbcType=LONGVARCHAR},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="is_auto_change != null" >
        is_auto_change = #{is_auto_change,jdbcType=BIT},
      </if>
      <if test="delay_day != null" >
        delay_day = #{delay_day,jdbcType=INTEGER},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        mtime = #{mtime,jdbcType=BIGINT},
      </if>
      <if test="deleted != null" >
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="micro_info != null" >
        micro_info = #{micro_info,jdbcType=BIT},
      </if>
      <if test="black_mcc != null" >
        black_mcc = #{black_mcc,jdbcType=VARCHAR},
      </if>
      <if test="filter_rules != null" >
        filter_rules = #{filter_rules,jdbcType=LONGVARCHAR},
      </if>
      <if test="apply_unionpay != null" >
        apply_unionpay = #{apply_unionpay,jdbcType=BIT},
      </if>
      <if test="apply_decp != null" >
        apply_decp = #{apply_decp,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfig" >
    update ccb_config
    set district_code = #{district_code,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      private_min_price = #{private_min_price,jdbcType=VARCHAR},
      public_min_price = #{public_min_price,jdbcType=VARCHAR},
      support_select_ins_no = #{support_select_ins_no,jdbcType=BIT},
      ins_no = #{ins_no,jdbcType=VARCHAR},
      ins_no_list = #{ins_no,jdbcType=LONGVARCHAR},
      account = #{account,jdbcType=VARCHAR},
      is_auto_change = #{is_auto_change,jdbcType=BIT},
      delay_day = #{delay_day,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=BIT},
      version = #{version,jdbcType=BIGINT},
      micro_info = #{micro_info,jdbcType=BIT},
      black_mcc = #{black_mcc,jdbcType=VARCHAR},
      filter_rules = #{filter_rules,jdbcType=LONGVARCHAR},
      apply_unionpay = #{apply_unionpay,jdbcType=BIT},
      apply_decp = #{apply_decp,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfig" >
    update ccb_config
    set district_code = #{district_code,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      private_min_price = #{private_min_price,jdbcType=VARCHAR},
      public_min_price = #{public_min_price,jdbcType=VARCHAR},
      support_select_ins_no = #{support_select_ins_no,jdbcType=BIT},
      ins_no = #{ins_no,jdbcType=VARCHAR},
      account = #{account,jdbcType=VARCHAR},
      is_auto_change = #{is_auto_change,jdbcType=BIT},
      delay_day = #{delay_day,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=BIT},
      version = #{version,jdbcType=BIGINT},
      micro_info = #{micro_info,jdbcType=BIT},
      black_mcc = #{black_mcc,jdbcType=VARCHAR},
      apply_unionpay = #{apply_unionpay,jdbcType=BIT},
      apply_decp = #{apply_decp,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultType="com.wosai.upay.job.model.ccbConfig.CcbConfig">
    select * from ccb_config where deleted = 0
  </select>
</mapper>