<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ContractEventMapper">

    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.ContractEvent" parameterType="java.lang.Long">
    select 
    *
    from contract_event
    where id = #{id,jdbcType=BIGINT}
  </select>


    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.ContractEvent" useGeneratedKeys="true"
            keyProperty="id">
        insert into contract_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="task_id != null">
                task_id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="event_type != null">
                event_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="event_msg != null">
                event_msg,
            </if>
            <if test="result != null">
                result,
            </if>
            <if test="rule_group_id != null">
                rule_group_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="task_id != null">
                #{task_id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="event_type != null">
                #{event_type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="event_msg != null">
                #{event_msg,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="rule_group_id != null">
                #{rule_group_id,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.ContractEvent">
        update contract_event
        <set>
            <if test="task_id != null">
                task_id = #{task_id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="event_type != null">
                event_type = #{event_type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <!--<if test="create_at != null">-->
            <!--create_at = #{create_at,jdbcType=TIMESTAMP},-->
            <!--</if>-->
            <!--<if test="update_at != null">-->
            <!--update_at = #{update_at,jdbcType=TIMESTAMP},-->
            <!--</if>-->
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="event_msg != null">
                event_msg = #{event_msg,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                result = #{result,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectForCreateTasks" resultType="com.wosai.upay.job.model.ContractEvent">
    select * from contract_event
    where
    status=0
    and event_type not in (3,4)
    and create_at > #{param1,jdbcType=TIMESTAMP}
    LIMIT ${param2}
  </select>


    <select id="selectSelfHelpNetInEventBymerchantSnAndtaskId" resultType="com.wosai.upay.job.model.ContractEvent">
        select * from contract_event
        <where>
            <if test="merchant_sn!=null and merchant_sn!=''">
                merchant_sn =#{merchant_sn}
            </if>
            <if test="task_id!=null and task_id!=''">
                AND task_id =#{task_id}
            </if>
        </where>
    </select>

    <select id="selectByTaskId" resultType="com.wosai.upay.job.model.ContractEvent">
        select * from contract_event
        where task_id=#{param1}
    </select>

    <select id="selectByMerchantSnRejectContract" resultType="com.wosai.upay.job.model.ContractEvent">
        select * from contract_event
        where merchant_sn=#{param1}
        and task_id is null
        and event_type in(4,9)
        and status=0 limit 1
    </select>

    <select id="selectEventTodoByMerchantSn" resultType="com.wosai.upay.job.model.ContractEvent">
        select * from contract_event
        where merchant_sn=#{param1}
        and status in (0, 1) limit 1
    </select>

    <select id="selectByMerchantSnPendingEventRejectContract" resultType="com.wosai.upay.job.model.ContractEvent">
        select * from contract_event
        where merchant_sn=#{param1}
        and task_id is null
        and status=0
    </select>

    <select id="selectEventCount" resultType="int">
        select count(1) from contract_event
        where status=0
        and    create_at > #{param1,jdbcType=TIMESTAMP}
        and create_at <![CDATA[<]]> #{param2,jdbcType=TIMESTAMP}
    </select>

    <select id="selectForBlocking" resultType="com.wosai.upay.job.model.ContractEvent">
        select * from contract_event
        where
        status=0
        and event_type!=3
        and create_at between  #{param1,jdbcType=TIMESTAMP} and #{param2,jdbcType=TIMESTAMP}
        LIMIT ${param3}
    </select>

    <update id="updateCreateAt">
       update contract_event set create_at=now(),result=#{param2} where id = #{param1} and status =0
   </update>

    <select id="selectNetInEvent" resultType="com.wosai.upay.job.model.ContractEvent">
        select * from contract_event
        where
        status = 0
        and event_type = 4
        and create_at > #{start}
          LIMIT #{queryLimit}
    </select>
    <select id="selectEventTodoByMerchantSnAndType" resultType="com.wosai.upay.job.model.ContractEvent">
        select * from contract_event
        where merchant_sn=#{param1} and event_type = #{param2}
          and status in (0, 1) limit 1
    </select>
</mapper>