<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.PsbcTerminalMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.PsbcTerminal" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="qr_code_id" property="qr_code_id" jdbcType="VARCHAR" />
    <result column="qr_code" property="qr_code" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="store_sn" property="store_sn" jdbcType="VARCHAR" />
    <result column="ctime" property="ctime" jdbcType="TIMESTAMP" />
    <result column="mtime" property="mtime" jdbcType="TIMESTAMP" />
    <result column="deleted" property="deleted" jdbcType="BIT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, qr_code_id, qr_code, status, merchant_sn, store_sn, ctime, mtime, deleted
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from psbc_terminal
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from psbc_terminal
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.PsbcTerminal" >
    insert into psbc_terminal (id, qr_code_id, qr_code, 
      status, merchant_sn, store_sn, 
      ctime, mtime, deleted
      )
    values (#{id,jdbcType=BIGINT}, #{qr_code_id,jdbcType=VARCHAR}, #{qr_code,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{merchant_sn,jdbcType=VARCHAR}, #{store_sn,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.PsbcTerminal" >
    insert into psbc_terminal
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="qr_code_id != null" >
        qr_code_id,
      </if>
      <if test="qr_code != null" >
        qr_code,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="store_sn != null" >
        store_sn,
      </if>
      <if test="ctime != null" >
        ctime,
      </if>
      <if test="mtime != null" >
        mtime,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="qr_code_id != null" >
        #{qr_code_id,jdbcType=VARCHAR},
      </if>
      <if test="qr_code != null" >
        #{qr_code,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="store_sn != null" >
        #{store_sn,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null" >
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.PsbcTerminal" >
    update psbc_terminal
    <set >
      <if test="qr_code_id != null" >
        qr_code_id = #{qr_code_id,jdbcType=VARCHAR},
      </if>
      <if test="qr_code != null" >
        qr_code = #{qr_code,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="store_sn != null" >
        store_sn = #{store_sn,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null" >
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null" >
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.PsbcTerminal" >
    update psbc_terminal
    set qr_code_id = #{qr_code_id,jdbcType=VARCHAR},
      qr_code = #{qr_code,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      store_sn = #{store_sn,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>