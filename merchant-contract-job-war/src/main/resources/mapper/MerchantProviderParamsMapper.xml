<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.MerchantProviderParamsMapper">
    <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.MerchantProviderParams">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR"/>
        <result column="out_merchant_sn" property="out_merchant_sn" jdbcType="VARCHAR"/>
        <result column="merchant_name" property="merchant_name" jdbcType="VARCHAR"/>
        <result column="channel_no" property="channel_no" jdbcType="VARCHAR"/>
        <result column="parent_merchant_id" property="parent_merchant_id" jdbcType="VARCHAR"/>
        <result column="provider" property="provider" jdbcType="INTEGER"/>
        <result column="provider_merchant_id" property="provider_merchant_id" jdbcType="VARCHAR"/>
        <result column="payway" property="payway" jdbcType="INTEGER"/>
        <result column="params_config_status" property="params_config_status" jdbcType="INTEGER"/>
        <result column="pay_merchant_id" property="pay_merchant_id" jdbcType="VARCHAR"/>
        <result column="weixin_sub_appid" property="weixin_sub_appid" jdbcType="VARCHAR"/>
        <result column="weixin_subscribe_appid" property="weixin_subscribe_appid" jdbcType="VARCHAR"/>
        <result column="weixin_sub_mini_appid" property="weixin_sub_mini_appid" jdbcType="VARCHAR"/>
        <result column="weixin_receipt_appid" property="weixin_receipt_appid" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="disable_status" property="disable_status" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="BIGINT"/>
        <result column="mtime" property="mtime" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="BIT"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="contract_rule" property="contract_rule" jdbcType="VARCHAR"/>
        <result column="rule_group_id" property="rule_group_id" jdbcType="VARCHAR"/>
        <result column="update_status" property="update_status" jdbcType="INTEGER"/>
        <result column="auth_status" property="auth_status" jdbcType="INTEGER"/>
        <result column="merchant_state" property="merchant_state" jdbcType="INTEGER"/>
        <result column="ali_mcc" property="ali_mcc" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.MerchantProviderParams"
               extends="BaseResultMap">
        <result column="extra" property="extra" jdbcType="LONGVARBINARY"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, merchant_sn, out_merchant_sn, merchant_name, channel_no, parent_merchant_id, provider, provider_merchant_id,
    payway, params_config_status, pay_merchant_id, weixin_sub_appid, weixin_subscribe_appid, 
    weixin_sub_mini_appid, weixin_receipt_appid, status, disable_status, ctime, mtime, deleted, version,
    contract_rule, rule_group_id, update_status,auth_status,wx_settlement_id,wx_use_type,merchant_state,ali_mcc
  </sql>
    <sql id="Blob_Column_List">
    extra
  </sql>
    <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs"
            parameterType="com.wosai.upay.job.model.DO.MerchantProviderParamsExample">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from merchant_provider_params
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.wosai.upay.job.model.DO.MerchantProviderParamsExample">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from merchant_provider_params
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExampleCustom" resultType="com.wosai.upay.job.model.DO.MerchantProviderParamsCustom">
        SELECT t0.id,
        t0.merchant_sn,
        t0.out_merchant_sn,
        t0.merchant_name,
        t0.channel_no ,
        t0.parent_merchant_id,
        t0.provider,
        t0.provider_merchant_id,
        t0.payway,
        t0.params_config_status,
        t0.pay_merchant_id,
        t0.weixin_sub_appid,
        t0.weixin_subscribe_appid,
        t0.weixin_sub_mini_appid,
        t0.weixin_receipt_appid,
        t0.status,
        t0.extra,
        t0.ctime,
        t0.mtime,
        t0.deleted,
        t0.version,
        t0.contract_rule,
        t0.rule_group_id,
        t0.update_status,
        t0.auth_status,
        t0.wx_settlement_id,
        t0.wx_use_type,
        t0.disable_status,
        t1.name as 'provider_name',
        t3.channel,
        t3.name as 'channel_name',
        t4.clear_type,
        t4.acquirer,
        t4.name as 'acquirer_name'
        FROM merchant_provider_params t0
        LEFT JOIN mc_provider t1 ON t0.provider = t1.provider
        LEFT JOIN mc_contract_rule t2 ON t0.contract_rule = t2.rule
        LEFT JOIN mc_channel t3 ON t2.channel = t3.channel
        LEFT JOIN mc_acquirer t4 ON t2.acquirer = t4.acquirer
        <where>
            t0.payway != 0
            <if test="params.merchant_sn != null">
                AND t0.merchant_sn = #{params.merchant_sn}
            </if>
            <if test="params.payway != null">
                AND t0.payway = #{params.payway}
            </if>
            <if test="params.pay_merchant_id != null">
                AND t0.pay_merchant_id = #{params.pay_merchant_id}
            </if>
            <if test="params.provider_merchant_id != null">
                AND t0.provider_merchant_id = #{params.provider_merchant_id}
            </if>
            <if test="params.provider != null">
                AND t0.provider = #{params.provider}
            </if>
            <if test="params.acquirer != null">
                AND t4.acquirer = #{params.acquirer}
            </if>
            <if test="params.deleted != null">
                AND t0.deleted = #{params.deleted}
            </if>
            <if test="params.channel_name != null">
                AND t3.name LIKE CONCAT('%', #{params.channel_name}, '%')
            </if>
            <if test="params.status != null">
                AND t0.status = #{params.status}
            </if>
        </where>

    </select>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from merchant_provider_params
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectByPrimaryKeyCustom" resultType="com.wosai.upay.job.model.DO.MerchantProviderParamsCustom">
        SELECT t0.id,
        t0.merchant_sn,
        t0.out_merchant_sn,
        t0.channel_no,
        t0.parent_merchant_id,
        t0.provider,
        t0.provider_merchant_id,
        t0.payway,
        t0.params_config_status,
        t0.pay_merchant_id,
        t0.weixin_sub_appid,
        t0.weixin_subscribe_appid,
        t0.weixin_sub_mini_appid,
        t0.weixin_receipt_appid,
        t0.status,
        t0.extra,
        t0.ctime,
        t0.mtime,
        t0.deleted,
        t0.version,
        t0.contract_rule,
        t0.rule_group_id,
        t0.update_status,
        t0.auth_status,
        t0.wx_settlement_id,
        t0.wx_use_type,
        t1.name as 'provider_name',
        t3.channel,
        t3.name as 'channel_name',
        t4.clear_type,
        t4.acquirer,
        t4.name as 'acquirer_name'
        FROM merchant_provider_params t0
        LEFT JOIN mc_provider t1 ON t0.provider = t1.provider
        LEFT JOIN mc_contract_rule t2 ON t0.contract_rule = t2.rule
        LEFT JOIN mc_channel t3 ON t2.channel = t3.channel
        LEFT JOIN mc_acquirer t4 ON t2.acquirer = t4.acquirer
        WHERE t0.id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from merchant_provider_params
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <delete id="deleteByExample" parameterType="com.wosai.upay.job.model.DO.MerchantProviderParamsExample">
        delete from merchant_provider_params
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.wosai.upay.job.model.DO.MerchantProviderParams">
    insert into merchant_provider_params (id, merchant_sn, out_merchant_sn,
      channel_no, parent_merchant_id, provider,
      provider_merchant_id, payway, params_config_status,
      pay_merchant_id, weixin_sub_appid, weixin_subscribe_appid,
      weixin_sub_mini_appid, weixin_receipt_appid,
      status, ctime, mtime,
      deleted, version, contract_rule,
      rule_group_id, update_status, extra
      )
    values (#{id,jdbcType=VARCHAR}, #{merchant_sn,jdbcType=VARCHAR}, #{out_merchant_sn,jdbcType=VARCHAR},
      #{channel_no,jdbcType=VARCHAR}, #{parent_merchant_id,jdbcType=VARCHAR}, #{provider,jdbcType=INTEGER},
      #{provider_merchant_id,jdbcType=VARCHAR}, #{payway,jdbcType=INTEGER}, #{params_config_status,jdbcType=INTEGER},
      #{pay_merchant_id,jdbcType=VARCHAR}, #{weixin_sub_appid,jdbcType=VARCHAR}, #{weixin_subscribe_appid,jdbcType=VARCHAR},
      #{weixin_sub_mini_appid,jdbcType=VARCHAR}, #{weixin_receipt_appid,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{ctime,jdbcType=BIGINT}, #{mtime,jdbcType=BIGINT},
      #{deleted,jdbcType=BIT}, #{version,jdbcType=BIGINT}, #{contract_rule,jdbcType=VARCHAR},
      #{rule_group_id,jdbcType=VARCHAR}, #{update_status,jdbcType=INTEGER}, #{extra,jdbcType=LONGVARBINARY}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.MerchantProviderParams">
        insert into merchant_provider_params
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="out_merchant_sn != null">
                out_merchant_sn,
            </if>
            <if test="channel_no != null">
                channel_no,
            </if>
            <if test="parent_merchant_id != null">
                parent_merchant_id,
            </if>
            <if test="provider != null">
                provider,
            </if>
            <if test="provider_merchant_id != null">
                provider_merchant_id,
            </if>
            <if test="payway != null">
                payway,
            </if>
            <if test="params_config_status != null">
                params_config_status,
            </if>
            <if test="pay_merchant_id != null">
                pay_merchant_id,
            </if>
            <if test="weixin_sub_appid != null">
                weixin_sub_appid,
            </if>
            <if test="weixin_subscribe_appid != null">
                weixin_subscribe_appid,
            </if>
            <if test="weixin_sub_mini_appid != null">
                weixin_sub_mini_appid,
            </if>
            <if test="weixin_receipt_appid != null">
                weixin_receipt_appid,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
            <if test="mtime != null">
                mtime,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="contract_rule != null">
                contract_rule,
            </if>
            <if test="rule_group_id != null">
                rule_group_id,
            </if>
            <if test="update_status != null">
                update_status,
            </if>
            <if test="gold_status != null">
                gold_status,
            </if>
            <if test="merchant_state != null">
                merchant_state,
            </if>
            <if test="extra != null">
                extra,
            </if>
            <if test="merchant_name != null">
                merchant_name,
            </if>
            <if test="wx_settlement_id != null">
                wx_settlement_id,
            </if>
            <if test="ali_mcc != null">
                ali_mcc,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="out_merchant_sn != null">
                #{out_merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="channel_no != null">
                #{channel_no,jdbcType=VARCHAR},
            </if>
            <if test="parent_merchant_id != null">
                #{parent_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="provider != null">
                #{provider,jdbcType=INTEGER},
            </if>
            <if test="provider_merchant_id != null">
                #{provider_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="payway != null">
                #{payway,jdbcType=INTEGER},
            </if>
            <if test="params_config_status != null">
                #{params_config_status,jdbcType=INTEGER},
            </if>
            <if test="pay_merchant_id != null">
                #{pay_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="weixin_sub_appid != null">
                #{weixin_sub_appid,jdbcType=VARCHAR},
            </if>
            <if test="weixin_subscribe_appid != null">
                #{weixin_subscribe_appid,jdbcType=VARCHAR},
            </if>
            <if test="weixin_sub_mini_appid != null">
                #{weixin_sub_mini_appid,jdbcType=VARCHAR},
            </if>
            <if test="weixin_receipt_appid != null">
                #{weixin_receipt_appid,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="ctime != null">
                #{ctime,jdbcType=BIGINT},
            </if>
            <if test="mtime != null">
                #{mtime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="contract_rule != null">
                #{contract_rule,jdbcType=VARCHAR},
            </if>
            <if test="rule_group_id != null">
                #{rule_group_id,jdbcType=VARCHAR},
            </if>
            <if test="update_status != null">
                #{update_status,jdbcType=INTEGER},
            </if>
            <if test="gold_status != null">
                #{gold_status,jdbcType=INTEGER},
            </if>
            <if test="merchant_state != null">
                #{merchant_state,jdbcType=INTEGER},
            </if>
            <if test="extra != null">
                #{extra,jdbcType=LONGVARBINARY},
            </if>
            <if test="merchant_name != null">
                #{merchant_name,jdbcType=VARCHAR},
            </if>
            <if test="wx_settlement_id != null">
                #{wx_settlement_id,jdbcType=VARCHAR},
            </if>
            <if test="ali_mcc != null">
                #{ali_mcc,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.wosai.upay.job.model.DO.MerchantProviderParamsExample"
            resultType="java.lang.Integer">
        select count(*) from merchant_provider_params
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update merchant_provider_params
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=VARCHAR},
            </if>
            <if test="record.merchant_sn != null">
                merchant_sn = #{record.merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="record.out_merchant_sn != null">
                out_merchant_sn = #{record.out_merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="record.channel_no != null">
                channel_no = #{record.channel_no,jdbcType=VARCHAR},
            </if>
            <if test="record.parent_merchant_id != null">
                parent_merchant_id = #{record.parent_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="record.provider != null">
                provider = #{record.provider,jdbcType=INTEGER},
            </if>
            <if test="record.provider_merchant_id != null">
                provider_merchant_id = #{record.provider_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="record.payway != null">
                payway = #{record.payway,jdbcType=INTEGER},
            </if>
            <if test="record.params_config_status != null">
                params_config_status = #{record.params_config_status,jdbcType=INTEGER},
            </if>
            <if test="record.pay_merchant_id != null">
                pay_merchant_id = #{record.pay_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="record.weixin_sub_appid != null">
                weixin_sub_appid = #{record.weixin_sub_appid,jdbcType=VARCHAR},
            </if>
            <if test="record.weixin_subscribe_appid != null">
                weixin_subscribe_appid = #{record.weixin_subscribe_appid,jdbcType=VARCHAR},
            </if>
            <if test="record.weixin_sub_mini_appid != null">
                weixin_sub_mini_appid = #{record.weixin_sub_mini_appid,jdbcType=VARCHAR},
            </if>
            <if test="record.weixin_receipt_appid != null">
                weixin_receipt_appid = #{record.weixin_receipt_appid,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.ctime != null">
                ctime = #{record.ctime,jdbcType=BIGINT},
            </if>
            <if test="record.mtime != null">
                mtime = #{record.mtime,jdbcType=BIGINT},
            </if>
            <if test="record.deleted != null">
                deleted = #{record.deleted,jdbcType=BIT},
            </if>
            <if test="record.version != null">
                version = #{record.version,jdbcType=BIGINT},
            </if>
            <if test="record.contract_rule != null">
                contract_rule = #{record.contract_rule,jdbcType=VARCHAR},
            </if>
            <if test="record.rule_group_id != null">
                rule_group_id = #{record.rule_group_id,jdbcType=VARCHAR},
            </if>
            <if test="record.update_status != null">
                update_status = #{record.update_status,jdbcType=INTEGER},
            </if>
            <if test="record.extra != null">
                extra = #{record.extra,jdbcType=LONGVARBINARY},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.MerchantProviderParams">
        update merchant_provider_params
        <set>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="out_merchant_sn != null">
                out_merchant_sn = #{out_merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="merchant_name != null">
                merchant_name = #{merchant_name,jdbcType=VARCHAR},
            </if>
            <if test="channel_no != null">
                channel_no = #{channel_no,jdbcType=VARCHAR},
            </if>
            <if test="parent_merchant_id != null">
                parent_merchant_id = #{parent_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="provider != null">
                provider = #{provider,jdbcType=INTEGER},
            </if>
            <if test="provider_merchant_id != null">
                provider_merchant_id = #{provider_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="payway != null">
                payway = #{payway,jdbcType=INTEGER},
            </if>
            <if test="params_config_status != null">
                params_config_status = #{params_config_status,jdbcType=INTEGER},
            </if>
            <if test="pay_merchant_id != null">
                pay_merchant_id = #{pay_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="weixin_sub_appid != null">
                weixin_sub_appid = #{weixin_sub_appid,jdbcType=VARCHAR},
            </if>
            <if test="weixin_subscribe_appid != null">
                weixin_subscribe_appid = #{weixin_subscribe_appid,jdbcType=VARCHAR},
            </if>
            <if test="weixin_sub_mini_appid != null">
                weixin_sub_mini_appid = #{weixin_sub_mini_appid,jdbcType=VARCHAR},
            </if>
            <if test="weixin_receipt_appid != null">
                weixin_receipt_appid = #{weixin_receipt_appid,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="ctime != null">
                ctime = #{ctime,jdbcType=BIGINT},
            </if>
            <if test="mtime != null">
                mtime = #{mtime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="contract_rule != null">
                contract_rule = #{contract_rule,jdbcType=VARCHAR},
            </if>
            <if test="rule_group_id != null">
                rule_group_id = #{rule_group_id,jdbcType=VARCHAR},
            </if>
            <if test="update_status != null">
                update_status = #{update_status,jdbcType=INTEGER},
            </if>
            <if test="extra != null">
                extra = #{extra,jdbcType=LONGVARBINARY},
            </if>
            <if test="auth_status != null">
                auth_status = #{auth_status,jdbcType=INTEGER},
            </if>
            <if test="gold_status != null">
                gold_status = #{gold_status,jdbcType=INTEGER},
            </if>
            <if test="wx_settlement_id != null">
                wx_settlement_id = #{wx_settlement_id},
            </if>
            <if test="wx_use_type != null">
                wx_use_type = #{wx_use_type},
            </if>
            <if test="ali_mcc != null">
                ali_mcc = #{ali_mcc,jdbcType=VARCHAR},
            </if>
            <if test="merchant_state != null">
                merchant_state = #{merchant_state},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateStatusByKey">
    update merchant_provider_params set status=#{status},mtime=#{mtime}
    where id=#{id}
  </update>

    <select id="getUseWeiXinParam" parameterType="java.lang.String"
            resultType="com.wosai.upay.job.model.DO.MerchantProviderParams">
        select * from merchant_provider_params where merchant_sn=#{merchantSn} and  payway=3 and status=1 and deleted=0 limit 1
    </select>
    <select id="getWeixinParamByChannelNo" resultType="com.wosai.upay.job.model.DO.MerchantProviderParams">
        select * from merchant_provider_params where merchant_sn=#{merchantSn} and payway=3 and channel_no=#{channelNo} order by ctime desc limit 1
    </select>
    <select id="getByPayMerchantId" resultType="com.wosai.upay.job.model.DO.MerchantProviderParams">
        select  * from merchant_provider_params where pay_merchant_id=#{payMerchantID} and deleted=0 limit 1;
    </select>

    <update id="updateWxUseTypeById">
        update merchant_provider_params set wx_use_type=#{wxUseType}
        where id=#{id}
    </update>

    <update id="updateParamsConfigStatusById">
        update merchant_provider_params set status=#{status},mtime=#{mtime},params_config_status=#{params_config_status}
        where id=#{id}
    </update>
</mapper>