<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ContractReconsiderTaskMapper">

    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.ContractReconsiderTask"
            parameterType="java.lang.Long">
    select *
    from contract_reconsider_task
    where id = #{id,jdbcType=BIGINT}
  </select>

    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.ContractReconsiderTask" useGeneratedKeys="true"
            keyProperty="id">
        insert into contract_reconsider_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="merchant_name != null">
                merchant_name,
            </if>
            <if test="sub_task_id != null">
                sub_task_id,
            </if>
            <if test="p_task_id != null">
                p_task_id,
            </if>
            <if test="contract_id != null">
                contract_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="event_context != null">
                event_context,
            </if>
            <if test="result != null">
                result,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="merchant_name != null">
                #{merchant_name,jdbcType=VARCHAR},
            </if>
            <if test="sub_task_id != null">
                #{sub_task_id,jdbcType=BIGINT},
            </if>
            <if test="p_task_id != null">
                #{p_task_id,jdbcType=BIGINT},
            </if>
            <if test="contract_id != null">
                #{contract_id,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="event_context != null">
                #{event_context,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                #{result,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.ContractReconsiderTask">
        update contract_reconsider_task
        <set>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="merchant_name != null">
                merchant_name = #{merchant_name,jdbcType=VARCHAR},
            </if>
            <if test="sub_task_id != null">
                sub_task_id = #{sub_task_id,jdbcType=BIGINT},
            </if>
            <if test="p_task_id != null">
                p_task_id = #{p_task_id,jdbcType=BIGINT},
            </if>
            <if test="contract_id != null">
                contract_id = #{contract_id,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="event_context != null">
                event_context = #{event_context,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                result = #{result,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByContractIdAndStatus" resultType="com.wosai.upay.job.model.ContractReconsiderTask">
            select *
    from contract_reconsider_task
    where contract_id = #{contractId}
    and status=#{status}
    </select>
</mapper>