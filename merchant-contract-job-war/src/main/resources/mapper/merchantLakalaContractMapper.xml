<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.MerchantLakalaContractMapper">
    <select id="selectByParams" resultType="com.wosai.upay.job.model.MerchantLakalaContract">
        select ctime,mtime,extra ,callback_status from merchant_lakala_contract
        <where>
            <if test="merchant_sn!=null and merchant_sn!=''">
                merchant_sn=#{merchant_sn}
            </if>
            <if test="opt_type!=null ">
             AND   opt_type  =#{opt_type}
            </if>
            <if test="contract_id!=null and contract_id!=''">
                AND  contract_id  =#{contract_id}
            </if>
            ORDER  BY MTIME  DESC 
        </where>
    </select>
</mapper>