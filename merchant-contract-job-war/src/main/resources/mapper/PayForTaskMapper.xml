<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.PayForTaskMapper">

    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.PayForTask" parameterType="java.lang.Long">
        select *
        from pay_for_task
        where id = #{id,jdbcType=BIGINT}
    </select>


    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.PayForTask" useGeneratedKeys="true"
            keyProperty="id">
        insert into pay_for_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="sub_task_id != null">
                sub_task_id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="hash_req != null">
                hash_req,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="submit_remit_order_id != null">
                submit_remit_order_id,
            </if>
            <if test="request_flow_no != null">
                request_flow_no,
            </if>
            <if test="context_param != null">
                context_param,
            </if>
            <if test="request_param != null">
                request_param,
            </if>
            <if test="response != null">
                response,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="sub_task_id != null">
                #{sub_task_id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="hash_req != null">
                #{hash_req,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="submit_remit_order_id != null">
                #{submit_remit_order_id,jdbcType=VARCHAR},
            </if>
            <if test="request_flow_no != null">
                #{request_flow_no,jdbcType=VARCHAR},
            </if>
            <if test="context_param != null">
                #{context_param,jdbcType=LONGVARCHAR},
            </if>
            <if test="request_param != null">
                #{request_param,jdbcType=LONGVARCHAR},
            </if>
            <if test="response != null">
                #{response,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.PayForTask">
        update pay_for_task
        <set>
            <if test="sub_task_id != null">
                sub_task_id = #{sub_task_id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="hash_req != null">
                hash_req = #{hash_req,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <!--<if test="update_at != null">-->
            <!--update_at = #{update_at,jdbcType=TIMESTAMP},-->
            <!--</if>-->
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="submit_remit_order_id != null">
                submit_remit_order_id = #{submit_remit_order_id,jdbcType=VARCHAR},
            </if>
            <if test="request_flow_no != null">
                request_flow_no = #{request_flow_no,jdbcType=VARCHAR},
            </if>
            <if test="context_param != null">
                context_param = #{context_param,jdbcType=LONGVARCHAR},
            </if>
            <if test="request_param != null">
                request_param = #{request_param,jdbcType=LONGVARCHAR},
            </if>
            <if test="response != null">
                response = #{response,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectBySnAndHashReq" resultType="com.wosai.upay.job.model.PayForTask">
        select  * from pay_for_task
        where merchant_sn=#{param1}
        and hash_req=#{param2} limit 1
    </select>

    <select id="selectByCreate" resultType="com.wosai.upay.job.model.PayForTask">
        select  * from pay_for_task
        where create_at > #{param1} and create_at &lt; #{param2}
        and status=0
        order by create_at desc LIMIT ${param3}
    </select>

    <select id="selectBySubmitRemitOrderId" resultType="com.wosai.upay.job.model.PayForTask">
        select * from pay_for_task
        where submit_remit_order_id=#{param1} and status=1 limit 1
    </select>

    <select id="selectByStatus" resultType="com.wosai.upay.job.model.PayForTask">
          select * from pay_for_task
        where status=#{param1}
        and submit_remit_order_id is not null
    </select>

    <select id="selectByRequestFlowNo" resultType="com.wosai.upay.job.model.PayForTask">
                select * from pay_for_task
        where request_flow_no=#{param1}
        and status in(1,4)
    </select>

    <select id="selectToSendMessage" resultType="com.wosai.upay.job.model.PayForTask">
        select * from pay_for_task
        where create_at >=#{param1}
      and create_at &lt;=#{param2}
        and status=1
        and submit_remit_order_id is not null
    </select>
    <select id="selectBySubTaskId" resultType="com.wosai.upay.job.model.PayForTask">
        select  * from pay_for_task
        where sub_task_id=#{param1}
    </select>
</mapper>