<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.IndustryCodeV2Mapper" >
    <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.IndustryCodeV2" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="industry_id" property="" jdbcType="VARCHAR" />
        <result column="union_code_weixin_private" property="union_code_weixin_private" jdbcType="VARCHAR" />
        <result column="union_code_weixin_public" property="union_code_weixin_public" jdbcType="VARCHAR" />
        <result column="nucc_code_weixin_private" property="nucc_code_weixin_private" jdbcType="VARCHAR" />
        <result column="nucc_code_weixin_public" property="nucc_code_weixin_public" jdbcType="VARCHAR" />
        <result column="union_code_alipay_private" property="union_code_alipay_private" jdbcType="VARCHAR" />
        <result column="union_code_alipay_public" property="union_code_alipay_public" jdbcType="VARCHAR" />
        <result column="nucc_code_alipay_private" property="nucc_code_alipay_private" jdbcType="VARCHAR" />
        <result column="nucc_code_alipay_public" property="nucc_code_alipay_public" jdbcType="VARCHAR" />
        <result column="nucc_code_bestpay_public" property="nucc_code_bestpay_public" jdbcType="VARCHAR" />
        <result column="nucc_code_bestpay_private" property="nucc_code_bestpay_private" jdbcType="VARCHAR" />
        <result column="nucc_code_bestpay" property="nucc_code_bestpay" jdbcType="VARCHAR" />
        <result column="lakala_code" property="lakala_code" jdbcType="VARCHAR" />
        <result column="tl_code" property="tl_code" jdbcType="VARCHAR" />
        <result column="wm_aly" property="wm_aly" jdbcType="VARCHAR" />
        <result column="wm_weixin_private" property="wm_weixin_private" jdbcType="VARCHAR" />
        <result column="wm_weixin_public" property="wm_weixin_public" jdbcType="VARCHAR" />
        <result column="union_open_code" property="union_open_code" jdbcType="VARCHAR" />
        <result column="direct_connect_weixin_code" property="direct_connect_weixin_code" jdbcType="VARCHAR" />
        <result column="wm_aly_mcc" property="wm_aly_mcc" jdbcType="VARCHAR" />
        <result column="version" property="version" jdbcType="VARCHAR" />
    </resultMap>

</mapper>