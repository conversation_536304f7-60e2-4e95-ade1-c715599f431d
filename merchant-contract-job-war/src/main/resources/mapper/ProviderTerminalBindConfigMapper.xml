<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ProviderTerminalBindConfigMapper">
    <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.ProviderTerminalBindConfig">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR"/>
        <result column="store_sn" property="store_sn" jdbcType="VARCHAR"/>
        <result column="terminal_sn" property="terminal_sn" jdbcType="VARCHAR"/>
        <result column="provider" property="provider" jdbcType="INTEGER"/>
        <result column="payway" property="payway" jdbcType="BIT"/>
        <result column="sub_mch_id" property="sub_mch_id" jdbcType="VARCHAR"/>
        <result column="provider_terminal_id" property="provider_terminal_id" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="BIT"/>
        <result column="result" property="result" jdbcType="VARCHAR"/>
        <result column="create_at" property="create_at" jdbcType="TIMESTAMP"/>
        <result column="update_at" property="update_at" jdbcType="TIMESTAMP"/>
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.ProviderTerminalBindConfig"
               extends="BaseResultMap">
        <result column="request_body" property="request_body" jdbcType="LONGVARCHAR"/>
        <result column="response_body" property="response_body" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <sql id="Blob_Column_List">
        request_body
        , response_body
    </sql>
    <sql id="Base_Column_List">
        id
        , merchant_sn, store_sn, terminal_sn, provider, payway, sub_mch_id, provider_terminal_id, status, result, create_at, update_at, request_body, response_body
    </sql>
    <insert id="insert" parameterType="com.wosai.upay.job.model.ProviderTerminalBindConfig">
        insert into provider_terminal_bind_config (id, merchant_sn, store_sn,
                                                   terminal_sn, provider, payway,
                                                   sub_mch_id, provider_terminal_id, status,
                                                   create_at, update_at, request_body,
                                                   response_body)
        values (#{id,jdbcType=VARCHAR}, #{merchant_sn,jdbcType=VARCHAR}, #{store_sn,jdbcType=VARCHAR},
                #{terminal_sn,jdbcType=VARCHAR}, #{provider,jdbcType=INTEGER}, #{payway,jdbcType=BIT},
                #{sub_mch_id,jdbcType=VARCHAR}, #{provider_terminal_id,jdbcType=VARCHAR}, #{status,jdbcType=BIT},
                #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP}, #{request_body,jdbcType=LONGVARCHAR},
                #{response_body,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.ProviderTerminalBindConfig">
        insert into provider_terminal_bind_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="store_sn != null">
                store_sn,
            </if>
            <if test="terminal_sn != null">
                terminal_sn,
            </if>
            <if test="provider != null">
                provider,
            </if>
            <if test="payway != null">
                payway,
            </if>
            <if test="sub_mch_id != null">
                sub_mch_id,
            </if>
            <if test="provider_terminal_id != null">
                provider_terminal_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="request_body != null">
                request_body,
            </if>
            <if test="response_body != null">
                response_body,
            </if>
            <if test="result != null">
                result
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="store_sn != null">
                #{store_sn,jdbcType=VARCHAR},
            </if>
            <if test="terminal_sn != null">
                #{terminal_sn,jdbcType=VARCHAR},
            </if>
            <if test="provider != null">
                #{provider,jdbcType=INTEGER},
            </if>
            <if test="payway != null">
                #{payway,jdbcType=BIT},
            </if>
            <if test="sub_mch_id != null">
                #{sub_mch_id,jdbcType=VARCHAR},
            </if>
            <if test="provider_terminal_id != null">
                #{provider_terminal_id,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=BIT},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="request_body != null">
                #{request_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="response_body != null">
                #{response_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                #{result,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.ProviderTerminalBindConfig">
        update provider_terminal_bind_config
        <set>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="store_sn != null">
                store_sn = #{store_sn,jdbcType=VARCHAR},
            </if>
            <if test="terminal_sn != null">
                terminal_sn = #{terminal_sn,jdbcType=VARCHAR},
            </if>
            <if test="provider != null">
                provider = #{provider,jdbcType=INTEGER},
            </if>
            <if test="payway != null">
                payway = #{payway,jdbcType=INTEGER},
            </if>
            <if test="sub_mch_id != null">
                sub_mch_id = #{sub_mch_id,jdbcType=VARCHAR},
            </if>
            <if test="provider_terminal_id != null">
                provider_terminal_id = #{provider_terminal_id,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="request_body != null">
                request_body = #{request_body,jdbcType=VARCHAR},
            </if>
            <if test="response_body != null">
                response_body = #{response_body,jdbcType=VARCHAR},
            </if>
            <if test="result != null">
                result = #{result,jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.ProviderTerminalBindConfig">
        select
        <include refid="Base_Column_List"/>
        from provider_terminal_bind_config where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectByExample" resultType="com.wosai.upay.job.model.ProviderTerminalBindConfig">
        select
        <include refid="Base_Column_List"/>
        from provider_terminal_bind_config
        <where>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR}
            </if>
            <if test="store_sn != null">
                and store_sn = #{store_sn,jdbcType=VARCHAR}
            </if>
            <if test="terminal_sn != null">
                and terminal_sn = #{terminal_sn,jdbcType=VARCHAR}
            </if>
            <if test="provider != null">
                and provider = #{provider,jdbcType=INTEGER}
            </if>
            <if test="payway != null">
                and payway = #{payway,jdbcType=INTEGER}
            </if>
            <if test="sub_mch_id != null">
                and sub_mch_id = #{sub_mch_id,jdbcType=VARCHAR}
            </if>
            <if test="provider_terminal_id != null">
                and provider_terminal_id = #{provider_terminal_id,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>
            <if test="sub_mch_ids != null and sub_mch_ids.size() > 0">
                and sub_mch_id in
                <foreach collection="sub_mch_ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>