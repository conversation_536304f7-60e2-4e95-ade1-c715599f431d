<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.job.mapper.MerchantUpgradeTaskMapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.MerchantUpgradeTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_sn" jdbcType="VARCHAR" property="merchant_sn" />
    <result column="merchant_id" jdbcType="VARCHAR" property="merchant_id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="target_acquirer" jdbcType="VARCHAR" property="target_acquirer" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="create_at" jdbcType="TIMESTAMP" property="create_at" />
    <result column="update_at" jdbcType="TIMESTAMP" property="update_at" />
  </resultMap>
  <sql id="Base_Column_List">
    id, merchant_sn, merchant_id, type, target_acquirer, status, message, create_at, update_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_upgrade_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectWaitingTask" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_upgrade_task
    where status not in (5,6)
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_upgrade_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.MerchantUpgradeTask">
    insert into merchant_upgrade_task (id, merchant_sn, merchant_id, type,
      target_acquirer, status, message, 
      create_at, update_at)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{merchant_id,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
      #{target_acquirer,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{message,jdbcType=VARCHAR}, 
      #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.MerchantUpgradeTask">
    insert into merchant_upgrade_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="merchant_sn != null">
        merchant_sn,
      </if>
      <if test="merchant_id != null">
        merchant_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="target_acquirer != null">
        target_acquirer,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="create_at != null">
        create_at,
      </if>
      <if test="update_at != null">
        update_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null">
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="merchant_id != null">
        #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="target_acquirer != null">
        #{target_acquirer,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null">
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null">
        #{update_at,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.MerchantUpgradeTask">
    update merchant_upgrade_task
    <set>
      <if test="merchant_sn != null">
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="merchant_id != null">
        merchant_id = #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="target_acquirer != null">
        target_acquirer = #{target_acquirer,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null">
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null">
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.MerchantUpgradeTask">
    update merchant_upgrade_task
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      merchant_id = #{merchant_id,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      target_acquirer = #{target_acquirer,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>