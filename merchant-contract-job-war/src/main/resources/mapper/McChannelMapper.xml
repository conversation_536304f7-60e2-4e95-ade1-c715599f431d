<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.McChannelMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.McChannel" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="channel" property="channel" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="subject" property="subject" jdbcType="VARCHAR" />
    <result column="payway" property="payway" jdbcType="INTEGER" />
    <result column="payway_channel_no" property="payway_channel_no" jdbcType="VARCHAR" />
    <result column="provider" property="provider" jdbcType="VARCHAR" />
    <result column="acquirer" property="acquirer" jdbcType="VARCHAR" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="channel_no" property="channel_no" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.McChannel" extends="BaseResultMap" >
    <result column="private_key" property="private_key" jdbcType="LONGVARCHAR" />
    <result column="provider_metadata" property="provider_metadata" jdbcType="LONGVARCHAR" />
    <result column="acquirer_metadata" property="acquirer_metadata" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, channel, name, subject, payway, payway_channel_no, provider, acquirer, create_at, 
    update_at, channel_no
  </sql>
  <sql id="Base_Column_List_With_Table_Name" >
    mc_channel.id, mc_channel.channel, mc_channel.name, mc_channel.subject, mc_channel.payway, mc_channel.payway_channel_no,
    mc_channel.provider, mc_channel.acquirer, mc_channel.create_at, mc_channel.update_at, mc_channel.channel_no
  </sql>
  <sql id="Blob_Column_List" >
    private_key, provider_metadata, acquirer_metadata
  </sql>
  <sql id="Blob_Column_List_With_Table_Name" >
    mc_channel.private_key, mc_channel.provider_metadata, mc_channel.acquirer_metadata
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.wosai.upay.job.model.DO.McChannelExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_channel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.wosai.upay.job.model.DO.McChannelExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mc_channel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByMcChannelCustom" resultType="com.wosai.upay.job.model.DO.McChannelCustom">
    SELECT
      <include refid="Base_Column_List_With_Table_Name"/>
      , mc_provider.name as 'provider_name'
    FROM mc_channel
    LEFT JOIN mc_provider ON mc_channel.provider = mc_provider.provider
    <where>
      <if test="params.payway != null">
        AND mc_channel.payway = #{params.payway}
      </if>
      <if test="params.channel != null">
        AND mc_channel.channel = #{params.channel}
      </if>
      <if test="params.provider != null">
        AND mc_channel.provider = #{params.provider}
      </if>
      <if test="params.acquirer != null">
        AND mc_channel.acquirer = #{params.acquirer}
      </if>
      <if test="params.name != null">
        AND mc_channel.name LIKE CONCAT('%', #{params.name}, '%')
      </if>
    </where>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_channel
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByChannel" resultMap="ResultMapWithBLOBs" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_channel
    where channel = #{channel}
  </select>
  <select id="selectChannelCustomByChannel" resultType="com.wosai.upay.job.model.DO.McChannelCustom">
    SELECT
    <include refid="Base_Column_List_With_Table_Name" />
    ,
    <include refid="Blob_Column_List_With_Table_Name" />
    , mc_acquirer.name as 'acquirer_name'
    FROM mc_channel
    LEFT JOIN mc_acquirer ON mc_channel.acquirer = mc_acquirer.acquirer
    WHERE channel = #{channel}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from mc_channel
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.wosai.upay.job.model.DO.McChannelExample" >
    delete from mc_channel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.McChannel" >
    insert into mc_channel (id, channel, name, 
      subject, payway, payway_channel_no, 
      provider, acquirer, create_at, 
      update_at, channel_no, private_key, 
      provider_metadata, acquirer_metadata
      )
    values (#{id,jdbcType=INTEGER}, #{channel,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{subject,jdbcType=VARCHAR}, #{payway,jdbcType=INTEGER}, #{payway_channel_no,jdbcType=VARCHAR}, 
      #{provider,jdbcType=VARCHAR}, #{acquirer,jdbcType=VARCHAR}, #{create_at,jdbcType=TIMESTAMP}, 
      #{update_at,jdbcType=TIMESTAMP}, #{channel_no,jdbcType=VARCHAR}, #{private_key,jdbcType=LONGVARCHAR}, 
      #{provider_metadata,jdbcType=LONGVARCHAR}, #{acquirer_metadata,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.McChannel" >
    insert into mc_channel
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="channel != null" >
        channel,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="subject != null" >
        subject,
      </if>
      <if test="payway != null" >
        payway,
      </if>
      <if test="payway_channel_no != null" >
        payway_channel_no,
      </if>
      <if test="provider != null" >
        provider,
      </if>
      <if test="acquirer != null" >
        acquirer,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="channel_no != null" >
        channel_no,
      </if>
      <if test="private_key != null" >
        private_key,
      </if>
      <if test="provider_metadata != null" >
        provider_metadata,
      </if>
      <if test="acquirer_metadata != null" >
        acquirer_metadata,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="channel != null" >
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="subject != null" >
        #{subject,jdbcType=VARCHAR},
      </if>
      <if test="payway != null" >
        #{payway,jdbcType=INTEGER},
      </if>
      <if test="payway_channel_no != null" >
        #{payway_channel_no,jdbcType=VARCHAR},
      </if>
      <if test="provider != null" >
        #{provider,jdbcType=VARCHAR},
      </if>
      <if test="acquirer != null" >
        #{acquirer,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="channel_no != null" >
        #{channel_no,jdbcType=VARCHAR},
      </if>
      <if test="private_key != null" >
        #{private_key,jdbcType=LONGVARCHAR},
      </if>
      <if test="provider_metadata != null" >
        #{provider_metadata,jdbcType=LONGVARCHAR},
      </if>
      <if test="acquirer_metadata != null" >
        #{acquirer_metadata,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.job.model.DO.McChannelExample" resultType="java.lang.Integer" >
    select count(*) from mc_channel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update mc_channel
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.channel != null" >
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null" >
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.subject != null" >
        subject = #{record.subject,jdbcType=VARCHAR},
      </if>
      <if test="record.payway != null" >
        payway = #{record.payway,jdbcType=INTEGER},
      </if>
      <if test="record.payway_channel_no != null" >
        payway_channel_no = #{record.payway_channel_no,jdbcType=VARCHAR},
      </if>
      <if test="record.provider != null" >
        provider = #{record.provider,jdbcType=VARCHAR},
      </if>
      <if test="record.acquirer != null" >
        acquirer = #{record.acquirer,jdbcType=VARCHAR},
      </if>
      <if test="record.create_at != null" >
        create_at = #{record.create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.update_at != null" >
        update_at = #{record.update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.channel_no != null" >
        channel_no = #{record.channel_no,jdbcType=VARCHAR},
      </if>
      <if test="record.private_key != null" >
        private_key = #{record.private_key,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.provider_metadata != null" >
        provider_metadata = #{record.provider_metadata,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.acquirer_metadata != null" >
        acquirer_metadata = #{record.acquirer_metadata,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update mc_channel
    set id = #{record.id,jdbcType=INTEGER},
      channel = #{record.channel,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      subject = #{record.subject,jdbcType=VARCHAR},
      payway = #{record.payway,jdbcType=INTEGER},
      payway_channel_no = #{record.payway_channel_no,jdbcType=VARCHAR},
      provider = #{record.provider,jdbcType=VARCHAR},
      acquirer = #{record.acquirer,jdbcType=VARCHAR},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
      channel_no = #{record.channel_no,jdbcType=VARCHAR},
      private_key = #{record.private_key,jdbcType=LONGVARCHAR},
      provider_metadata = #{record.provider_metadata,jdbcType=LONGVARCHAR},
      acquirer_metadata = #{record.acquirer_metadata,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update mc_channel
    set id = #{record.id,jdbcType=INTEGER},
      channel = #{record.channel,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      subject = #{record.subject,jdbcType=VARCHAR},
      payway = #{record.payway,jdbcType=INTEGER},
      payway_channel_no = #{record.payway_channel_no,jdbcType=VARCHAR},
      provider = #{record.provider,jdbcType=VARCHAR},
      acquirer = #{record.acquirer,jdbcType=VARCHAR},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
      channel_no = #{record.channel_no,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.McChannel" >
    update mc_channel
    <set >
      <if test="channel != null" >
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="subject != null" >
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="payway != null" >
        payway = #{payway,jdbcType=INTEGER},
      </if>
      <if test="payway_channel_no != null" >
        payway_channel_no = #{payway_channel_no,jdbcType=VARCHAR},
      </if>
      <if test="provider != null" >
        provider = #{provider,jdbcType=VARCHAR},
      </if>
      <if test="acquirer != null" >
        acquirer = #{acquirer,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="channel_no != null" >
        channel_no = #{channel_no,jdbcType=VARCHAR},
      </if>
      <if test="private_key != null" >
        private_key = #{private_key,jdbcType=LONGVARCHAR},
      </if>
      <if test="provider_metadata != null" >
        provider_metadata = #{provider_metadata,jdbcType=LONGVARCHAR},
      </if>
      <if test="acquirer_metadata != null" >
        acquirer_metadata = #{acquirer_metadata,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByChannelSelective" parameterType="com.wosai.upay.job.model.DO.McChannel" >
    update mc_channel
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="subject != null" >
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="payway != null" >
        payway = #{payway,jdbcType=INTEGER},
      </if>
      <if test="payway_channel_no != null" >
        payway_channel_no = #{payway_channel_no,jdbcType=VARCHAR},
      </if>
      <if test="provider != null" >
        provider = #{provider,jdbcType=VARCHAR},
      </if>
      <if test="acquirer != null" >
        acquirer = #{acquirer,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="channel_no != null" >
        channel_no = #{channel_no,jdbcType=VARCHAR},
      </if>
      <if test="private_key != null" >
        private_key = #{private_key,jdbcType=LONGVARCHAR},
      </if>
      <if test="provider_metadata != null" >
        provider_metadata = #{provider_metadata,jdbcType=LONGVARCHAR},
      </if>
      <if test="acquirer_metadata != null" >
        acquirer_metadata = #{acquirer_metadata,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where channel = #{channel,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.McChannel" >
    update mc_channel
    set channel = #{channel,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      subject = #{subject,jdbcType=VARCHAR},
      payway = #{payway,jdbcType=INTEGER},
      payway_channel_no = #{payway_channel_no,jdbcType=VARCHAR},
      provider = #{provider,jdbcType=VARCHAR},
      acquirer = #{acquirer,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      channel_no = #{channel_no,jdbcType=VARCHAR},
      private_key = #{private_key,jdbcType=LONGVARCHAR},
      provider_metadata = #{provider_metadata,jdbcType=LONGVARCHAR},
      acquirer_metadata = #{acquirer_metadata,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.McChannel" >
    update mc_channel
    set channel = #{channel,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      subject = #{subject,jdbcType=VARCHAR},
      payway = #{payway,jdbcType=INTEGER},
      payway_channel_no = #{payway_channel_no,jdbcType=VARCHAR},
      provider = #{provider,jdbcType=VARCHAR},
      acquirer = #{acquirer,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      channel_no = #{channel_no,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>