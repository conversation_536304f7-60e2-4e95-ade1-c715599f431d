<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ZftMerchantApplyMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.payLater.ZftMerchantApply" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="smid" property="smid" jdbcType="VARCHAR" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.payLater.ZftMerchantApply" extends="BaseResultMap" >
    <result column="result" property="result" jdbcType="LONGVARCHAR" />
    <result column="form_body" property="form_body" jdbcType="LONGVARCHAR" />
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, status, account, smid, create_at, update_at
  </sql>
  <sql id="Blob_Column_List" >
    result, form_body, extra
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from zft_merchant_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from zft_merchant_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.payLater.ZftMerchantApply" useGeneratedKeys="true" keyProperty="id">
    insert into zft_merchant_apply (id, merchant_sn, status, 
      account, smid, create_at, 
      update_at, result, form_body, 
      extra)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{account,jdbcType=VARCHAR}, #{smid,jdbcType=VARCHAR}, #{create_at,jdbcType=TIMESTAMP}, 
      #{update_at,jdbcType=TIMESTAMP}, #{result,jdbcType=LONGVARCHAR}, #{form_body,jdbcType=LONGVARCHAR}, 
      #{extra,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.payLater.ZftMerchantApply" >
    insert into zft_merchant_apply
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="account != null" >
        account,
      </if>
      <if test="smid != null" >
        smid,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="form_body != null" >
        form_body,
      </if>
      <if test="extra != null" >
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="account != null" >
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="smid != null" >
        #{smid,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="result != null" >
        #{result,jdbcType=LONGVARCHAR},
      </if>
      <if test="form_body != null" >
        #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.payLater.ZftMerchantApply" >
    update zft_merchant_apply
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="smid != null" >
        smid = #{smid,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=LONGVARCHAR},
      </if>
      <if test="form_body != null" >
        form_body = #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.payLater.ZftMerchantApply" >
    update zft_merchant_apply
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      account = #{account,jdbcType=VARCHAR},
      smid = #{smid,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      result = #{result,jdbcType=LONGVARCHAR},
      form_body = #{form_body,jdbcType=LONGVARCHAR},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.payLater.ZftMerchantApply" >
    update zft_merchant_apply
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      account = #{account,jdbcType=VARCHAR},
      smid = #{smid,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByStatus" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from zft_merchant_apply
    where update_at &gt;= #{startTime}
    and update_at &lt;=#{endTime}
    and status in
    <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by update_at desc LIMIT #{limit}
  </select>
</mapper>