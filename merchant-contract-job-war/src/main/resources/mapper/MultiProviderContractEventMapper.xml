<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.MultiProviderContractEventMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.MultiProviderContractEvent" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="primary_task_id" property="primary_task_id" jdbcType="BIGINT" />
    <result column="secondary_task_id" property="secondary_task_id" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="version" property="version" jdbcType="BIGINT" />
    <result column="primary_group_id" property="primary_group_id" jdbcType="VARCHAR" />
    <result column="secondary_group_id" property="secondary_group_id" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.MultiProviderContractEvent" extends="BaseResultMap" >
    <result column="event_msg" property="event_msg" jdbcType="LONGVARCHAR" />
    <result column="result" property="result" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, primary_task_id, secondary_task_id, status, create_at, update_at, 
    version, primary_group_id, secondary_group_id
  </sql>
  <sql id="Blob_Column_List" >
    event_msg, result
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from multi_provider_contract_event
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from multi_provider_contract_event
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.MultiProviderContractEvent" >
    insert into multi_provider_contract_event (id, merchant_sn, primary_task_id, 
      secondary_task_id, status, create_at, 
      update_at, version, primary_group_id, 
      secondary_group_id, event_msg, result
      )
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{primary_task_id,jdbcType=BIGINT}, 
      #{secondary_task_id,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{create_at,jdbcType=TIMESTAMP}, 
      #{update_at,jdbcType=TIMESTAMP}, #{version,jdbcType=BIGINT}, #{primary_group_id,jdbcType=VARCHAR}, 
      #{secondary_group_id,jdbcType=VARCHAR}, #{event_msg,jdbcType=LONGVARCHAR}, #{result,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.MultiProviderContractEvent"  useGeneratedKeys="true" keyProperty="id">
    insert into multi_provider_contract_event
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="primary_task_id != null" >
        primary_task_id,
      </if>
      <if test="secondary_task_id != null" >
        secondary_task_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="primary_group_id != null" >
        primary_group_id,
      </if>
      <if test="secondary_group_id != null" >
        secondary_group_id,
      </if>
      <if test="event_msg != null" >
        event_msg,
      </if>
      <if test="result != null" >
        result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="primary_task_id != null" >
        #{primary_task_id,jdbcType=BIGINT},
      </if>
      <if test="secondary_task_id != null" >
        #{secondary_task_id,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        #{version,jdbcType=BIGINT},
      </if>
      <if test="primary_group_id != null" >
        #{primary_group_id,jdbcType=VARCHAR},
      </if>
      <if test="secondary_group_id != null" >
        #{secondary_group_id,jdbcType=VARCHAR},
      </if>
      <if test="event_msg != null" >
        #{event_msg,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        #{result,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.MultiProviderContractEvent" >
    update multi_provider_contract_event
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="primary_task_id != null" >
        primary_task_id = #{primary_task_id,jdbcType=BIGINT},
      </if>
      <if test="secondary_task_id != null" >
        secondary_task_id = #{secondary_task_id,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="primary_group_id != null" >
        primary_group_id = #{primary_group_id,jdbcType=VARCHAR},
      </if>
      <if test="secondary_group_id != null" >
        secondary_group_id = #{secondary_group_id,jdbcType=VARCHAR},
      </if>
      <if test="event_msg != null" >
        event_msg = #{event_msg,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.MultiProviderContractEvent" >
    update multi_provider_contract_event
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      primary_task_id = #{primary_task_id,jdbcType=BIGINT},
      secondary_task_id = #{secondary_task_id,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT},
      primary_group_id = #{primary_group_id,jdbcType=VARCHAR},
      secondary_group_id = #{secondary_group_id,jdbcType=VARCHAR},
      event_msg = #{event_msg,jdbcType=LONGVARCHAR},
      result = #{result,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.MultiProviderContractEvent" >
    update multi_provider_contract_event
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      primary_task_id = #{primary_task_id,jdbcType=BIGINT},
      secondary_task_id = #{secondary_task_id,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT},
      primary_group_id = #{primary_group_id,jdbcType=VARCHAR},
      secondary_group_id = #{secondary_group_id,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectPendingNetInMultiEvents" resultType="com.wosai.upay.job.model.DO.MultiProviderContractEvent">
    select * from multi_provider_contract_event
    where
      status=0
      and update_at > #{param1,jdbcType=TIMESTAMP}
    LIMIT ${param2}
  </select>
    <select id="selectNotFinishedEventsByMerchantSn"
            resultType="com.wosai.upay.job.model.DO.MultiProviderContractEvent">
      select * from multi_provider_contract_event
      where
        merchant_sn = #{merchantSn}
        and status = 1
    </select>
  <select id="selectProcessingNetInMultiEvents"
          resultType="com.wosai.upay.job.model.DO.MultiProviderContractEvent">
    select * from multi_provider_contract_event
    where
      status=1
      and update_at > #{param1,jdbcType=TIMESTAMP}
    LIMIT ${param2}
  </select>
  <select id="selectMultiEventByMerchantSnAndTaskId" resultType="com.wosai.upay.job.model.DO.MultiProviderContractEvent">
    select * from multi_provider_contract_event
    where
      merchant_sn = #{param1}
      and (primary_task_id = #{param2}
      or secondary_task_id = #{param2})
    LIMIT 1
  </select>
    <select id="selectLatestMultiEventByMerchantSn"
            resultType="com.wosai.upay.job.model.DO.MultiProviderContractEvent">
      select * from multi_provider_contract_event
      where
        merchant_sn = #{param1}
        order by create_at desc
      LIMIT 1
    </select>
  <select id="selectMultiEventTodoByMerchantSn"
          resultType="com.wosai.upay.job.model.DO.MultiProviderContractEvent">
    select * from multi_provider_contract_event
    where merchant_sn=#{param1}
      and status in (0, 1) limit 1
  </select>
  <select id="selectSuccessEvents" resultType="com.wosai.upay.job.model.DO.MultiProviderContractEvent">
    select * from multi_provider_contract_event
    where
      update_at > #{param1,jdbcType=TIMESTAMP}
      and status = 5
      LIMIT ${param2}
  </select>
</mapper>