<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.CcbConfigChangeHistoryMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.ccbConfig.CcbConfigChangeHistory" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="ccb_config_id" property="ccb_config_id" jdbcType="BIGINT" />
    <result column="op_type" property="op_type" jdbcType="INTEGER" />
    <result column="private_min_price" property="private_min_price" jdbcType="VARCHAR" />
    <result column="public_min_price" property="public_min_price" jdbcType="VARCHAR" />
    <result column="support_select_ins_no" property="support_select_ins_no" jdbcType="BIT"/>
    <result column="ins_no" property="ins_no" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="delay_day" property="delay_day" jdbcType="INTEGER" />
    <result column="is_auto_change" property="is_auto_change" jdbcType="BIT" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="update_time" property="update_time" jdbcType="BIGINT" />
    <result column="micro_info" property="micro_info" jdbcType="BIT" />
    <result column="black_mcc" property="black_mcc" jdbcType="VARCHAR" />
    <result column="apply_unionpay" property="apply_unionpay" jdbcType="BIT" />
    <result column="apply_decp" property="apply_decp" jdbcType="BIT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.ccbConfig.CcbConfigChangeHistory" extends="BaseResultMap" >
    <result column="filter_rules" property="filter_rules" jdbcType="LONGVARCHAR" />
    <result column="ins_no_list" property="ins_no_list" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, ccb_config_id, op_type, private_min_price, public_min_price, support_select_ins_no, ins_no, account,
    delay_day, is_auto_change, operator, update_time, micro_info, black_mcc, apply_unionpay, apply_decp
  </sql>
  <sql id="Blob_Column_List" >
    filter_rules, ins_no_list
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ccb_config_change_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ccb_config_change_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfigChangeHistory" >
    insert into ccb_config_change_history (id, ccb_config_id, op_type, 
      private_min_price, public_min_price, support_select_ins_no, ins_no, ins_no_list,
      account, delay_day, is_auto_change, 
      operator, update_time, micro_info, 
      black_mcc, filter_rules, apply_unionpay, apply_decp)
    values (#{id,jdbcType=BIGINT}, #{ccb_config_id,jdbcType=BIGINT}, #{op_type,jdbcType=INTEGER}, 
      #{private_min_price,jdbcType=VARCHAR}, #{public_min_price,jdbcType=VARCHAR}, #{support_select_ins_no,jdbcType=BIT}, #{ins_no,jdbcType=VARCHAR},  #{ins_no_list,jdbcType=LONGVARCHAR},
      #{account,jdbcType=VARCHAR}, #{delay_day,jdbcType=INTEGER}, #{is_auto_change,jdbcType=BIT}, 
      #{operator,jdbcType=VARCHAR}, #{update_time,jdbcType=BIGINT}, #{micro_info,jdbcType=BIT}, 
      #{black_mcc,jdbcType=VARCHAR}, #{filter_rules,jdbcType=LONGVARCHAR},
      #{apply_unionpay,jdbcType=BIT}, #{apply_decp,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfigChangeHistory" >
    insert into ccb_config_change_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="ccb_config_id != null" >
        ccb_config_id,
      </if>
      <if test="op_type != null" >
        op_type,
      </if>
      <if test="private_min_price != null" >
        private_min_price,
      </if>
      <if test="public_min_price != null" >
        public_min_price,
      </if>
      <if test="support_select_ins_no != null" >
        support_select_ins_no,
      </if>
      <if test="ins_no != null" >
        ins_no,
      </if>
      <if test="ins_no_list != null" >
        ins_no_list,
      </if>
      <if test="account != null" >
        account,
      </if>
      <if test="delay_day != null" >
        delay_day,
      </if>
      <if test="is_auto_change != null" >
        is_auto_change,
      </if>
      <if test="operator != null" >
        operator,
      </if>
      <if test="update_time != null" >
        update_time,
      </if>
      <if test="micro_info != null" >
        micro_info,
      </if>
      <if test="black_mcc != null" >
        black_mcc,
      </if>
      <if test="filter_rules != null" >
        filter_rules,
      </if>
      <if test="apply_unionpay != null" >
        apply_unionpay,
      </if>
      <if test="apply_decp != null" >
        apply_decp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ccb_config_id != null" >
        #{ccb_config_id,jdbcType=BIGINT},
      </if>
      <if test="op_type != null" >
        #{op_type,jdbcType=INTEGER},
      </if>
      <if test="private_min_price != null" >
        #{private_min_price,jdbcType=VARCHAR},
      </if>
      <if test="public_min_price != null" >
        #{public_min_price,jdbcType=VARCHAR},
      </if>
      <if test="support_select_ins_no != null" >
        #{support_select_ins_no,jdbcType=BIT},
      </if>
      <if test="ins_no != null" >
        #{ins_no,jdbcType=VARCHAR},
      </if>
      <if test="ins_no_list != null" >
        #{ins_no_list,jdbcType=LONGVARCHAR},
      </if>
      <if test="account != null" >
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="delay_day != null" >
        #{delay_day,jdbcType=INTEGER},
      </if>
      <if test="is_auto_change != null" >
        #{is_auto_change,jdbcType=BIT},
      </if>
      <if test="operator != null" >
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="update_time != null" >
        #{update_time,jdbcType=BIGINT},
      </if>
      <if test="micro_info != null" >
        #{micro_info,jdbcType=BIT},
      </if>
      <if test="black_mcc != null" >
        #{black_mcc,jdbcType=VARCHAR},
      </if>
      <if test="filter_rules != null" >
        #{filter_rules,jdbcType=LONGVARCHAR},
      </if>
      <if test="apply_unionpay != null" >
        #{apply_unionpay,jdbcType=BIT},
      </if>
      <if test="apply_decp != null" >
        #{apply_decp,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfigChangeHistory" >
    update ccb_config_change_history
    <set >
      <if test="ccb_config_id != null" >
        ccb_config_id = #{ccb_config_id,jdbcType=BIGINT},
      </if>
      <if test="op_type != null" >
        op_type = #{op_type,jdbcType=INTEGER},
      </if>
      <if test="private_min_price != null" >
        private_min_price = #{private_min_price,jdbcType=VARCHAR},
      </if>
      <if test="public_min_price != null" >
        public_min_price = #{public_min_price,jdbcType=VARCHAR},
      </if>
      <if test="support_select_ins_no != null" >
        support_select_ins_no = #{support_select_ins_no,jdbcType=BIT},
      </if>
      <if test="ins_no != null" >
        ins_no = #{ins_no,jdbcType=VARCHAR},
      </if>
      <if test="ins_no_list != null" >
        ins_no_list = #{ins_no_list,jdbcType=LONGVARCHAR},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="delay_day != null" >
        delay_day = #{delay_day,jdbcType=INTEGER},
      </if>
      <if test="is_auto_change != null" >
        is_auto_change = #{is_auto_change,jdbcType=BIT},
      </if>
      <if test="operator != null" >
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="update_time != null" >
        update_time = #{update_time,jdbcType=BIGINT},
      </if>
      <if test="micro_info != null" >
        micro_info = #{micro_info,jdbcType=BIT},
      </if>
      <if test="black_mcc != null" >
        black_mcc = #{black_mcc,jdbcType=VARCHAR},
      </if>
      <if test="filter_rules != null" >
        filter_rules = #{filter_rules,jdbcType=LONGVARCHAR},
      </if>
      <if test="apply_unionpay != null" >
        apply_unionpay = #{apply_unionpay,jdbcType=BIT},
      </if>
      <if test="apply_decp != null" >
        apply_decp = #{apply_decp,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfigChangeHistory" >
    update ccb_config_change_history
    set ccb_config_id = #{ccb_config_id,jdbcType=BIGINT},
      op_type = #{op_type,jdbcType=INTEGER},
      private_min_price = #{private_min_price,jdbcType=VARCHAR},
      public_min_price = #{public_min_price,jdbcType=VARCHAR},
      support_select_ins_no = #{support_select_ins_no,jdbcType=BIT},
      ins_no = #{ins_no,jdbcType=VARCHAR},
      ins_no_list = #{ins_no_list,jdbcType=LONGVARCHAR},
      account = #{account,jdbcType=VARCHAR},
      delay_day = #{delay_day,jdbcType=INTEGER},
      is_auto_change = #{is_auto_change,jdbcType=BIT},
      operator = #{operator,jdbcType=VARCHAR},
      update_time = #{update_time,jdbcType=BIGINT},
      micro_info = #{micro_info,jdbcType=BIT},
      black_mcc = #{black_mcc,jdbcType=VARCHAR},
      filter_rules = #{filter_rules,jdbcType=LONGVARCHAR},
      apply_unionpay = #{apply_unionpay,jdbcType=BIT},
      apply_decp = #{apply_decp,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.ccbConfig.CcbConfigChangeHistory" >
    update ccb_config_change_history
    set ccb_config_id = #{ccb_config_id,jdbcType=BIGINT},
      op_type = #{op_type,jdbcType=INTEGER},
      private_min_price = #{private_min_price,jdbcType=VARCHAR},
      public_min_price = #{public_min_price,jdbcType=VARCHAR},
      support_select_ins_no = #{support_select_ins_no,jdbcType=BIT},
      ins_no = #{ins_no,jdbcType=VARCHAR},
      account = #{account,jdbcType=VARCHAR},
      delay_day = #{delay_day,jdbcType=INTEGER},
      is_auto_change = #{is_auto_change,jdbcType=BIT},
      operator = #{operator,jdbcType=VARCHAR},
      update_time = #{update_time,jdbcType=BIGINT},
      micro_info = #{micro_info,jdbcType=BIT},
      black_mcc = #{black_mcc,jdbcType=VARCHAR},
      apply_unionpay = #{apply_unionpay,jdbcType=BIT},
      apply_decp = #{apply_decp,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByCcbConfigId" resultType="com.wosai.upay.job.model.ccbConfig.CcbConfigChangeHistory">
    select * from ccb_config_change_history where ccb_config_id = #{ccbConfigId} order by id asc
  </select>
</mapper>