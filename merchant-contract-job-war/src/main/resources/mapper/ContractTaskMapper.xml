<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ContractTaskMapper">
    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.ContractTask" parameterType="java.lang.Long">
        select *
        from contract_task
        where id = #{id,jdbcType=BIGINT}
    </select>
    <insert id="insert" parameterType="com.wosai.upay.job.model.ContractTask" useGeneratedKeys="true" keyProperty="id">
        insert into contract_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="merchant_name != null">
                merchant_name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="affect_sub_task_count != null">
                affect_sub_task_count,
            </if>
            <if test="affect_status_success_task_count != null">
                affect_status_success_task_count,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="complete_at != null">
                complete_at,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="event_msg != null">
                event_msg,
            </if>
            <if test="event_context != null">
                event_context,
            </if>
            <if test="result != null">
                result,
            </if>
            <if test="rule_group_id != null">
                rule_group_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="merchant_name != null">
                #{merchant_name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="affect_sub_task_count != null">
                #{affect_sub_task_count,jdbcType=INTEGER},
            </if>
            <if test="affect_status_success_task_count != null">
                #{affect_status_success_task_count,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="complete_at != null">
                #{complete_at,jdbcType=TIMESTAMP},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="event_msg != null">
                #{event_msg,jdbcType=LONGVARCHAR},
            </if>
            <if test="event_context != null">
                #{event_context,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="rule_group_id != null">
                #{rule_group_id,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="addAffectStatusSuccessTaskCount">
        update contract_task
        set affect_status_success_task_count = affect_status_success_task_count + 1,
            status                           = if(affect_sub_task_count = affect_status_success_task_count, 5, status)
        where id = #{id};
    </insert>
    <insert id="reduceAffectSubTaskCount">
        update contract_task
        set affect_sub_task_count = affect_sub_task_count - 1,
            status                           = if(affect_sub_task_count = affect_status_success_task_count, 5, status)
        where id = #{id};
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.ContractTask">
        update contract_task
        <set>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="merchant_name != null">
                merchant_name = #{merchant_name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="affect_sub_task_count != null">
                affect_sub_task_count = #{affect_sub_task_count,jdbcType=INTEGER},
            </if>
            <if test="affect_status_success_task_count != null">
                affect_status_success_task_count = #{affect_status_success_task_count,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <!--<if test="update_at != null">-->
            <!--update_at = #{update_at,jdbcType=TIMESTAMP},-->
            <!--</if>-->
            <if test="complete_at != null">
                complete_at = #{complete_at,jdbcType=TIMESTAMP},
            </if>
            <!--<if test="priority != null">-->
            <!--priority = #{update_at,jdbcType=TIMESTAMP},-->
            <!--</if>-->
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="event_msg != null">
                event_msg = #{event_msg,jdbcType=LONGVARCHAR},
            </if>
            <if test="event_context != null">
                event_context = #{event_context,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                result = #{result,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="restartHXTask">
        update contract_task
        set status = 1, create_at = now(), priority = now()
        where id = #{id}
    </update>

    <select id="selectByParams" resultType="com.wosai.upay.job.model.ContractTask">
        select * from contract_task
        <where>
            <if test="merchant_sn!=null and merchant_sn!=''">
                merchant_sn=#{merchant_sn}
            </if>
            <if test="status != null and status !='' or status==0">
                and status=#{status,jdbcType=INTEGER}
            </if>
            <if test="type!=null and type!=''">
                AND `type` =#{type}
            </if>
            <if test="create_at_start != null">
                AND create_at &gt;= #{create_at_start}
            </if>
            <if test="create_at_end != null">
                AND create_at &lt;=#{create_at_end}
            </if>
            order by create_at desc
        </where>
    </select>

    <select id="selectListByParams" resultType="com.wosai.upay.job.model.ContractTask">
        select * from contract_task
        <where>
            <if test="merchant_sn!=null and merchant_sn!=''">
                merchant_sn=#{merchant_sn}
            </if>
            <if test="status != null and status !='' or status==0">
                and status=#{status,jdbcType=INTEGER}
            </if>
            AND `type` in
            <foreach collection="types" item="item" open="(" separator="," close=")" index="">
                #{item}
            </foreach>
            <if test="create_at_start != null">
                AND create_at &gt;= #{create_at_start}
            </if>
            <if test="create_at_end != null">
                AND create_at &lt;=#{create_at_end}
            </if>
            order by create_at desc
        </where>
    </select>

    <select id="selectProcessingMutexTasks" resultType="com.wosai.upay.job.model.ContractTask">
        select * from contract_task
        <where>
            <if test="merchant_sn!=null and merchant_sn!=''">
                merchant_sn=#{merchant_sn}
            </if>
            and status in (0,1)
            AND `type` in
            <foreach collection="types" item="item" open="(" separator="," close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>


    <select id="selectUpdateFeeRateTodo" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority > #{param1,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> current_timestamp()
          and status in (0, 1)
          and type in ('更新商户费率至拉卡拉', '更新商户费率')
        order by priority desc LIMIT ${param2}
    </select>

    <select id="selectUpdateBasicTodo" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority > #{param1,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> current_timestamp()
          and status in (0, 1)
          and type in ('更新商户基本信息至拉卡拉', '更新商户基本信息')
        order by priority desc LIMIT ${param2}
    </select>

    <select id="selectUpdateCardTodo" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority > #{param1,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> current_timestamp()
          and status in (0, 1)
          and type = '结算账户变更'
        order by priority desc LIMIT ${param2}
    </select>

    <select id="selectPicUploadTo" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority > #{param1,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> current_timestamp()
          and status in (0, 1)
          and type = '附件上传'
        order by priority desc LIMIT ${param2}
    </select>

    <select id="selectShopTermTodo" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority > #{param1,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> current_timestamp()
          and status in (0, 1)
          and type = '增网增终'
        order by priority desc LIMIT ${param2}
    </select>

    <select id="selectIndirectContractTaskTodo" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority > #{param1,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> current_timestamp()
          and status in (0, 1)
          and type = '新增商户入网'
        <if test="ruleGroupIds != null and ruleGroupIds.size() > 0">
            and rule_group_id not in
            <foreach collection="ruleGroupIds" item="ruleGroupId" open="(" separator="," close=")">
                #{ruleGroupId}
            </foreach>
        </if>
        order by priority desc LIMIT ${param2}
    </select>

    <select id="selectBankContractTaskTodo" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority > #{param1,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> current_timestamp()
          and status in (0, 1)
          and type = '新增商户入网'
        <if test="ruleGroupIds != null and ruleGroupIds.size() > 0">
            and rule_group_id in
            <foreach collection="ruleGroupIds" item="ruleGroupId" open="(" separator="," close=")">
                #{ruleGroupId}
            </foreach>
        </if>
        order by priority desc LIMIT ${param2}
    </select>

    <select id="selectTaskTodoByMerchantSn" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where merchant_sn = #{param1}
          and type not in
              ('微信商家认证', '更新商户费率', '附件上传', '增网增终', '支付宝商家认证', '支付宝直连', '微信直连线上','小微升级',
               '微信直连线下')
          and status in (0, 1) LIMIT 100
    </select>

    <select id="selectForTipsByMerchantSn" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task FORCE INDEX (idx_sn_status)
        where merchant_sn = #{param1}
          and type!='附件上传'
        and type!='微信直连线下'
        and type!='微信直连线上'
        and type!='支付宝直连' and type != '小微升级'
        order by priority desc LIMIT 1
    </select>

    <select id="selectLastByMerchantSn" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task FORCE INDEX (idx_sn_status)
        where merchant_sn = #{param1}
          and type!='附件上传'
        and type!='批量任务生成' and type != '小微升级'
        order by priority desc LIMIT 1
    </select>

    <select id="selectWarnCount" resultType="int">
        select count(1)
        from contract_task
        where create_at > #{param1,jdbcType=TIMESTAMP}
          and create_at <![CDATA[<]]> #{param2,jdbcType=TIMESTAMP}
          and status in (0, 1)
          and type = '新增商户入网'
    </select>

    <select id="selectSystemExceptionTask" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority = #{param1,jdbcType=TIMESTAMP} LIMIT ${param2}
    </select>

    <update id="updatePriority">
        update contract_task
        set priority=#{priority,jdbcType=TIMESTAMP}
        where id = #{taskId}
    </update>

    <update id="updatePriorityAndResult">
        update contract_task
        set priority=#{priority,jdbcType=TIMESTAMP},
            result=#{result,jdbcType=LONGVARCHAR}
        where id = #{taskId}
    </update>

    <select id="getUpgradeTaskByMerchangtSn" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task FORCE INDEX (idx_sn_status)
        where merchant_sn = #{merchantSn}
          and type = #{type}
        order by priority desc limit 1
    </select>


    <select id="selectReactivateContractTaskTodo" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority between #{startTime} and #{endTime}
          and priority != '2018-08-08 08:08:08'
        and status in (0,1)
        and type='新增商户入网'
        LIMIT #{queryLimit}
    </select>

    <select id="selectProcessTaskByMerchantSn" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where merchant_sn = #{merchantSn}
          and status in (0, 1)
          and type = #{type} limit 1
    </select>

    <select id="selectMerchantChangeDataTasks" resultType="com.wosai.upay.job.model.ContractTask">
        select *
        from contract_task
        where priority > #{param1,jdbcType=TIMESTAMP}
          and priority <![CDATA[<]]> current_timestamp()
          and status in (0, 1)
          and type = '商户信息变更'
        order by priority desc LIMIT ${param2}
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
        delete from contract_task
        where id = #{id,jdbcType=BIGINT}
    </delete>
</mapper>