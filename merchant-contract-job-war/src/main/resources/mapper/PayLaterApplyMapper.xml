<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.PayLaterApplyMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.payLater.PayLaterApply" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="zft_merchant_apply_id" property="zft_merchant_apply_id" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="sub_status" property="sub_status" jdbcType="INTEGER" />
    <result column="process_status" property="process_status" jdbcType="INTEGER" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.payLater.PayLaterApply" extends="BaseResultMap" >
    <result column="result" property="result" jdbcType="LONGVARCHAR" />
    <result column="form_body" property="form_body" jdbcType="LONGVARCHAR" />
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, zft_merchant_apply_id, status, sub_status, process_status, account, 
    create_at, update_at
  </sql>
  <sql id="Blob_Column_List" >
    result, form_body, extra
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pay_later_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from pay_later_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.payLater.PayLaterApply" useGeneratedKeys="true" keyProperty="id" >
    insert into pay_later_apply (id, merchant_sn, zft_merchant_apply_id, 
      status, sub_status, process_status, 
      account, create_at, update_at, 
      result, form_body, extra
      )
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{zft_merchant_apply_id,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{sub_status,jdbcType=INTEGER}, #{process_status,jdbcType=INTEGER}, 
      #{account,jdbcType=VARCHAR}, #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP}, 
      #{result,jdbcType=LONGVARCHAR}, #{form_body,jdbcType=LONGVARCHAR}, #{extra,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.payLater.PayLaterApply" >
    insert into pay_later_apply
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="zft_merchant_apply_id != null" >
        zft_merchant_apply_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="sub_status != null" >
        sub_status,
      </if>
      <if test="process_status != null" >
        process_status,
      </if>
      <if test="account != null" >
        account,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="form_body != null" >
        form_body,
      </if>
      <if test="extra != null" >
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="zft_merchant_apply_id != null" >
        #{zft_merchant_apply_id,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="sub_status != null" >
        #{sub_status,jdbcType=INTEGER},
      </if>
      <if test="process_status != null" >
        #{process_status,jdbcType=INTEGER},
      </if>
      <if test="account != null" >
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="result != null" >
        #{result,jdbcType=LONGVARCHAR},
      </if>
      <if test="form_body != null" >
        #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.payLater.PayLaterApply" >
    update pay_later_apply
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="zft_merchant_apply_id != null" >
        zft_merchant_apply_id = #{zft_merchant_apply_id,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="sub_status != null" >
        sub_status = #{sub_status,jdbcType=INTEGER},
      </if>
      <if test="process_status != null" >
        process_status = #{process_status,jdbcType=INTEGER},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=LONGVARCHAR},
      </if>
      <if test="form_body != null" >
        form_body = #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.payLater.PayLaterApply" >
    update pay_later_apply
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      zft_merchant_apply_id = #{zft_merchant_apply_id,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sub_status = #{sub_status,jdbcType=INTEGER},
      process_status = #{process_status,jdbcType=INTEGER},
      account = #{account,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      result = #{result,jdbcType=LONGVARCHAR},
      form_body = #{form_body,jdbcType=LONGVARCHAR},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.payLater.PayLaterApply" >
    update pay_later_apply
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      zft_merchant_apply_id = #{zft_merchant_apply_id,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sub_status = #{sub_status,jdbcType=INTEGER},
      process_status = #{process_status,jdbcType=INTEGER},
      account = #{account,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByProcessStatus" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from pay_later_apply
    where sub_status != 40 and update_at &gt;= #{startTime}
    and update_at &lt;=#{endTime}
    and process_status in
    <foreach item="item" index="index" collection="processStatus" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by update_at desc LIMIT #{limit}
  </select>

</mapper>