<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.PayWayConfigChangeMapper">

    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.PayWayConfigChange"
            parameterType="java.lang.Long">
    select *
    from payway_config_change
    where id = #{id,jdbcType=BIGINT}
  </select>

    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.PayWayConfigChange" useGeneratedKeys="true"
            keyProperty="id">
        insert into payway_config_change
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="payway != null">
                payway,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="body != null">
                body,
            </if>
            <if test="channel != null">
                channel,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="payway != null">
                #{payway,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="body != null">
                #{body,jdbcType=LONGVARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.PayWayConfigChange">
        update payway_config_change
        <set>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="payway != null">
                payway = #{payway,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="body != null">
                body = #{body,jdbcType=LONGVARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectByUpdateAt" resultType="com.wosai.upay.job.model.PayWayConfigChange">
        select * from payway_config_change
        where update_at between #{param1} and #{param2}
        and status=0
        order by update_at desc LIMIT ${param3}
    </select>

    <select id="selectConfigChange" resultType="com.wosai.upay.job.model.PayWayConfigChange">
        select  * from payway_config_change
        where merchant_sn=#{merchantSn}
        and channel=#{channel}
        and payway=#{payway}
        limit 1
    </select>
</mapper>