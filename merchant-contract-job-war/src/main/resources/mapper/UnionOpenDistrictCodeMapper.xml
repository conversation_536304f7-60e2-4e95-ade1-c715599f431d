<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.UnionOpenDistrictCodeMapper">
    <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.UnionOpenDistrictCode">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="province_code" property="province_code" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="city_code" property="city_code" jdbcType="VARCHAR"/>
        <result column="county" property="county" jdbcType="VARCHAR"/>
        <result column="county_code" property="county_code" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="BIGINT"/>
        <result column="mtime" property="mtime" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="BIT"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <select id="selectByExample" resultType="com.wosai.upay.job.model.DO.UnionOpenDistrictCode"
            parameterType="com.wosai.upay.job.model.DO.UnionOpenDistrictCode">
        select * from union_open_district_code
        <where>
            <if test="province != null">
                AND province like CONCAT("%",#{province},"%")
            </if>
            <if test="city != null">
                AND city like CONCAT("%",#{city},"%")
            </if>
            <if test="county != null">
                AND county like CONCAT("%",#{county},"%")
            </if>
        </where>

    </select>
</mapper>