<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ChannelActivityMapper">

    <update id="updateActivityStatusByIdAndPreStatus">
        update channel_activity set status =  #{after} where status = #{pre} and id = #{id}
    </update>


    <update id="updateActivityStatusAndOutUidByIdAndPreStatus">
        update channel_activity set status =  #{after},out_uid = #{out_uid},result='' where status = #{pre} and id = #{id}
    </update>
    <update id="updateActivityStatusAndResultByIdAndPreStatus">
        update channel_activity set status =  #{after},result = #{result} where status = #{pre} and id = #{id}
    </update>


    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.ChannelActivity"
            parameterType="java.lang.Long">
    select *
    from channel_activity
    where id = #{id,jdbcType=BIGINT}
  </select>

    <insert id="insert" parameterType="com.wosai.upay.job.model.ChannelActivity" useGeneratedKeys="true"
            keyProperty="id">
        insert into channel_activity (audit_id,apply_id,mch_id,merchant_sn,merchant_id,`type`,`status`,`form_body`)
        values (#{audit_id},#{apply_id},#{mch_id},#{merchant_sn},#{merchant_id},#{type},#{status},#{form_body});
    </insert>


    <select id="listByStatusAndCtimeLimit" resultType="com.wosai.upay.job.model.ChannelActivity">
        select * from channel_activity
        where status = #{status} and create_at &gt; #{start,jdbcType=TIMESTAMP} order by create_at desc limit ${limit}
    </select>

    <select id="listByStatusAndMtimeLimit" resultType="com.wosai.upay.job.model.ChannelActivity">
        select * from channel_activity
        where status = #{status} and update_at &gt;= #{start,jdbcType=TIMESTAMP} and update_at &lt; #{end,jdbcType=TIMESTAMP} and type = #{type}  order by create_at asc limit ${limit}
    </select>

    <select id="getLatestActivityByMchId" resultType="com.wosai.upay.job.model.ChannelActivity">
        select * from channel_activity
        where mch_id = #{mch_id} and id != #{not_id} order by create_at desc limit 1
    </select>

    <select id="getByStatusAndCtimeLimit" resultType="com.wosai.upay.job.model.ChannelActivity">
           select * from channel_activity
        where status = #{status} and create_at  <![CDATA[<]]>  #{start,jdbcType=TIMESTAMP} order by create_at desc limit ${limit}
    </select>

    <select id="getSuccessActivityByMchId" resultType="com.wosai.upay.job.model.ChannelActivity">
        select * from channel_activity
        where mch_id = #{mch_id} and `type` = #{type} and `status` in (3,4) order by create_at desc limit 1
    </select>
</mapper>