<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.OpenCcbDecpMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.OpenCcbDecp" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="identity" property="identity" jdbcType="VARCHAR" />
    <result column="number" property="number" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="ctime" property="ctime" jdbcType="BIGINT" />
    <result column="mtime" property="mtime" jdbcType="BIGINT" />
    <result column="version" property="version" jdbcType="BIGINT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.OpenCcbDecp" extends="BaseResultMap" >
    <result column="request_body" property="request_body" jdbcType="LONGVARCHAR" />
    <result column="response_body" property="response_body" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, identity, number, status, ctime, mtime, version
  </sql>
  <sql id="Blob_Column_List" >
    request_body, response_body
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from open_ccb_decp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from open_ccb_decp
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.OpenCcbDecp" >
    insert into open_ccb_decp (id, identity, number, 
      status, ctime, mtime, 
      version, request_body, response_body
      )
    values (#{id,jdbcType=BIGINT}, #{identity,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{ctime,jdbcType=BIGINT}, #{mtime,jdbcType=BIGINT}, 
      #{version,jdbcType=BIGINT}, #{request_body,jdbcType=LONGVARCHAR}, #{response_body,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.OpenCcbDecp" >
    insert into open_ccb_decp
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="identity != null" >
        identity,
      </if>
      <if test="number != null" >
        number,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="ctime != null" >
        ctime,
      </if>
      <if test="mtime != null" >
        mtime,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="request_body != null" >
        request_body,
      </if>
      <if test="response_body != null" >
        response_body,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="identity != null" >
        #{identity,jdbcType=VARCHAR},
      </if>
      <if test="number != null" >
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="ctime != null" >
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        #{mtime,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        #{version,jdbcType=BIGINT},
      </if>
      <if test="request_body != null" >
        #{request_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="response_body != null" >
        #{response_body,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.OpenCcbDecp" >
    update open_ccb_decp
    <set >
      <if test="identity != null" >
        identity = #{identity,jdbcType=VARCHAR},
      </if>
      <if test="number != null" >
        number = #{number,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        mtime = #{mtime,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="request_body != null" >
        request_body = #{request_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="response_body != null" >
        response_body = #{response_body,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.OpenCcbDecp" >
    update open_ccb_decp
    set identity = #{identity,jdbcType=VARCHAR},
      number = #{number,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      version = #{version,jdbcType=BIGINT},
      request_body = #{request_body,jdbcType=LONGVARCHAR},
      response_body = #{response_body,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.OpenCcbDecp" >
    update open_ccb_decp
    set identity = #{identity,jdbcType=VARCHAR},
      number = #{number,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>