<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ProviderTerminalTaskMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.ProviderTerminalTask" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="retry" property="retry" jdbcType="INTEGER" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="priority" property="priority" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.ProviderTerminalTask" extends="BaseResultMap" >
    <result column="context" property="context" jdbcType="LONGVARCHAR" />
    <result column="result" property="result" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, type, status, retry, create_at, update_at, priority
  </sql>
  <sql id="Blob_Column_List" >
    context, result
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from provider_terminal_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from provider_terminal_task
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.ProviderTerminalTask" >
    insert into provider_terminal_task
    <trim prefix="(" suffix=")" suffixOverrides="," >
        merchant_sn,
        type,
      <if test="status != null" >
        status,
      </if>
      <if test="retry != null" >
        retry,
      </if>
      <if test="context != null" >
        context,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="priority != null" >
        priority,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

        #{merchant_sn,jdbcType=VARCHAR},
        #{type,jdbcType=INTEGER},

      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="retry != null" >
        #{retry,jdbcType=INTEGER},
      </if>
      <if test="context != null" >
        #{context,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        #{result,jdbcType=LONGVARCHAR},
      </if>
      <if test="priority != null" >
        #{priority,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.ProviderTerminalTask" >
    update provider_terminal_task
    <set >
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="retry != null" >
        retry = #{retry,jdbcType=INTEGER},
      </if>

      <if test="priority != null" >
        priority = #{priority,jdbcType=TIMESTAMP},
      </if>
      <if test="context != null" >
        context = #{context,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByPriorityAndType" resultType="long">

    select id
    from provider_terminal_task
    where priority >= #{priority}
      and type = #{type}
      and status in (0, 1)
    order by priority limit #{limit}

  </select>

  <select id="selectMerchantSnByPriorityAndType" resultMap="BaseResultMap">

    select id
    from provider_terminal_task
    where
      type = #{type}
      and status in (0, 1)
      and priority BETWEEN #{priority}
      and #{currentTime}
    order by priority limit #{limit}

  </select>

  <select id="selectByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from provider_terminal_task
    <where>
      <trim  prefixOverrides="and" >
        <if test="providerTerminalTask.merchant_sn != null">
          and merchant_sn = #{providerTerminalTask.merchant_sn}
        </if>
        <if test="providerTerminalTask.type != null">
          and type = #{providerTerminalTask.type}
        </if>
        <if test="providerTerminalTask.status != null">
          and status = #{providerTerminalTask.status}
        </if>
      </trim>
    </where>
  </select>
</mapper>