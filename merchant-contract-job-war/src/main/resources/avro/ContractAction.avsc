{"namespace": "com.wosai.upay.job.avro", "type": "record", "name": "ContractAction", "fields": [{"name": "merchant_sn", "type": "string", "meta": "商户号"}, {"name": "merchant_id", "type": "string", "meta": "商户id"}, {"name": "ctime", "type": ["string", "null"], "meta": "进件任务创建时间"}, {"name": "mtime", "type": ["string", "null"], "meta": "进件任务最新状态变更时间"}, {"name": "type", "type": ["string", "null"], "meta": "进件任务类型（新增商户入网、微信升级认证等，剔除附件上传事件）"}, {"name": "status", "type": ["string", "null"], "meta": "进件状态（提交、失败、成功）"}, {"name": "message", "type": ["string", "null"], "meta": "上游返回的原始文案，根据不同机构返回的信息提取文案部分"}, {"name": "TrsMemo", "type": ["string", "null"], "meta": "配置的转译文案"}, {"name": "provider", "type": ["null", "string"], "default": null, "meta": "支付通道"}]}