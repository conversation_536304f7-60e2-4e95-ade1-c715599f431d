{"namespace": "com.wosai.upay.merchant.contract.avro", "type": "record", "name": "MerchantBindBank", "fields": [{"name": "merchant_id", "type": ["string", "null"]}, {"name": "merchant_sn", "type": ["string", "null"]}, {"name": "bank_type", "type": ["int", "null"]}, {"name": "bank_name", "type": ["string", "null"]}, {"name": "status", "type": ["int", "null"]}, {"name": "message", "type": ["string", "null"]}, {"name": "ctime", "type": ["long", "null"]}, {"name": "mtime", "type": ["long", "null"]}]}