package com.wosai.upay.job.biz.acquirer;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.McAcquirerChangeMapper;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

public class ChangeAcquirerTest extends BaseTest {

    @Autowired
    private ChangeToCcbBizBank changeToCcbBizBank;

    @Resource(name = "lkl-AcquirerChangeBiz")
    private ChangeToLklBiz changeToLklBiz;

    @Autowired
    private ChangeToPsbcBizBank changeToPsbcBizBank;

    @Autowired
    private McAcquirerChangeMapper mcAcquirerChangeMapper;

    @Test
    public void lklToCcb(){
        //McAcquirerChange change = new McAcquirerChange();
        //change.setMerchant_id("db304cd6-45db-4e7f-a30e-a120bbd09b35");
        //change.setMerchant_sn("**************");
        //change.setSource_acquirer("lkl");
        //change.setTarget_acquirer("ccb");
        //changeToCcbBizBank.doChangeAcquirer(change);
        //change.setSource_acquirer("ccb");
        //change.setTarget_acquirer("psbc");
        //changeToPsbcBizBank.doChangeAcquirer(change);
        //change.setSource_acquirer("psbc");
        //change.setTarget_acquirer("lkl");
        //changeToLklBiz.doChangeAcquirer(change);
    }

    @Test
    public void cancelCombo() {
//        McAcquirerChange mcAcquirerChange = mcAcquirerChangeMapper.selectByPrimaryKey(11090);
//        changeToPsbcBizBank.cancelCombo(mcAcquirerChange);
//        mcAcquirerChange = mcAcquirerChangeMapper.selectByPrimaryKey(11090);
    }

    @Test
    public void applyCombo() {
//        McAcquirerChange mcAcquirerChange = mcAcquirerChangeMapper.selectByPrimaryKey(11010);
//        changeToPsbcBizBank.applyCombo(mcAcquirerChange);
    }


}
