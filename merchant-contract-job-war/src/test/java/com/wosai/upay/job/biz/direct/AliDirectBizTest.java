package com.wosai.upay.job.biz.direct;

import com.alipay.api.request.AlipayOpenAgentOrderQueryRequest;
import com.alipay.api.response.AlipayOpenAgentOrderQueryResponse;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.biz.SensorSendBiz;
import com.wosai.upay.job.enume.AliDirectApplyStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.AliDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.IndustryCodeV2Mapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.DO.IndustryCodeV2;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.direct.AliAuthorizeCallBack;
import com.wosai.upay.job.model.direct.AliDirectReq;
import com.wosai.upay.job.model.direct.AliDirectStatusCode;
import com.wosai.upay.job.model.direct.ApplyStatusResp;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.service.AliPayDirectService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;

@Transactional
public class AliDirectBizTest extends H2DbBaseTest {

    @Autowired
    private AliDirectBiz aliDirectBiz;

    @Mock
    private AliPayDirectService aliPayDirectService;

    @Mock
    private MerchantService merchantService;

    @Mock
    private MerchantProviderParamsService merchantProviderParamsService;

    @Mock
    private TradeComboDetailService comboDetailService;

    @Autowired
    private AliDirectApplyMapper aliDirectApplyMapper;

    @Mock
    private IndustryCodeV2Mapper industryCodeV2Mapper;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    private ApplicationApolloConfig applicationApolloConfig;

    @MockBean
    private ErrorCodeManageBiz errorCodeManageBiz;

    @Value("${ali.direct}")
    private String aliDirectDevCode;

    private Map processMessage = CollectionUtil.hashMap(ProviderUtil.ALI_DIRECT,
            Arrays.asList(CollectionUtil.hashMap("error_msg", "支付宝审核中", "error_code", "1111", "crm_msg", "单元测试审核中文案"),
                    CollectionUtil.hashMap("error_msg", "待商户签约", "error_code", "2222", "crm_msg", "单元测试待商户签约文案"),
                    CollectionUtil.hashMap("error_msg", "待支付宝授权", "error_code", "3333", "crm_msg", "单元测试待支付宝授权文案")));

    private List<Map> failMemo = Arrays.asList(
            CollectionUtil.hashMap("error_msg", "支付宝返回开通失败", "error_code", "1111", "crm_msg", "单元测试支付宝审核失败文案"),
            CollectionUtil.hashMap("error_msg", "保底文案", "error_code", "2222", "crm_msg", "支付宝不让通过:#"));


    /**
     * 处于审核中的申请单
     */
    private final String processAliApplySn = "processAliApplySn";

    private final String successAliSn = "successAliSn";

    private final String processAliTaskSn = "processAliTaskSn";

    private final String aliAuditApplySn = "aliAuditApplySn";

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(aliDirectBiz, "industryCodeV2Mapper", industryCodeV2Mapper);
        ReflectionTestUtils.setField(aliDirectBiz, "merchantProviderParamsService", merchantProviderParamsService);
        ReflectionTestUtils.setField(aliDirectBiz, "aliPayDirectService", aliPayDirectService);
        ReflectionTestUtils.setField(aliDirectBiz, "merchantService", merchantService);
        ReflectionTestUtils.setField(aliDirectBiz, "comboDetailService", comboDetailService);
        ReflectionTestUtils.setField(directStatusBiz, "kafkaTemplate", Mockito.mock(KafkaTemplate.class));
        ReflectionTestUtils.setField(contractTaskBiz, "sensorSendBiz", Mockito.mock(SensorSendBiz.class));
        applicationApolloConfig = Mockito.mock(ApplicationApolloConfig.class);
        ReflectionTestUtils.setField(aliDirectBiz, "apolloParamsConfig", applicationApolloConfig);
        Mockito.doReturn(processMessage).when(applicationApolloConfig).getProcessingDirectMessage();
    }

    /**
     * 该应用不可以开通支付宝直连
     */
    @Test
    public void preCheck01() {
        thrown.expectMessage("该应用不可以开通支付宝直连");
        aliDirectBiz.preCheck("merchant_sn", "不存在的应用标识");
    }

    /**
     * 存在未结束的直连申请任务
     */
    @Test
    public void preCheck02() {
        thrown.expectMessage("存在未结束的直连申请任务");
        aliDirectBiz.preCheck(processAliTaskSn, aliDirectDevCode);
    }

    /**
     * 存在未完成的申请单
     */
    @Test
    public void preCheck03() {
        thrown.expectMessage("存在未完成的申请单");
        aliDirectBiz.preCheck(processAliApplySn, aliDirectDevCode);
    }

    /**
     * 该商户已开通成功
     */
    @Test
    public void preCheck04() {
        thrown.expectMessage("该商户已开通成功");
        aliDirectBiz.preCheck(successAliSn, aliDirectDevCode);
    }
    /**
     * 行业不支持
     */
    @Test
    public void preCheck05() {
        Mockito.doReturn(new MerchantInfo()).when(merchantService).getMerchantBySn("merchant_sn", aliDirectDevCode);
        Mockito.doReturn(new IndustryCodeV2().setWm_aly_mcc("4870")).when(industryCodeV2Mapper).getIndustryCodeV2ByIndustryId(any());
        Mockito.doReturn(Maps.newHashMap()).when(applicationApolloConfig).getAliDirectIndustry();
        thrown.expect(CommonInvalidParameterException.class);
        aliDirectBiz.preCheck("merchant_sn", aliDirectDevCode);
    }

    @Test
    public void createTaskAndApplyAndStatus() {
        AliDirectReq aliDirectReq = new AliDirectReq();
        aliDirectReq.setMerchant_sn("createTaskAndApplyAndStatus").setDev_code(aliDirectDevCode);
        aliDirectBiz.createTaskAndApplyAndStatus(aliDirectReq, Maps.newHashMap());

        ContractTask contractTask = contractTaskMapper.getBySnAndType("createTaskAndApplyAndStatus", "支付宝直连");
        assertNotNull(contractTask);
        AliDirectApply aliDirectApply = aliDirectApplyMapper.selectLatestApplyByMerchantSn("createTaskAndApplyAndStatus");
        assertNotNull(aliDirectApply);
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode("createTaskAndApplyAndStatus", aliDirectDevCode);
        assertNotNull(directStatus);
    }

    /**
     * 支付宝返回未知状态
     */
    @Test
    public void getLatestStatusFromAli01() {
        AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = new AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse>();
        AlipayOpenAgentOrderQueryResponse resp = new AlipayOpenAgentOrderQueryResponse();
        resp.setOrderStatus("NOT_EXIST");
        response.setResp(resp);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentOrderQuery(any());
        thrown.expectMessage("查询支付宝返回未知状态");
        aliDirectBiz.getLatestStatusFromAli(processAliApplySn, "crm");
    }

    /**
     * 支付宝返回审核中，状态未变
     */
    @Test
    public void getLatestStatusFromAli02() {
        AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = new AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse>();
        AlipayOpenAgentOrderQueryResponse resp = new AlipayOpenAgentOrderQueryResponse();
        resp.setOrderStatus("MERCHANT_AUDITING");
        response.setResp(resp);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentOrderQuery(any());
        ApplyStatusResp statusResp = aliDirectBiz.getLatestStatusFromAli(aliAuditApplySn, "crm");
        assertEquals(statusResp.getContract_memo(), "单元测试审核中文案");
    }

    /**
     * 支付宝返回审核中，无apollo文案,返回默认文案
     */
    @Test
    public void getLatestStatusFromAli03() {
        AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = new AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse>();
        AlipayOpenAgentOrderQueryResponse resp = new AlipayOpenAgentOrderQueryResponse();
        resp.setOrderStatus("MERCHANT_AUDITING");
        response.setResp(resp);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentOrderQuery(any());
        Mockito.doReturn(Maps.newHashMap()).when(applicationApolloConfig).getProcessingDirectMessage();
        ApplyStatusResp statusResp = aliDirectBiz.getLatestStatusFromAli(aliAuditApplySn, "crm");
        assertEquals(statusResp.getContract_memo(), AliDirectStatusCode.ALI_AUDITING.getMsg());
    }

    /**
     * 支付宝返回待签约
     */
    @Test
    public void getLatestStatusFromAli04() {
        AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = new AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse>();
        AlipayOpenAgentOrderQueryResponse resp = new AlipayOpenAgentOrderQueryResponse();
        resp.setOrderStatus("MERCHANT_CONFIRM");
        response.setResp(resp);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentOrderQuery(any());
        ApplyStatusResp statusResp = aliDirectBiz.getLatestStatusFromAli(aliAuditApplySn, "crm");
        assertEquals(statusResp.getContract_memo(), "单元测试待商户签约文案");
    }

    /**
     * 支付宝返回待授权
     */
    @Test
    public void getLatestStatusFromAli05() {
        AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = new AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse>();
        AlipayOpenAgentOrderQueryResponse resp = new AlipayOpenAgentOrderQueryResponse();
        resp.setOrderStatus("MERCHANT_CONFIRM_SUCCESS");
        response.setResp(resp);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentOrderQuery(any());
        ApplyStatusResp statusResp = aliDirectBiz.getLatestStatusFromAli(aliAuditApplySn, "crm");
        assertEquals(statusResp.getContract_memo(), "单元测试待支付宝授权文案");
    }

    /**
     * 支付宝返回开通失败，未取到apollo文案
     */
    @Test
    public void getLatestStatusFromAli06() {
        AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = new AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse>();
        AlipayOpenAgentOrderQueryResponse resp = new AlipayOpenAgentOrderQueryResponse();
        resp.setOrderStatus("MERCHANT_APPLY_ORDER_CANCELED");
        resp.setRejectReason("支付宝返回开通失败");
        response.setResp(resp);
        Mockito.doReturn(null).when(errorCodeManageBiz).getPromptMessageFromErrorCodeManager(any(),any(),any());
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentOrderQuery(any());
        ApplyStatusResp statusResp = aliDirectBiz.getLatestStatusFromAli(aliAuditApplySn, "crm");
        assertEquals(statusResp.getContract_memo(), "支付宝返回开通失败");
    }

    /**
     * 支付宝返回开通失败，返回apollo文案
     */
    @Test
    public void getLatestStatusFromAli07() {
        AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = new AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse>();
        AlipayOpenAgentOrderQueryResponse resp = new AlipayOpenAgentOrderQueryResponse();
        resp.setOrderStatus("MERCHANT_APPLY_ORDER_CANCELED");
        resp.setRejectReason("支付宝返回开通失败");
        response.setResp(resp);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentOrderQuery(any());
        ApplyStatusResp statusResp = aliDirectBiz.getLatestStatusFromAli(aliAuditApplySn, "crm");
        assertEquals(statusResp.getContract_memo(), "单元测试支付宝审核失败文案");
    }

    /**
     * 支付宝返回开通失败，返回保底文案
     */
    @Test
    public void getLatestStatusFromAli08() {
        AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse> response = new AliCommResponse<AlipayOpenAgentOrderQueryRequest, AlipayOpenAgentOrderQueryResponse>();
        AlipayOpenAgentOrderQueryResponse resp = new AlipayOpenAgentOrderQueryResponse();
        resp.setOrderStatus("MERCHANT_APPLY_ORDER_CANCELED");
        resp.setRejectReason("未知失败");
        response.setResp(resp);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentOrderQuery(any());
        ApplyStatusResp statusResp = aliDirectBiz.getLatestStatusFromAli(aliAuditApplySn, "crm");
        assertTrue(statusResp.getContract_memo().contains("支付宝不让通过"));
    }

    @Test
    public void doAfterStatusChanged() {
    }

    @Test
    public void getAliDirectContractMemo() {
        //申请任务不存在
        ApplyStatusResp statusResp = aliDirectBiz.getAliDirectContractMemo("不存在的商户", "crm");
        assertEquals(AliDirectStatusCode.NO_TASK.getMsg(), statusResp.getContract_memo());
        //待处理
        statusResp = aliDirectBiz.getAliDirectContractMemo(new ContractTask().setStatus(TaskStatus.PENDING.getVal()), "crm");
        assertEquals(AliDirectStatusCode.PENDING_TASK.getMsg(), statusResp.getContract_memo());
        //成功
        statusResp = aliDirectBiz.getAliDirectContractMemo(new ContractTask().setStatus(TaskStatus.SUCCESS.getVal()), "crm");
        assertEquals(AliDirectStatusCode.ALI_TASK_SUCCESS.getMsg(), statusResp.getContract_memo());

    }

    @Test
    public void createFailTask() {
        AliDirectReq aliDirectReq = new AliDirectReq();
        aliDirectReq.setMerchant_sn("createFailTask").setDev_code(aliDirectDevCode);
        aliDirectBiz.createFailTask(aliDirectReq, "不知道为什么失败");

        ContractTask contractTask = contractTaskMapper.getBySnAndType("createFailTask", "支付宝直连");
        assertNotNull(contractTask);
        assertEquals(contractTask.getStatus(), TaskStatus.FAIL.getVal());
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode("createFailTask", aliDirectDevCode);
        assertNotNull(directStatus);
        assertEquals(directStatus.getStatus().intValue(), DirectStatus.STATUS_BIZ_FAIL);
    }

    @Test
    public void addAliDirectConfig() {
        Mockito.doReturn(Collections.singletonList(new TradeComboDetailResult().setWapStatus(1).setB2cStatus(1).setPayway(PaywayEnum.ALIPAY.getValue())))
                .when(comboDetailService).listByComboId(any());
        Mockito.doReturn(new MerchantInfo().setId("merchant_id")).when(merchantService).getMerchantBySn(aliAuditApplySn, aliDirectDevCode);
        AliAuthorizeCallBack callBack = new AliAuthorizeCallBack();
        callBack.setUserId("user_id").setBatchNo("20880316000001");
        aliDirectBiz.addAliDirectConfig(callBack);

        ContractTask contractTask = contractTaskMapper.getBySnAndType(aliAuditApplySn, "支付宝直连");
        assertEquals(contractTask.getStatus(), TaskStatus.SUCCESS.getVal());
        AliDirectApply aliDirectApply = aliDirectApplyMapper.selectLatestApplyByMerchantSn(aliAuditApplySn);
        assertEquals(aliDirectApply.getStatus(), AliDirectApplyStatus.APPLY_ACCEPTED.getVal());
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode(aliAuditApplySn, aliDirectDevCode);
        assertEquals(directStatus.getStatus().intValue(),DirectStatus.STATUS_SUCCESS);
    }
}