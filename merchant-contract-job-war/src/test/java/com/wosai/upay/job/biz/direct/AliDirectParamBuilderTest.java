package com.wosai.upay.job.biz.direct;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.mapper.IndustryCodeV2Mapper;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.DO.IndustryCodeV2;
import com.wosai.upay.job.model.direct.AliDirectReq;
import com.wosai.upay.merchant.contract.model.directPay.AlipayOpenAgentCreateReq;
import com.wosai.upay.merchant.contract.model.directPay.AlipayOpenAgentFacetofaceSignReq;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.junit.Assert.*;

public class AliDirectParamBuilderTest {

    @InjectMocks
    private AliDirectParamBuilder builder = new AliDirectParamBuilder();

    @Test
    public void buildCreateParam() {
        AliDirectApply alidirectApply = new AliDirectApply();
        AliDirectReq req = new AliDirectReq();
        req.setMerchant_sn("merchant_sn")
                .setContact_info(new AliDirectReq.Contact_info()
                                    .setContact_email("<EMAIL>")
                                    .setContact_phone("***********")
                                    .setContact_name("测试"))
                .setApp_info(new AliDirectReq.App_info()
                                    .setAccount("<EMAIL>"));
        alidirectApply.setForm_body(JSON.toJSONString(req));
        AlipayOpenAgentCreateReq result = builder.buildCreateParam(alidirectApply);
        assertEquals(req.getContact_info().getContact_name(), result.getContactInfo().getContactName());
    }

    @Test
    public void buildFaceToFaceSignParam() {
        Map contextParam = CollectionUtil.hashMap(
                ParamContextBiz.KEY_BUSINESS_LICENCE, new MerchantBusinessLicenseInfo().setNumber("*********"),
                ParamContextBiz.KEY_PICTURES, new StotreExtInfoAndPictures().setBrandPhoto(new PhotoInfo().setUrl("www.baidu.com")).setIndoorMaterialPhoto(new PhotoInfo().setUrl("www.baidu.com")),
                ParamContextBiz.KEY_MERCHANT, new MerchantInfo().setIndustry("industry_id")
        );
        AliDirectApply alidirectApply = new AliDirectApply();
        AliDirectReq req = new AliDirectReq();
        req.setMerchant_sn("merchant_sn")
                .setContact_info(new AliDirectReq.Contact_info()
                        .setContact_email("<EMAIL>")
                        .setContact_phone("***********")
                        .setContact_name("测试"))
                .setApp_info(new AliDirectReq.App_info()
                        .setFee_rate("0.38"));
        alidirectApply.setForm_body(JSON.toJSONString(req));
        IndustryCodeV2Mapper mapper = Mockito.mock(IndustryCodeV2Mapper.class);
        ReflectionTestUtils.setField(builder, "mapper", mapper);
        Mockito.doReturn(new IndustryCodeV2().setWm_aly_mcc("1111")).when(mapper).getIndustryCodeV2ByIndustryId("industry_id");
        AlipayOpenAgentFacetofaceSignReq result = builder.buildFaceToFaceSignParam(alidirectApply, JSON.parseObject(JSON.toJSONString(contextParam), Map.class));
        assertEquals("0.38", result.getRate());
    }
}