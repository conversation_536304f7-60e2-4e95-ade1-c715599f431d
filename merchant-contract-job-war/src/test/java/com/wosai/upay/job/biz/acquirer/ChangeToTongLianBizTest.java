package com.wosai.upay.job.biz.acquirer;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class ChangeToTongLianBizTest extends BaseTest {

    @Autowired
    private ChangeToTongLianBiz biz;

    @Test
    public void sendKafkaMsg() {
//        McAcquirerChange change = new McAcquirerChange();
//        change.setMerchant_id("db304cd6-45db-4e7f-a30e-a120bbd09b35");
//        change.setMerchant_sn("21690003066865");
//        change.setSource_acquirer("lkl");
//        change.setTarget_acquirer("tonglian");
//        biz.sendKafkaMsg(change);
    }

    @Test
    public void sendNotice() {
//        biz.sendNotice("00311d8cf29a-af38-f9c4-a4d0-f8ea92a3");
    }
}