<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wosai.upay</groupId>
    <artifactId>merchant-contract-job</artifactId>
    <packaging>pom</packaging>
    <version>4.31.10</version>
    <modules>
        <module>merchant-contract-job-war</module>
        <module>merchant-contract-job-api</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
        <relativePath/><!-- lookup parent from repository -->
    </parent>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>


    <dependencies>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>profit-sharing-api</artifactId>
            <version>1.24.12</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
            <version>2.2.4-alpha</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>3.2.0</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>
</project>